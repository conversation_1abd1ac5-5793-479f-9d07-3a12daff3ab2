const { TipArticle, TipArticleType, VirtualProcessDevice, TipComment, sequelize: $seq } = require('../models')
class TipArticleService {
  static async create (req, res) {
    let data = await TipArticle.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await TipArticle.update(req.body, {
      where: {
        articleId: req.body.articleId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { articleId } = req.params
    let data = await TipArticle.destroy({
      where: {
        articleId
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  //虚拟程序访问议题列表请求
  static async list (req, res) {
    let { deviceId, page, pageSize = 10, articleId } = req.body
    if (!articleId) {
      let where = {}
      if (deviceId) {
        where = {
          deviceId: {
            $like: `%${deviceId}%`,
          }
        }
      }
      if (!page) {
        let { rows, count } = await TipArticle.findAndCountAll({
          include: [
            {
              model: TipArticleType,
              attributes: []
            },
            {
              model: VirtualProcessDevice,
              attributes: []
            },
          ],
          attributes: {
            include: [['isTop', 'processRecordId'], 'TipArticleType.articleTypeName', 'TipArticleType.articleTypeInfo', [$seq.col('VirtualProcessDevice.vpId'), 'virtualprocessId']]
          },
          where,
          distinct: true,
          raw: true
        })
        // res.sendSuccess({ code: 200, data: rows, total: count, msg: '成功' })
        res.sendSuccess({ code: 200, data: { list: rows, total: count }, msg: '成功' })
      } else {
        let { rows, count } = await TipArticle.findAndCountAll({
          include: [
            {
              model: TipArticleType,
              attributes: []
            },
            {
              model: VirtualProcessDevice,
              attributes: []
            },
          ],
          attributes: {
            include: [['isTop', 'processRecordId'], 'TipArticleType.articleTypeName', 'TipArticleType.articleTypeInfo', [$seq.col('VirtualProcessDevice.vpId'), 'virtualprocessId']]
          },
          where,
          offset: (page - 1) * pageSize,
          limit: pageSize,
          distinct: true,
          raw: true
        })
        res.sendSuccess({ code: 200, data: rows, total: count, msg: '成功' })
      }
    } else {
      let data = await TipArticle.findAll({
        where: {
          articleId
        }
      })
      res.sendSuccess({ code: 200, data, msg: '成功' })
    }
  }
  static async createType (req, res) {
    let checkDate = await TipArticleType.findOne({
      where: { articleTypeName: req.body.articleTypeName },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此名称已存在' })
    let data = await TipArticleType.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async updateType (req, res) {
    let checkDate = await TipArticleType.findOne({
      where: {
        articleTypeName: req.body.articleTypeName,
        articleTypeId: {
          $ne: req.body.articleTypeId,
        },
      },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此名称已存在' })
    let data = await TipArticleType.update(req.body, {
      where: {
        articleTypeId: req.body.articleTypeId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async deleteType (req, res) {
    const { articleTypeId } = req.params
    let data = await TipArticleType.destroy({
      where: {
        articleTypeId
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async listType (req, res) {
    let { page, pageSize, articleTypeName = '' } = req.body
    if (!page && !pageSize) {
      let data = await TipArticleType.findAll({
        where: {
          articleTypeName: {
            $like: `%${articleTypeName}%`,
          },
        }
      })
      res.sendSuccess({ code: 200, data })
    } else {
      let { rows, count } = await TipArticleType.findAndCountAll({
        where: {
          articleTypeName: {
            $like: `%${articleTypeName}%`,
          }
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
        // raw:true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    }
  }
}

module.exports = TipArticleService
