<template>
  <div class="container">
    <div class="bread">
      <span class="bread-label">分      类</span>
      <div class="category-tags">
        <span class="category-tag" :class="{ active: radio === 0 }" @click="radio = 0; getData()">
          全部
        </span>
        <span class="category-tag" :class="{ active: radio === item.value }" v-for="item in coursewareTypeSelect"
          :key="item.value" @click="radio = item.value; getData()">
          {{ item.label }}
        </span>
      </div>
    </div>

    <div class="course-grid-wrapper" :style="`height: ${windowHeight - 350}px`">
      <el-row :gutter="20" class="course-grid" v-loading="loading">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="(item, index) in tableData" :key="index">
          <el-card shadow="never" class="course-card">
            <div class="card-header">
              <span class="card-title">
                <i class="el-icon-connection"></i> {{ item.coursewareName }}
              </span>
              <el-tag v-if="item.children" type="info" size="small">
                子目录数：{{ item.children?.length || 0 }}
              </el-tag>
            </div>
            <div class="card-body" @click="handleToDetail(item.id)">
              <el-image class="card-image" :fit="'cover'"
                :src="item.coursewarePic ? $SOURCEURL + item.coursewarePic : ''">
                <div slot="error" class="image-placeholder">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div class="card-info" :title="item.info">
                {{ item.info }}
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-pagination class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      v-model:current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
      layout="total, prev, pager, next, jumper" :total="tablePage.total" />
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      radio: 0,
      windowHeight: window.innerHeight, // 用于存储窗口高度
      coursewareTypeSelect: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      loading: false,
      tableData: [],
      formSearch: {
        processTypeId: undefined,
        page: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.getData();
    this.getProcessFlow();
  },
  mounted() {
    this.updateWindowHeight();
    window.addEventListener('resize', this.updateWindowHeight);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.updateWindowHeight);
  },
  methods: {
    updateWindowHeight() {
      console.log(this.windowHeight);
      this.windowHeight = window.innerHeight; // 获取可视窗口的高度
    },
    getData() {
      this.loading = true;
      if (this.radio != 0) {
        this.formSearch.processTypeId = this.radio;
      } else {
        this.formSearch.processTypeId = undefined;
      }
      this.$http
        .coursewareLearnList(this.formSearch)
        .then((res) => {
          this.loading = false;
          this.tableData = res.data;
          this.tablePage.total = res.total;
        });
    },
    getProcessFlow() {
      this.$http.getDictList({ dictField: "processFlow" }).then((res) => {
        this.coursewareTypeSelect = res.data;
      });
    },
    handleToDetail(id) {
      if (!id) return this.$msg("id缺失，请刷新后再试", "error");
      this.$router.push({ path: "/courseLearn/detail", query: { id } });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
};
</script>

<style lang="scss" scoped>
$primary-color: #2b82ff; // 主色调，柔和蓝色
$background-color: #f7f9fc; // 页面背景
$card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); // 卡片阴影
$border-radius: 12px; // 圆角

.container {
  padding: 4px;
  background: $background-color;
  min-height: 100vh;
}

.bread {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  background: #fff;
  border-radius: $border-radius;
  box-shadow: $card-shadow;
  margin-bottom: 4px;
  max-height: 100px;
  overflow: hidden;

  .bread-label {
    text-align: center;
    writing-mode: vertical-rl; // 文字竖排，从右到左
    font-size: 16px;
    font-weight: 500;
    color: #333;
    width: 30px; // 固定宽度
    height: 100px; // 固定高度
    display: flex;
    align-items: center; // 文字垂直居中
    justify-content: center;
    background: #f0f2f5;
    margin-right: 18px;
    padding-right: 2px;
  }

  .category-tags {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    max-height: 100px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 4px 0;
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .category-tag {
      padding: 4px 8px;
      font-size: 12px;
      color: #666;
      background: #f0f2f5;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        background: lighten($primary-color, 40%);
        color: $primary-color;
        transform: scale(1.05);
      }

      &.active {
        background: $primary-color;
        color: #fff;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba($primary-color, 0.3);
      }
    }
  }
}

.course-grid-wrapper {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.course-grid {
  margin-top: 8px;

  .course-card {
    border: none;
    border-radius: $border-radius;
    background: #fff;
    box-shadow: $card-shadow;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: linear-gradient(135deg, #f8fafc, #ffffff);
      border-bottom: 1px solid #e8ecef;

      .card-title {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
        width: 95%; // 调整为95%宽度
        max-width: 95%; // 防止溢出
        height: 24px; // 固定高度
        line-height: 24px; // 文字垂直居中
        overflow: hidden; // 隐藏溢出内容
        text-overflow: ellipsis; // 长标题显示省略号
        white-space: nowrap; // 防止换行

        .el-icon-connection {
          color: $primary-color;
          font-size: 18px;
          flex-shrink: 0;
        }
      }

      .el-tag {
        background: #e6f0ff;
        color: $primary-color;
        border: none;
        font-size: 12px;
        padding: 0 10px;
        height: 24px;
        line-height: 24px;
        flex-shrink: 0; // 防止标签被压缩
      }
    }

    .card-body {
      cursor: pointer;

      // .card-image {
      //   width: 100%;
      //   height: 210px;
      //   /* 高度从 180px 减到 160px，比例更协调 */
      //   background: #f5f5f5;
      //   object-fit: cover;
      //   /* 保持图片填充，裁剪多余部分 */
      //   transform: scale(1);
      //   /* 初始轻微放大 */
      //   transition: transform 0.3s ease;
      //   /* 平滑过渡 */
      //   object-position: center;
      //   /* 图片居中显示 */

      //   &:hover {
      //     transform: scale(1.1);
      //     /* 悬停时放大到 1.1 倍 */
      //   }

      //   .image-placeholder {
      //     display: flex;
      //     justify-content: center;
      //     align-items: center;
      //     height: 100%;
      //     background: linear-gradient(135deg, #eceff1, #f5f5f5);
      //     color: #c0c4cc;
      //     font-size: 36px;
      //   }
      // }
      // .card-image {
      //   width: 100%;
      //   height: 204px;
      //   /* 高度设为 165px，折中选择 */
      //   background: #f5f5f5;
      //   object-fit: cover;
      //   transform: scale(1);
      //   /* 初始无缩放 */
      //   transition: transform 0.3s ease;
      //   object-position: center;

      //   &:hover {
      //     transform: scale(1.1);
      //     /* 悬停时放大到 1.1 倍 */
      //   }

      //   .image-placeholder {
      //     display: flex;
      //     justify-content: center;
      //     align-items: center;
      //     height: 100%;
      //     background: linear-gradient(135deg, #eceff1, #f5f5f5);
      //     color: #c0c4cc;
      //     font-size: 36px;
      //   }
      // }
      .card-image {
        width: 100%;
        height: 250px;
        background: #f5f5f5;
        object-fit: contain;
        /* 图片完整显示，可能有空白 */
        transform: scale(1);
        transition: transform 0.3s ease;
        object-position: center;

        &:hover {
          transform: scale(1.1);
        }

        .image-placeholder {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          background: linear-gradient(135deg, #eceff1, #f5f5f5);
          color: #c0c4cc;
          font-size: 36px;
        }
      }

      .card-info {
        padding: 16px;
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }
}

.pagination {
  margin-top: 24px;
  padding: 16px;
  background: #fff;
  border-radius: $border-radius;
  box-shadow: $card-shadow;
  display: flex;
  justify-content: center;

  .el-pager li {
    font-size: 14px;
    color: #666;
    border-radius: 6px;
    transition: all 0.3s ease;

    &.active {
      background: $primary-color;
      color: #fff;
    }

    &:hover {
      color: $primary-color;
      background: lighten($primary-color, 45%);
    }
  }

  .el-pagination__total,
  .el-pagination__jump {
    font-size: 14px;
    color: #666;
  }
}
</style>