// 存储用户聊天数据
const aiService = require('../utils/aiService');
const AiChatService = require('../services/AiChatService');
const db = require('../models'); // 引入数据库模型

class AiChatController {
  /**
   * 根据课程ID或课程名称获取课程信息
   * @param {String|Number} courseIdOrName 课程ID或课程名称
   * @returns {Promise<Object>} 课程信息
   */
  static async getCourseInfo(courseIdOrName) {
    try {
      // 首先尝试按ID查询
      let course = null;
      
      // 判断是否为数字ID
      if (!isNaN(courseIdOrName)) {
        course = await db.Courseware.findOne({
          where: { id: courseIdOrName }
        });
      }
      
      // 如果按ID未找到，尝试按名称查询
      if (!course) {
        course = await db.Courseware.findOne({
          where: { coursewareName: courseIdOrName }
        });
      }
      
      return course;
    } catch (error) {
      console.error('获取课程信息失败:', error);
      return null;
    }
  }

  /**
   * 获取用户剩余聊天次数
   */
  static async getRemainingChats(req, res) {
    try {
      const { userId, courseId, tempUserId } = req.query;
      
      if (!courseId) {
        return res.sendSuccess({ code: 400, msg: '缺少课程ID参数', data: 0 });
      }
      
      // 获取课程信息
      const course = await AiChatController.getCourseInfo(courseId);
      const courseIdentifier = course ? course.id : courseId;
      
      // 获取剩余次数
      const remainingChats = await AiChatService.getRemainingChats(
        userId, 
        courseIdentifier, 
        tempUserId || (userId ? null : ('temp-' + new Date().getTime()))
      );
      
      return res.sendSuccess({ 
        code: 200, 
        msg: '获取成功', 
        data: remainingChats 
      });
    } catch (error) {
      console.error('获取剩余聊天次数失败:', error);
      return res.sendSuccess({ code: 500, msg: '服务器错误', data: 0 });
    }
  }

  /**
   * 获取聊天历史记录
   */
  static async getChatHistory(req, res) {
    try {
      const { userId, courseId, tempUserId } = req.query;
      
      if (!courseId) {
        return res.sendSuccess({ code: 400, msg: '缺少课程ID参数', data: [] });
      }
      
      // 获取课程信息
      const course = await AiChatController.getCourseInfo(courseId);
      const courseIdentifier = course ? course.id : courseId;
      
      // 获取聊天历史
      const history = await AiChatService.getChatHistory(
        userId, 
        courseIdentifier, 
        tempUserId || (userId ? null : ('temp-' + new Date().getTime()))
      );
      
      return res.sendSuccess({ 
        code: 200, 
        msg: '获取成功', 
        data: history 
      });
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      return res.sendSuccess({ code: 500, msg: '服务器错误', data: [] });
    }
  }

  /**
   * 发送聊天消息
   */
  static async sendChatMessage(req, res) {
    try {
      const { userId, courseId, message, tempUserId } = req.body;
      
      if (!courseId) {
        return res.sendSuccess({ code: 400, msg: '缺少课程ID参数', data: null });
      }
      
      if (!message) {
        return res.sendSuccess({ code: 400, msg: '消息内容不能为空', data: null });
      }
      
      // 获取课程信息
      const course = await AiChatController.getCourseInfo(courseId);
      const courseIdentifier = course ? course.id : courseId;
      const courseName = course ? course.coursewareName : courseId;
      
      // 使用临时用户ID，如果userId为空
      const userTempId = tempUserId || (userId ? null : ('temp-' + new Date().getTime()));
      
      // 获取剩余次数
      const remainingChats = await AiChatService.getRemainingChats(userId, courseIdentifier, userTempId);
      
      // 检查剩余次数
      if (remainingChats <= 0) {
        return res.sendSuccess({ 
          code: 400, 
          msg: '您已用完所有AI提问次数', 
          data: null 
        });
      }
      
      // 减少剩余次数
      await AiChatService.decreaseRemainingChats(userId, courseIdentifier, userTempId);
      
      // 记录用户消息
      const currentTime = new Date();
      await AiChatService.saveMessage({
        userId,
        tempUserId: userTempId,
        courseId: courseIdentifier,
        courseName: courseName,
        content: message,
        isUser: true,
        messageTime: currentTime
      });
      
      // 使用AI服务获取回复
      let aiResponse;
      try {
        // 传递课程名称给AI服务
        aiResponse = await aiService.getAiResponse(message, courseName || courseId);
      } catch (aiError) {
        console.error('AI回复生成失败:', aiError);
        aiResponse = '抱歉，AI服务暂时无法响应，请稍后再试。';
      }
      
      // 记录AI回复
      await AiChatService.saveMessage({
        userId,
        tempUserId: userTempId,
        courseId: courseIdentifier,
        courseName: courseName,
        content: aiResponse,
        isUser: false,
        messageTime: new Date()
      });
      
      // 获取更新后的剩余次数
      const updatedRemainingChats = await AiChatService.getRemainingChats(userId, courseIdentifier, userTempId);
      
      return res.sendSuccess({ 
        code: 200, 
        msg: '发送成功', 
        data: {
          reply: aiResponse,
          remainingChats: updatedRemainingChats
        }
      });
    } catch (error) {
      console.error('发送聊天消息失败:', error);
      return res.sendSuccess({ 
        code: 500, 
        msg: error.message || '服务器错误', 
        data: null 
      });
    }
  }
  
  /**
   * 删除聊天历史
   */
  static async deleteChatHistory(req, res) {
    try {
      const { userId, courseId, tempUserId } = req.body;
      
      if (!courseId) {
        return res.sendSuccess({ code: 400, msg: '缺少课程ID参数', data: false });
      }
      
      // 获取课程信息
      const course = await AiChatController.getCourseInfo(courseId);
      const courseIdentifier = course ? course.id : courseId;
      
      // 删除聊天历史
      const result = await AiChatService.deleteChatHistory(
        userId, 
        courseIdentifier, 
        tempUserId || (userId ? null : ('temp-' + new Date().getTime()))
      );
      
      return res.sendSuccess({ 
        code: result ? 200 : 400, 
        msg: result ? '删除成功' : '删除失败', 
        data: result 
      });
    } catch (error) {
      console.error('删除聊天历史失败:', error);
      return res.sendSuccess({ code: 500, msg: '服务器错误', data: false });
    }
  }
  
  /**
   * 重置聊天次数
   */
  static async resetChatQuota(req, res) {
    try {
      const { userId, courseId, newLimit } = req.body;
      
      if (!userId || !courseId) {
        return res.sendSuccess({ code: 400, msg: '缺少必要参数', data: false });
      }
      
      // 获取课程信息
      const course = await AiChatController.getCourseInfo(courseId);
      const courseIdentifier = course ? course.id : courseId;
      
      // 重置次数
      const result = await AiChatService.resetChatQuota(
        userId, 
        courseIdentifier, 
        newLimit || null // 如果未提供newLimit，则传null使用默认配置
      );
      
      return res.sendSuccess({ 
        code: result ? 200 : 400, 
        msg: result ? '重置成功' : '重置失败', 
        data: result 
      });
    } catch (error) {
      console.error('重置聊天次数失败:', error);
      return res.sendSuccess({ code: 500, msg: '服务器错误', data: false });
    }
  }
  
  /**
   * 获取用户聊天统计信息
   */
  static async getChatStats(req, res) {
    try {
      const { userId } = req.query;
      
      if (!userId) {
        return res.sendSuccess({ code: 400, msg: '缺少用户ID参数', data: null });
      }
      
      // 获取统计信息
      const stats = await AiChatService.getChatStats(userId);
      
      return res.sendSuccess({ 
        code: 200, 
        msg: '获取成功', 
        data: stats 
      });
    } catch (error) {
      console.error('获取聊天统计信息失败:', error);
      return res.sendSuccess({ code: 500, msg: '服务器错误', data: null });
    }
  }
}

module.exports = AiChatController