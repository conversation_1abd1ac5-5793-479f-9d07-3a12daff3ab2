const { Role, sequelize: $seq, $LikeWhere, $Order } = require('../models')
const RedisService = require('./RedisService')
const fields = Object.keys(Role.tableAttributes)

class RoleService {
  static async create (req, res) {
    let parmas = req.body
    let checkDate = await Role.findOne({ where: { roleName: parmas.roleName } })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此角色已存在' })
    let data = await Role.create(parmas)
    RedisService.setRoleOption()
    RedisService.setSysRolePermission(data.roleId)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    let parmas = req.body
    let checkDate = await Role.findOne({
      where: {
        roleName: parmas.roleName, roleId: {
          $ne: parmas.roleId
        }
      }
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此角色已存在' })
    let data = await Role.update(req.body, {
      where: { roleId: parmas.roleId },
    })
    RedisService.setRoleOption()
    RedisService.setSysRolePermission(parmas.roleId)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { roleId } = req.params
    let data = await Role.destroy({
      where: {
        roleId: {
          $in: roleId.split(',')
        }
      },
    })
    RedisService.setRoleOption()
    RedisService.setSysRolePermission(roleId)
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async info (req, res) {
    const obj = req.user
    let data = await Role.findOne({
      where: { userName: obj.userName },
    })
    data.setDataValue('permissions', ['admins'])
    res.sendSuccess({ code: 200, data })
  }

  static async list (req, res) {
    let { page, pageSize } = req.body
    if (!page || !pageSize) {
      return res.sendSuccess({ code: -1, msg: "分页参数异常或缺失" })
    }
    let where = $LikeWhere(req, res, fields)
    let { count, rows } = await Role.findAndCountAll({
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: $Order(req, res, fields) || ['roleSort']
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  static async roleList (req, res) {
    let rows = await Role.findAll({
      attributes: ['roleId', 'roleName'],
      where: {
        roleName: {
          $ne: '超级管理员'
        },
        status: {
          $ne: 0
        }
      }
    })
    res.sendSuccess({ code: 200, data: rows })
  }  
}

module.exports = RoleService
