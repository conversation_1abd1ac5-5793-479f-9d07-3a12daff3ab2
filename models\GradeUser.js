'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class GradeUser extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      this.hasOne(models.User, { foreignKey: 'id',sourceKey:'userId', constraints: false })

      this.belongsTo(models.Grade, { foreignKey: 'gradeId', constraints: false })
    }
  }
  GradeUser.init(
    {
      gradeId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "届/年级id"
      },
      userId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "用户id"
      },
    },
    {
      sequelize,
      // timestamps: false,
      modelName: 'GradeUser',
      tableName: 'zk_basic_grade_user',
      comment:'年级与学员id关联表'
    }
  )
  return GradeUser
}
