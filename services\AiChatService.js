'use strict';

const { AiChatMessage, AiChatQuota, sequelize } = require('../models');
const aiService = require('../utils/aiService');
const { Op } = require('sequelize');

/**
 * AI聊天服务类
 * 用于管理AI聊天的数据库操作
 */
class AiChatService {
  /**
   * 获取用户剩余聊天次数
   * @param {Number|String} userId 用户ID 
   * @param {Number|String} courseId 课程ID
   * @param {String} tempUserId 临时用户ID
   * @returns {Promise<Number>} 剩余聊天次数
   */
  static async getRemainingChats(userId, courseId, tempUserId) {
    try {
      // 构建查询条件
      const whereCondition = { courseId };
      
      if (userId) {
        whereCondition.userId = userId;
      } else if (tempUserId) {
        whereCondition.tempUserId = tempUserId;
      } else {
        // 没有任何用户标识，返回默认值
        return aiService.getMaxQuestionsPerCourse();
      }
      
      // 查询现有记录
      let quota = await AiChatQuota.findOne({ where: whereCondition });
      
      // 如果记录不存在，创建新记录
      if (!quota) {
        const defaultLimit = aiService.getMaxQuestionsPerCourse();
        quota = await AiChatQuota.create({
          userId,
          tempUserId,
          courseId,
          remainingChats: defaultLimit,
          totalChats: defaultLimit,
          lastResetDate: new Date()
        });
      } else {
        // 检查是否需要重置每日限制
        if (quota.dailyLimit) {
          const today = new Date().toISOString().split('T')[0];
          const lastReset = new Date(quota.lastResetDate).toISOString().split('T')[0];
          
          if (today !== lastReset) {
            // 重置剩余次数
            quota.remainingChats = quota.totalChats;
            quota.lastResetDate = today;
            await quota.save();
          }
        }
      }
      
      return quota.remainingChats;
    } catch (error) {
      console.error('获取剩余聊天次数失败:', error);
      return 0;
    }
  }
  
  /**
   * 减少剩余聊天次数
   * @param {Number|String} userId 用户ID
   * @param {Number|String} courseId 课程ID
   * @param {String} tempUserId 临时用户ID
   * @returns {Promise<Number>} 更新后的剩余次数
   */
  static async decreaseRemainingChats(userId, courseId, tempUserId) {
    try {
      // 构建查询条件
      const whereCondition = { courseId };
      
      if (userId) {
        whereCondition.userId = userId;
      } else if (tempUserId) {
        whereCondition.tempUserId = tempUserId;
      } else {
        // 没有任何用户标识，无法减少次数
        return 0;
      }
      
      // 获取现有记录
      let quota = await AiChatQuota.findOne({ where: whereCondition });
      
      // 如果记录不存在，先创建
      if (!quota) {
        const defaultLimit = aiService.getMaxQuestionsPerCourse();
        quota = await AiChatQuota.create({
          userId,
          tempUserId,
          courseId,
          remainingChats: defaultLimit - 1, // 创建时就减1
          totalChats: defaultLimit,
          lastResetDate: new Date()
        });
      } else {
        // 减少剩余次数
        if (quota.remainingChats > 0) {
          quota.remainingChats -= 1;
          await quota.save();
        }
      }
      
      return quota.remainingChats;
    } catch (error) {
      console.error('减少剩余聊天次数失败:', error);
      return 0;
    }
  }
  
  /**
   * 保存聊天记录
   * @param {Object} message 消息对象
   * @returns {Promise<Object>} 保存的消息记录
   */
  static async saveMessage(message) {
    try {
      return await AiChatMessage.create(message);
    } catch (error) {
      console.error('保存聊天记录失败:', error);
      return null;
    }
  }
  
  /**
   * 获取聊天历史记录
   * @param {Number|String} userId 用户ID
   * @param {Number|String} courseId 课程ID
   * @param {String} tempUserId 临时用户ID
   * @param {Number} limit 限制条数
   * @returns {Promise<Array>} 聊天历史记录
   */
  static async getChatHistory(userId, courseId, tempUserId, limit = 100) {
    try {
      // 构建查询条件
      const whereCondition = { 
        courseId,
        deleted: false
      };
      
      if (userId) {
        whereCondition.userId = userId;
      } else if (tempUserId) {
        whereCondition.tempUserId = tempUserId;
      } else {
        // 没有任何用户标识，返回空数组
        return [];
      }
      
      // 查询历史记录
      const messages = await AiChatMessage.findAll({
        where: whereCondition,
        order: [['messageTime', 'ASC']],
        limit
      });
      
      // 格式化返回结果
      return messages.map(msg => ({
        content: msg.content,
        isUser: msg.isUser,
        time: msg.messageTime
      }));
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      return [];
    }
  }
  
  /**
   * 删除聊天历史记录
   * @param {Number|String} userId 用户ID
   * @param {Number|String} courseId 课程ID
   * @param {String} tempUserId 临时用户ID
   * @returns {Promise<Boolean>} 是否删除成功
   */
  static async deleteChatHistory(userId, courseId, tempUserId) {
    try {
      // 构建查询条件
      const whereCondition = { courseId };
      
      if (userId) {
        whereCondition.userId = userId;
      } else if (tempUserId) {
        whereCondition.tempUserId = tempUserId;
      } else {
        // 没有任何用户标识，返回失败
        return false;
      }
      
      // 逻辑删除
      await AiChatMessage.update(
        { deleted: true },
        { where: whereCondition }
      );
      
      return true;
    } catch (error) {
      console.error('删除聊天历史失败:', error);
      return false;
    }
  }
  
  /**
   * 重置用户聊天次数
   * @param {Number|String} userId 用户ID
   * @param {Number|String} courseId 课程ID
   * @param {Number} newLimit 新的限制次数，默认从配置获取
   * @returns {Promise<Boolean>} 是否重置成功
   */
  static async resetChatQuota(userId, courseId, newLimit = null) {
    try {
      if (!userId || !courseId) {
        return false;
      }
      
      // 如果没有提供新的限制次数，使用配置中的值
      const defaultLimit = newLimit || aiService.getMaxQuestionsPerCourse();
      
      // 查找并更新记录
      const [quota, created] = await AiChatQuota.findOrCreate({
        where: { userId, courseId },
        defaults: {
          remainingChats: defaultLimit,
          totalChats: defaultLimit,
          lastResetDate: new Date()
        }
      });
      
      if (!created) {
        quota.remainingChats = defaultLimit;
        quota.totalChats = defaultLimit;
        quota.lastResetDate = new Date();
        await quota.save();
      }
      
      return true;
    } catch (error) {
      console.error('重置聊天次数失败:', error);
      return false;
    }
  }
  
  /**
   * 获取用户聊天统计信息
   * @param {Number|String} userId 用户ID
   * @returns {Promise<Object>} 统计信息
   */
  static async getChatStats(userId) {
    try {
      if (!userId) {
        return null;
      }
      
      // 获取用户的所有聊天记录数量
      const totalMessages = await AiChatMessage.count({
        where: { 
          userId,
          deleted: false
        }
      });
      
      // 获取用户的提问数量
      const userQuestions = await AiChatMessage.count({
        where: { 
          userId,
          isUser: true,
          deleted: false
        }
      });
      
      // 获取用户聊天的课程数量
      const courseCount = await AiChatMessage.count({
        where: { 
          userId,
          deleted: false
        },
        distinct: true,
        col: 'courseId'
      });
      
      // 获取用户今日的聊天数量
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayMessages = await AiChatMessage.count({
        where: { 
          userId,
          deleted: false,
          messageTime: {
            [Op.gte]: today
          }
        }
      });
      
      return {
        totalMessages,
        userQuestions,
        courseCount,
        todayMessages
      };
    } catch (error) {
      console.error('获取聊天统计信息失败:', error);
      return null;
    }
  }
}

module.exports = AiChatService; 