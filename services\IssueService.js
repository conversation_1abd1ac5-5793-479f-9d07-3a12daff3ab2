const { Issue, VirtualProcess, ProcessRecord, $LikeWhere, sequelize: $seq } = require('../models')
const fields = Object.keys(Issue.tableAttributes)

class IssueService {
  static async create (req, res) {
    let data = await Issue.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    if (!req.body.id) {
      return res.sendSuccess({ code: -1, msg: '数据id缺失' })
    }
    let data = await Issue.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await Issue.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async getOne (req, res) {
    const { id } = req.params
    let data = await Issue.findByPk(id)
    res.sendSuccess({ code: 200, data, msg: '获取成功' })
  }
  static async list (req, res) {
    let { deviceId, recordId, page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    if (!deviceId) {
      let { rows, count } = await Issue.findAndCountAll({
        attributes: {
          include: [[$seq.col('VirtualProcess.vrProName'), 'processTypeName']]
        },
        include: [
          {
            model: VirtualProcess,
            // required: true,
            attributes: []
          },
        ],
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order: [['id', 'desc']],
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      if (!recordId) {
        return res.sendSuccess({ code: -1, msg: 'recordId参数缺失' })
      }
      let ids = await ProcessRecord.findAll({
        attributes: ['issueId'],
        where: {
          deviceId,
          issueId: {
            $ne: null
          },
          recordId
        },
        raw: true
      })
      ids = ids.map(item => { return item.issueId })
      let { rows, count } = await Issue.findAndCountAll({
        attributes: {
          include: [['id', 'issueId'], [$seq.col('VirtualProcess.vrProName'), 'processTypeName']]
        },
        include: [
          {
            model: VirtualProcess,
            required: true,
            attributes: []
          },
        ],
        where: {
          id: {
            $notIn: ids,
          },
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
      })

      res.sendSuccess({ code: 200, data: rows, total: count })
    }
  }
}

module.exports = IssueService
