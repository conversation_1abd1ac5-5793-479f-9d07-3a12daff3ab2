const { User, Role, UserRole, sequelize: $seq, $LikeWhere, $Order } = require('../models')
const ConfigService = require('./ConfigService')
const fields = Object.keys(User.tableAttributes)
const { md5 } = require('../utils/md5')
const { PWD_SALT } = require('../utils/constant')

class RegisterService {
  static async create (req, res) {
    let userInfo = req.body
    if (!userInfo.userName) return res.sendSuccess({ code: -1, msg: '用户名不能为空' })
    if (!userInfo.password) return res.sendSuccess({ code: -1, msg: '用户密码不能为空' })
    let config = await ConfigService.getInfo()
    if (config.isRegister != 1 || !config.registerRole) return res.sendSuccess({ code: -1, msg: '注册功能未开启或注册用户角色未配置' })
    let checkData = await User.findOne({ where: { userName: userInfo.userName } })
    if (checkData) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    userInfo.password = md5(`${userInfo.password}${PWD_SALT}`)
    userInfo.status = config.isApproval ? 0 : 1
    userInfo['isRegister'] = 1
    let data = await User.create(userInfo)
    await data.setRoleIds(config.registerRole)
    res.sendSuccess({ code: 200, data, msg: '注册成功' })
  }

  static async comRegister (req, res) {
    let userInfo = req.body
    if (!userInfo.userName) return res.sendSuccess({ code: -1, msg: '用户名不能为空' })
    if (!userInfo.password) return res.sendSuccess({ code: -1, msg: '用户密码不能为空' })
    let config = await ConfigService.getInfo()
    let checkData = await User.findOne({ where: { userName: userInfo.userName } })
    if (checkData) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    userInfo.password = md5(`${userInfo.password}${PWD_SALT}`)
    userInfo.status = 1
    userInfo['isRegister'] = 1
    let data = await User.create(userInfo)
    await data.setRoleIds(config.registerRole)
    res.sendSuccess({ code: 200, data, msg: '注册成功' })
  }


  static async update (req, res) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let parmas = req.body
    let checkDate = await User.findOne({
      where: {
        userName: parmas.userName,
        id: {
          $ne: parmas.id
        }
      }
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此用户已存在' })
    let data = await User.update(req.body, {
      where: { id: parmas.id },
    })
    await data.setRoleIds(parmas.roleIds)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async approval (req, res, next, transaction) {
    if (!req.body.id || !req.body.status) return res.sendSuccess({ code: -1, msg: 'id或状态参数缺失' })
    let data = await User.update({ status: req.body.status }, {
      where: {
        id: req.body.id,
        isRegister: 1
      }
    })
    res.sendSuccess({ code: 200, data, msg: '审批成功' })
  }

  static async approvalBatch (req, res, next, transaction) {
    if (!req.body.ids || !req.body.status) return res.sendSuccess({ code: -1, msg: 'id或状态参数缺失' })
    let ids = req.body.ids.split(',')
    let data = await User.update({ status: req.body.status }, {
      where: {
        id: {
          $in: ids
        },
        isRegister: 1
      }
    })
    res.sendSuccess({ code: 200, data, msg: '审批成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await User.destroy({
      where: {
        id: {
          $in: id.split(',')
        },
        isRegister: 1
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async info (req, res) {
    const obj = req.user
    let data = await User.findOne({
      where: { userName: obj.userName },
    })
    data.setDataValue('permissions', ['admins'])
    res.sendSuccess({ code: 200, data })
  }

  static async list (req, res) {
    let { page, pageSize } = req.query
    if (!page || !pageSize) return res.sendSuccess({ code: -1, msg: "分页参数异常或缺失" })
    let where = $LikeWhere(req, res, fields)
    where['isRegister'] = 1
    let { rows, count } = await User.findAndCountAll({
      include: [
        {
          through: { attributes: [] }, // 排除中间表
          model: Role,
          as: 'roleIds',
          attributes: [
            'roleId',
            'roleName'
          ],
        },
      ],
      attributes: {
        // include:[[$seq.fn('concat',$seq.col('roleIds.roleName')),'roleNames']],
        exclude: ['password', 'roleId'],
      },
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      distinct: true,
      // raw:true
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }
}

module.exports = RegisterService
