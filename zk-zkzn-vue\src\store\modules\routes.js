/**
 * @<NAME_EMAIL>
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { asyncRoutes, constantRoutes } from "@/router";
import { getRouterList } from "@/api/router";
import { filterAllRoutes, filterAsyncRoutes } from "@/utils/handleRoutes";

function hasComponent(menu, componentName) {
  for (let item of menu) {
    // 检查当前项的 component 是否匹配
    if (item.component === componentName) {
      return true;
    }
    // 如果还有子项，则递归查找
    if (item.children) {
      if (hasComponent(item.children, componentName)) {
        return true;
      }
    }
  }
  return false; // 如果查找完仍未找到
}

const state = {
  routes: [],
  partialRoutes: [],
};

const getters = {
  routes: (state) => state.routes,
  partialRoutes: (state) => state.partialRoutes,
};

const mutations = {
  setRoutes(state, routes) {
    state.routes = constantRoutes.concat(routes);
  },
  setAllRoutes(state, resultObject) {
    const constantRoutesFilter = constantRoutes.map((item) => {
      return item; 
    });
    state.routes = constantRoutesFilter.concat(resultObject.accessRoutes);
  },

  setPartialRoutes(state, routes) {
    state.partialRoutes = constantRoutes.concat(routes);
  },
};

const actions = {
  async setRoutes({ commit }, permissions) {
    let accessedRoutes = [];
    if (permissions.includes("admin")) {
      accessedRoutes = asyncRoutes;
    } else {
      accessedRoutes = await filterAsyncRoutes(asyncRoutes, permissions);
    }
    commit("setRoutes", accessedRoutes);
    return accessedRoutes;
  },

  async setAllRoutes({ commit, state }) {
    let { data } = await getRouterList();
    data.push({ path: "*", redirect: "/404", hidden: true });

    let result = hasComponent(
      JSON.parse(JSON.stringify(data)),
      "virtual/course"
    );
    let accessRoutes = filterAllRoutes(data);
    commit("setAllRoutes", { accessRoutes, result }); // 只传入 accessRoutes
    return accessRoutes; // 可选择将 result 返回以便于后续使用
  },

  setPartialRoutes({ commit }, accessRoutes) {
    commit("setPartialRoutes", accessRoutes);
    return accessRoutes;
  },
};

export default { state, getters, mutations, actions };
