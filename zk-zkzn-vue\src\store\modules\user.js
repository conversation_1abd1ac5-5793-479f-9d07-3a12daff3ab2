/**
 * @<NAME_EMAIL>
 * @description 登录、获取用户信息、退出登录、清除accessToken逻辑，不建议修改
 */

import Vue from "vue";
import { getInfo, login, logout } from "@/api/user";
import {
  getAccessToken,
  removeAccessToken,
  setAccessToken,
} from "@/utils/accessToken";
import {
  getRefreshToken,
  removeRefreshToken,
  setRefreshToken,
} from "@/utils/refreshToken";
import { resetRouter } from "@/router";
import { title, tokenName } from "@/config/settings";

const state = {
  accessToken: getAccessToken(),
  refreshToken: getRefreshToken(),
  userName: "",
  nickName: "",
  avatar: "",
  permissions: [],
};
const getters = {
  accessToken: (state) => state.accessToken,
  refreshToken: (state) => state.refreshToken,
  userName: (state) => state.userName,
  nickName: (state) => state.nickName,
  avatar: (state) => state.avatar,
  permissions: (state) => state.permissions,
};
const mutations = {
  setAccessToken (state, accessToken) {
    state.accessToken = accessToken;
    setAccessToken(accessToken);
  },
  setRefreshToken (state, refreshToken) {
    state.refreshToken = refreshToken;
    setRefreshToken(refreshToken);
  },
  setUserName (state, userName) {
    state.userName = userName;
  },
  setNickName (state, nickName) {
    state.nickName = nickName;
  },
  setAvatar (state, avatar) {
    state.avatar = avatar;
  },
  setPermissions (state, permissions) {
    state.permissions = permissions;
  },
};
const actions = {
  setPermissions ({ commit }, permissions) {
    commit("setPermissions", permissions);
  },
  async login ({ commit }, userInfo) {
    const { data } = await login(userInfo);
    const accessToken = data[tokenName];
    const refreshToken = data['refreshToken'];
    if (refreshToken) {
      commit("setRefreshToken", refreshToken);
    }
    if (accessToken) {
      commit("setAccessToken", accessToken);
      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? "早上好"
          : hour <= 11
            ? "上午好"
            : hour <= 13
              ? "中午好"
              : hour < 18
                ? "下午好"
                : "晚上好";
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`);
    } else {
      Vue.prototype.$baseMessage(
        `登录接口异常，未正确返回${tokenName}...`,
        "error"
      );
    }
  },
  async getInfo ({ commit, state }) {
    const { data } = await getInfo(state.accessToken);
    if (!data) {
      Vue.prototype.$baseMessage("验证失败，请重新登录...", "error");
      return false;
    }
    let { permissions, userName, avatar, nickName } = data;
    if (permissions && userName) {
      commit("setPermissions", permissions);
      commit("setUserName", userName);
      commit("setNickName", nickName);
      commit("setAvatar", avatar || require('@/assets/img/avater.jpg'));
      return permissions;
    } else {
      Vue.prototype.$baseMessage("获取用户信息接口异常", "error");
      return false;
    }
  },
  async logout ({ dispatch }) {
    await logout(state.accessToken);
    await dispatch("tagsBar/delAllRoutes", null, { root: true });
    await dispatch("resetAccessToken");
    await dispatch("resetRefreshToken");
    await resetRouter();
  },
  resetAccessToken ({ commit }) {
    commit("setPermissions", []);
    commit("setAccessToken", "");
    removeAccessToken();
  },
  resetRefreshToken ({ commit }) {
    commit("setRefreshToken", "");
    removeRefreshToken();
  },
};
export default { state, getters, mutations, actions };
