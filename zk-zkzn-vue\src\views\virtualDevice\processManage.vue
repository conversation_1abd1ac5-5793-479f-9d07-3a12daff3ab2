<template>
  <!-- <div class="app-container"> -->
  <el-row :gutter="10" class="process">
    <el-col :span="5">
      <el-card class="box-card leftBox" v-loading="loading">
        <div slot="header" class="clearfix">
          <span>工艺流程</span>
          <el-button type="success" plain icon="el-icon-sort" @click="toggleExpandAll" size="mini" class="treeBtn">
            <span v-if="!isTreeExpand">展开</span>
            <span v-else>折叠</span>
          </el-button>
        </div>
        <el-input placeholder="输入关键字进行过滤" v-model="filterText" style="margin-bottom:10px;"></el-input>
        <div class="treeBox">
          <el-tree node-key='id' :indent="0" :data="treeData" class="tree" :highlight-current="true" :default-expanded-keys="expandKeys" @node-click="treeNodeClick" :expand-on-click-node="false" :props="defaultProps" ref="tree" :filter-node-method="filterNode">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <el-tag :type="data.level==1?'default':data.level==2?'success':'warning'">{{data.level==1?'流程':data.level==2?'工艺设备':'操作'}}</el-tag><span :title="data.virtualName">{{ ' '+data.virtualName }}</span>
            </span>
          </el-tree>
        </div>
      </el-card>
    </el-col>

    <el-col :span="19">
      <slot :data="propData"></slot>
      <el-card class="box-card rightBox">
        <div slot="header" class="clearfix">
          <span>介绍:</span>
          <span class="pathInfo">{{pathInfo}}</span>
        </div>
        <div class="imgInfoBox">
          <div class="img">
            <el-image style="width: 80px; height: 80px" :src="imgUrl" :preview-src-list="srcList">
              <div slot="error" class="image-slot" style="width: 80px; height: 80px;line-height:80px;text-align:center;background-color:#f5f7fa">
                <i class="el-icon-picture-outline" style="font-size:20px;color:#909399"></i>
              </div>
            </el-image>
          </div>
          <div class="info">
            {{ introduce }}
          </div>
        </div>
      </el-card>
      <el-card class="box-card" style="margin-top: 10px;">
        <!-- <el-empty description="请先选择左侧树节点中的工艺设备或操作" v-show="selectNodeTree == false"></el-empty> -->
        <el-empty description="请先选择左侧树节点中的操作" v-show="selectNodeTree == false"></el-empty>
        <!-- 选择工艺设备后的组件 -->
        <!-- <com-device ref="comDeviceRef" v-show="selectNodeTree == 2"></com-device> -->
        <!-- 选择操作后的组件 -->
        <com-operator ref="comOperatorRef" v-show="selectNodeTree == 3"></com-operator>
      </el-card>
    </el-col>
  </el-row>
  <!-- </div> -->
</template>

<script>
import comDevice from "./comDevice";
import comOperator from "./comOperator";
export default {
  name: "ProcessManage",
  components: {
    comDevice,
    comOperator
  },
  data () {
    return {
      loading: false,
      selectNodeTree: false,
      isTreeExpand: false,
      expandKeys: [],
      tableData: [],
      surfaceData: [],
      treeData: [],
      introduce: '',
      treeDataTwo: [],
      defaultProps: {
        children: 'children',
        label: 'virtualName'
      },
      imgUrl: '',
      srcList: [],
      filterText: '',
      pathInfo: '',
      propData: {
        recordId: '',//2022.3.4 czk新增记录id字段
        virtualId: '',//2023.4.28 czk添加此字段，新版只需这一个id字段
        deviceOperationId: '',
        deviceId: '',
        virtualprocessId: ''
      },
    };
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    },
    // propData: {
    //   handler: function (val) { // 此处注意，handler函数不能为箭头函数，this会取上下文，而不是组件里的this,此外，深度监听，必须为handler函数名，否则会无效果
    //     console.log(val,'val')
    //     //可以做些相应的处理
    //   },
    //   deep: true
    // },
  },
  created () {
    this.getData();
  },
  methods: {
    getData () {
      this.$http.virtualTreeOption({}).then(res => {
        this.treeData = res.data;
        this.expandKeys = [res.data[0].id, res.data[0].children[0].id]
      });
    },
    //树的全部展开与折叠
    toggleExpandAll () {
      this.loading = true
      this.isTreeExpand = !this.isTreeExpand
      let nodes = this.$refs.tree.store.nodesMap
      setTimeout(() => {
        for (let i in nodes) {
          nodes[i].expanded = this.isTreeExpand
        }
        this.loading = false
      }, 500)
    },
    filterNode (value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    recursiveFun (arr, flag) {
      for (let i = 0; i < arr.length; i++) {
        arr[i].expanded = flag
        if (arr[i].childNodes) {
          this.recursiveFun(arr[i].childNodes, flag)
        }
      }
    },
    changeRecord (val) {
      this.propData.recordId = val
      if (this.selectNodeTree == 2) {
        this.$refs.comDeviceRef.open(this.propData)
      } else if (this.selectNodeTree == 3) {
        this.$refs.comOperatorRef.open(this.propData)
      }
    },
    treeNodeClick (data, node) {
      if (!this.propData.recordId) {
        return this.$msg('请先选择流程记录', 'warning')
      }
      this.imgUrl = data.pic ? this.$SOURCEURL + data.pic : ''
      this.srcList = data.pic ? [this.$SOURCEURL + data.pic] : []
      this.introduce = data.description;
      this.pathInfo = this.getAllParentNode(node, []).reverse().join(' > ')
      if (data.level != 2 && data.level != 3) {
        this.selectNodeTree = false;
      } else if (data.level == 2) {
        this.selectNodeTree = false;
        // this.selectNodeTree = data.level
        this.propData.virtualId = data.id
        this.propData.virtualprocessId = node.parent.key
        this.propData.deviceId = data.id
        this.propData.deviceOperationId = null
        this.$refs.comDeviceRef.open(this.propData)
      } else if (data.level == 3) {
        this.selectNodeTree = data.level
        this.propData.virtualId = data.id
        this.propData.virtualprocessId = node.parent.parent.key
        this.propData.deviceId = node.parent.key
        this.propData.deviceOperationId = data.id
        this.$refs.comOperatorRef.open(this.propData)
      }
    },
    getAllParentNode (node, arr) {

      arr.push(node.label)
      if (node.parent && node.parent.label) {
        this.getAllParentNode(node.parent, arr)
      }
      return arr
    },
    treeOne (row) {
      this.treeDataTwo = row;
    }
  }
};
</script>

<style lang="scss" scoped>
.process ::v-deep {
  .el-col {
    .leftBox {
      // height: $base-app-main-height;
      margin-bottom: 0;
      .el-card__header {
        padding: 10px;
        font-size: 16px;
        .treeBtn {
          float: right;
        }
      }
      .el-card__body {
        height: calc($base-app-main-height - 65px);
        // overflow: auto;
        .treeBox {
          height: calc(100% - 42px);
          overflow: auto;
        }
      }
    }
    .rightBox {
      .el-card__header {
        padding: 10px;
        font-size: 16px;
      }
      .pathInfo {
        margin-left: 10px;
        font-size: 14px;
        color: #459bec;
      }
      .imgInfoBox {
        display: flex;
        .img {
          width: 80px;
          height: 80px;
        }
        .info {
          padding: 0 8px;
          height: 80px;
          vertical-align: top;
          overflow-y: auto;
        }
      }
    }
  }
}
.tree ::v-deep .el-tree-node__expand-icon {
  font-size: 20px;
  color: #459bec;
}
.tree ::v-deep .el-tree-node__expand-icon.is-leaf {
  // display: none;
  color: transparent;
}
.tree ::v-deep .el-tree-node {
  position: relative;
  // padding-left: 16px;
  padding-left: 0px;
}

.tree ::v-deep .el-tree-node__children {
  padding-left: 16px;
}

.tree ::v-deep .el-tree-node:last-child:before {
  height: 26px;
  // border-color: red!important;
}

.tree ::v-deep .el-tree > .el-tree-node:before {
  border-left: none;
}

.tree-container ::v-deep .el-tree > .el-tree-node:after {
  border-top: none;
}

.tree ::v-deep .el-tree-node:before {
  content: "";
  left: -4px;
  position: absolute;
  right: auto;
  border-width: 1px;
}

.tree ::v-deep .el-tree-node:after {
  content: "";
  left: -4px;
  position: absolute;
  right: auto;
  border-width: 1px;
}

.tree ::v-deep .el-tree-node:before {
  // border-left: 2px solid #92c4f3;
  border-left: 1px solid #ccc;
  bottom: 0px;
  height: 100%;
  // left: 8px;
  left: 0px;
  top: -13px;
}
.tree ::v-deep > .el-tree-node:nth-last-child(2):before {
  height: 26px;
}
.tree ::v-deep > .el-tree-node:nth-child(1):before {
  top: 13px;
}
.tree ::v-deep .el-tree-node:after {
  // border-top: 2px solid #92c4f3;
  border-top: 1px solid #ccc;
  top: 12px;
  // left: 8px;
  left: 0px;
  width: 12px;
}
</style>
