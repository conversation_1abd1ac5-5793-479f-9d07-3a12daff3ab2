'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Config extends Model {
    static associate (models) {
    }
  }
  Config.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: "唯一id"
      },
      isRegister: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "是否开启用户注册（1.开启，2.关闭）"
      },
      isApproval: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "注册用户是否需要审批(1.是，0.否)，需要审批则注册用户状态默认为禁用"
      },
      registerRole: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "注册用户默认角色id"
      },
      webPrefixUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "web端访问地址前缀"
      },
      webUploadUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "web端上传文件目录"
      },
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'Config',
      tableName: 'sys_config',
      comment: '系统配置表'
    }
  )
  return Config
}
