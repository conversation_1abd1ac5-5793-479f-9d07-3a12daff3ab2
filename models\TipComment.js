'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class TipComment extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.belongsTo(models.TipArticle,{foreignKey:'articleId',constraints:false})
      this.belongsTo(models.User,{foreignKey:'userId',constraints:false})
    }
  }
  TipComment.init(
    {            
      commentId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "评论唯一ID"
      },
      articleId:{
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "议题id"
      },
      userId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "用户id"
      },
      pid:{
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue:null,
        comment: "用户父id",
        set(value) {
          if(value == '') {
            return null
          }
        }
      },
      commentStatus: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "评论状态(1表示验证 2表示批准 3表示拒绝)"
      },    
      hostIp: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "IP地址"
      }, 
      os:{
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "系统"
      },  
      browser:{
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue:'',
        comment: "浏览器"
      },  
      content:{
        type: DataTypes.TEXT,
        allowNull: false,
        comment: "评论内容"
      },        
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },      
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'TipComment',
      tableName: 'zk_tip_comment',
      comment:'议题评论表'
    }
  )
  return TipComment
}
