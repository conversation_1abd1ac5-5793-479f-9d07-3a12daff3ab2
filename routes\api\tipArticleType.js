var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/TipArticleService");

router.post('/list', tryAsyncErrors(Service.listType))
router.post('/add', tryAsyncErrors(Service.createType))
router.post('/edit', tryAsyncErrors(Service.updateType))
router.delete('/delete/:articleTypeId', tryAsyncErrors(Service.deleteType))

module.exports = router;
