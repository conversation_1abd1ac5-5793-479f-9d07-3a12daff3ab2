var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const { transactionTryAsyncErrors } = require("../../utils/transaction");
const Service = require("../../services/RegisterService");

router.post('/add', tryAsyncErrors(Service.create))
router.get('/list',tryAsyncErrors(Service.list))
router.post('/edit', tryAsyncErrors(Service.update))
router.post('/approval', transactionTryAsyncErrors(Service.approval))
router.post('/approvalBatch', transactionTryAsyncErrors(Service.approvalBatch))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

module.exports = router;
