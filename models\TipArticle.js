'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class TipArticle extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.belongsTo(models.TipArticleType,{foreignKey:'articleTypeId',constraints:false})
      this.belongsTo(models.VirtualProcessDevice,{sourceKey:'deviceId',foreignKey:'deviceId',constraints:false})
    }
  }
  TipArticle.init(
    {         
            
      articleId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "议题ID"
      },
      articleTypeId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "议题类型"
      },
      deviceId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备id"
      },
      articleTitle: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "议题标题"
      },    
      coverImage: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "议题封面图片"
      }, 
      isMarkdown:{
        type: DataTypes.CHAR(1),
        allowNull: true,
        comment: "是否Markdown（1代表是 2代表不是）"
      },  
      content:{
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue:'',
        comment: "内容"
      },  
      contentMd:{
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue:'',
        comment: "markdown内容"
      },  
      articleInfo:{
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue:'',
        comment: "议题简介"
      },  
      isOriginal:{
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue:'1',
        comment: "是否原创（1代表是 2代表不是）"
      },  
      isComment:{
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue:'1',
        comment: "是否开启评论"
      },  
      isTop:{
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue:'',
        comment: "是否置顶"
      },  
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },      
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      }, 
      processRecordId:{
        type: DataTypes.VIRTUAL,
        get() {
          return 1
        },
      },     
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'TipArticle',
      tableName: 'zk_tip_article',
      comment:'以前的议题表'
    }
  )
  return TipArticle
}
