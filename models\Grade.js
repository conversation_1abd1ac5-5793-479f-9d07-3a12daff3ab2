'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Grade extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsTo(models.Question, {foreignKey:'questionId',constraints:false })
      this.hasMany(models.Record, { foreignKey: 'gradeId', constraints: false })
      this.hasMany(models.Class, { foreignKey: 'gradeId', constraints: false })
      // this.hasMany(models.User, { foreignKey: 'gradeId', constraints: false })
    }
  }
  Grade.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },
      gradeName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "年级名称"
      },
      createById: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "创建者id"
      },
      createBy: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Grade',
      tableName: 'zk_basic_grade',
      comment:'届/年级表'
    }
  )
  return Grade
}
