const { where } = require('sequelize')
const { VirtualCate, VirtualData, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(VirtualCate.tableAttributes)
const { handleTree } = require('../utils/index')

class VirtualCateService {
  static async create (req, res) {
    if (!req.body.name) return res.sendSuccess({ code: -1, msg: '名称不能为空' })
    let insertObj = req.body
    let maxLevelCode = await VirtualCate.max('levelCode', { where: { pId: req.body.pId ? req.body.pId : 0 } })
    if (maxLevelCode) {
      if (req.body.pId) {
        let arr = maxLevelCode.split('_')
        let code = (Number(arr.pop()) + 1)
        insertObj['levelCode'] = arr.join('_') + '_' + code
      } else {
        insertObj['levelCode'] = Number(maxLevelCode) + 1
      }
    } else {
      if (req.body.pId) {
        let code = await VirtualCate.findOne({ where: { id: req.body.pId } })
        insertObj['levelCode'] = code.levelCode + '_1'
      } else {
        insertObj['levelCode'] = 1
      }
    }
    let data = await VirtualCate.create(insertObj)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res, next, transaction) {
    if (Array.isArray(req.body)) {
      if (req.body.length <= 0) return res.sendSuccess({ code: -1, msg: '请勿传递空数据' })
      let flag = req.body.some(item => (item.id == undefined || item.id == null || item.id == 0))
      if (flag) return res.sendSuccess({ code: -1, msg: '数据中存在id缺失的数据' })
      let data = await VirtualCate.bulkCreate(req.body, { updateOnDuplicate: ['name', 'description', 'orderNum', 'remark'] })
      return res.sendSuccess({ code: 200, data, msg: '修改成功' })
    }
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id或其它字段缺失' })
    const { id, pId, status } = req.body
    if (!id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    if (id == pId) return res.sendSuccess({ code: -1, msg: 'id和pId参数不能相同' })

    let waitData = await VirtualCate.findByPk(id)
    //判断是否修改pId，修改就查找
    if (pId != null && pId != undefined && pId != waitData.pId) {
      let maxLevelCode = await VirtualCate.max('levelCode', { where: { pId }, transaction })
      let finalCode
      if (maxLevelCode) {
        if (req.body.pId) {
          let arr = maxLevelCode.split('_')
          let code = (Number(arr.pop()) + 1)
          finalCode = arr.join('_') + '_' + code
        } else {
          finalCode = Number(maxLevelCode) + 1
        }
      } else {
        if (req.body.pId) {
          let code = await VirtualCate.findOne({ where: { id: req.body.pId } })
          finalCode = code.levelCode + '_1'
        } else {
          finalCode = 1
        }
      }
      req.body.levelCode = finalCode
      await $seq.query(`UPDATE zk_virtual_cate SET levelCode = REPLACE(levelCode,'${waitData.levelCode}','${finalCode}') WHERE levelCode LIKE '${waitData.levelCode}_%'`, { transaction })
    }

    //判断是否修改status，修改就查找其子集，并修改子集所有状态，或修改父级状态
    if (status != null && status != undefined && status != waitData.status) {
      await $seq.query(`UPDATE zk_virtual_cate SET status = ${status} WHERE levelCode LIKE '${waitData.levelCode}_%'`, { transaction })
      if (status == 1) {
        let codeArr = waitData.levelCode.split('_')
        if (codeArr.length > 1) {
          let arr = [codeArr[0]]
          for (let i = 1; i < codeArr.length - 1; i++) {
            arr.push(arr[i - 1] + '_' + codeArr[i])
          }
          await VirtualCate.update({ status }, {
            where: {
              levelCode: {
                $in: arr
              }
            },
            transaction
          })
        }
      }
    }

    let data = await VirtualCate.update(req.body, {
      where: { id },
      transaction
    })
    res.sendSuccess({ code: 200, data })
  }

  static async delete (req, res, next, transaction) {
    const { id } = req.params
    let origin = await VirtualCate.findOne({
      where: {
        id
      }
    })
    if (!origin) return res.sendSuccess({ code: -1, msg: '未查询到相关id数据，请刷新后重试' })
    //清除关联仿真数据的virtualCateId
    let idsData = await VirtualCate.findAll({
      attributes: ['id'],
      where: {
        $or: [
          {
            levelCode: {
              $like: `${origin.levelCode}_%`
            }
          },
          {
            id
          }
        ]
      },
      raw: true
    })
    let ids = idsData.map(t => t.id)
    await VirtualData.update({ virtualCateId: null }, {
      where: {
        virtualCateId: {
          $in: ids
        }
      },
      transaction
    })

    let data = await VirtualCate.destroy({
      where: {
        $or: [
          {
            levelCode: {
              $like: `${origin.levelCode}_%`
            }
          },
          {
            id
          }
        ]
      },
      transaction
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async list (req, res) {
    let where = $LikeWhere(req, res, fields)
    let data = await VirtualCate.findAll({
      where,
      order: [
        // [$seq.literal('FIELD(`VirtualData`.`sortBy`,NULL) ASC')],
        ['orderNum'], ['id']
      ],
      raw: true
    })
    res.sendSuccess({ code: 200, data })
  }

  static async treeList (req, res) {
    let result = await VirtualCate.findAll({
      // where: {
      //   status: '1'
      // },
      order: [
        ['orderNum'], ['id']
      ]
    })
    let data = handleTree(result, 'id', 'pId')
    res.sendSuccess({ code: 200, data })
  }

  static async relevance (req, res, next, transaction) {
    if (!Array.isArray(req.body) || req.body.length == 0) return res.sendSuccess({ code: -1, msg: '数据不合法' })
    let finalData = req.body
    await VirtualData.update({ virtualCateId: null }, {
      where: {
        virtualCateId: finalData[0].virtualCateId
      }
    })
    for (let i = 0; i < finalData.length; i++) {
      await VirtualData.update({ virtualCateId: finalData[i].virtualCateId }, {
        where: {
          code: finalData[i].code
        },
        transaction
      })
    }
    res.sendSuccess({ code: 200, data: true })
  }

  static async notRelevance (req, res, next) {
    if (!req.body.code) return res.sendSuccess({ code: -1, msg: '唯一参数缺失' })
    let data = await VirtualData.update({ virtualCateId: null }, {
      where: {
        code: req.body.code
      }
    })
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = VirtualCateService
