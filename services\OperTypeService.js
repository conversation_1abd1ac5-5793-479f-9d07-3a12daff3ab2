const { OperType, VirtualData, VirtualProcess, VirtualProcessDevice, VirtualProcessDeviceOper, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(OperType.tableAttributes)

class OperTypeService {
  static async create (req, res) {
    if(!req.body.operTypeName) return res.sendSuccess({code:-1,msg:'名称不能为空'})
    let data = await OperType.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    if (Array.isArray(req.body)) {
      if (req.body.length <= 0) return res.sendSuccess({ code: -1, msg: '请勿传递空数据' })
      let flag = req.body.some(item => (item.id == undefined || item.id == null || item.id == 0))
      if (flag) return res.sendSuccess({ code: -1, msg: '数据中存在id缺失的数据' })
      // req.body.forEach(item => {
      //   if (!item.sortBy) {
      //     item.sortBy = null
      //   }
      // })
      let data = await OperType.bulkCreate(req.body, { updateOnDuplicate: ['operTypeName', 'remark'] })
      return res.sendSuccess({ code: 200, data, msg: '修改成功' })
    }
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id或其它字段缺失' })
    let data = await OperType.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await OperType.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await OperType.findAndCountAll({
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }
}

module.exports = OperTypeService
