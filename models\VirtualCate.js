'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class VirtualCate extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.hasMany(models.VirtualData, { foreignKey:'virtualCateId', constraints: false })
    }
  }
  VirtualCate.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },      
      pId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "pId"
      },      
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        defaultValue: '',
        comment: "分类名称"
      },
      pic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "图片地址"
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "简介"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '状态1是启用0是禁用',
      },
      levelCode: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "层次码"
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      }, 
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建者id"
      },
      updateById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建者id"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualCate',
      tableName: 'zk_virtual_cate',
      comment: '仿真数据分类表'
    }
  )
  return VirtualCate
}
