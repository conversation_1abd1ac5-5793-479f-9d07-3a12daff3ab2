# Logs
log
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules
jspm_packages
.idea

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# 上传的img图片
# ;public/upload/img
# ;public/files
public/backup/
public/export/
public/upload/img
*.xlsx
*.xls

#ueditor的上传文件
uploadUeditor/formula/
uploadUeditor/img/
uploadUeditor/video/
uploadUeditor/scrawl/


#office装换文件夹
office/
public/uploads/
public/uploadFile/
public/uploadInitVR/
public/uploadVideo/
public/uploadExe/
uploadSourceFile/
uploadImportFile/
/bin
pm2.conf.json
dist
zk-zkzn-vue/public/config.js
zk-zkzn-vue/src/config/settings.js
