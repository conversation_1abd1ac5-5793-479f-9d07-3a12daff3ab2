const { ADMIN_TOKEN, PRIVATE_KEY } = require('../utils/constant')
const jwt = require('jsonwebtoken');
const expressJWT = require("express-jwt");
const { hgetRedis } = require('./redisUtils');

module.exports = async (req, res, next) => {
  // const authHeader = req.headers['authorization'];
  // let token = authHeader && authHeader.split(' ')[1];
  let defaultToken = null

  if (req.headers.accesstoken) {
    defaultToken = req.headers.accesstoken
  } else if (req.headers.token) {
    defaultToken = req.headers.token
  } else if (req.query && req.query.token) {
    defaultToken = req.query.token
  }
  
  //注释掉此处则账号可以登录多个地方
  // if (defaultToken && defaultToken != ADMIN_TOKEN && req.originalUrl != "/api/token/refresh") {
  //   try {
  //     // 使用verify方法验证token
  //     const decoded = jwt.verify(defaultToken, PRIVATE_KEY);
  //     const result = await hgetRedis(`user:${decoded.userId}`, 'token')      
  //     if (!result || result != defaultToken) {
  //       return res.send({ code: 405, msg: '此账号已在其它地方登录' })
  //     }
  //   } catch (error) {
  //     return res.send({ code: 402, msg: "登录令牌已过期，请重新登录" })
  //   }
  // }

  if (defaultToken && defaultToken === ADMIN_TOKEN) {
    req.user = {
      userId: 1,
      userName: 'admin_temp'
    }
    // 如果是固定的token，则直接放行
    next(); // 调用下一个中间件或路由处理器
  } else {
    // 如果token存在，则进行jwt验证
    expressJWT({
      secret: PRIVATE_KEY,
      algorithms: ["HS256"],
      credentialsRequired: true, //false 不效验
      getToken: function (req) {
        // if (req.headers.token && req.headers.token.split(' ')[0] === 'Bearer') {
        //   return req.headers.token.split(' ')[1]
        if (req.headers.accesstoken) {
          return req.headers.accesstoken
        } else if (req.headers.token) {
          return req.headers.token
        } else if (req.query && req.query.token) {
          return req.query.token
        }
        return null
      }
    }).unless({
      path: [
        "/api/token/check",
        "/api/token/refresh",        
        "/api/token/getRefreshToken",
        "/api/common/captcha",
        "/api/common/config",
        "/api/common/comRegister",
        "/favicon.ico",
        { url: /^\/uploads\/.*/, methods: ['GET'] },
        "/api/dd/version",
        "/api/dd/download",
        "/api/user/register",
        "/api/ueditor/",
        "/api/login",
        "/api/user/login",
        "/api/register/add",        
        // "/api/user/getInfo",
        // "/api/user/upload",
        "/api/file/ueditor/ue",
        // "/api/processRecord/issueList",
        // "/api/processRecord/queryQuestionById",
        // "/api/processRecord/articleList",
        // "/api/processRecord/queryIssueById",
        // "/api/virtual/operList",
        // "/api/virtualData/operList",
      ],
    })(req, res, next)
  }
};