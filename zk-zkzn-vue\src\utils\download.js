import axios from 'axios'
import { Message } from 'element-ui'
import { saveAs } from 'file-saver'
import {
  baseURL,
  contentType,
  invalidCode,
  messageDuration,
  noPermissionCode,
  requestTimeout,
  successCode,
  tokenName,
  debounce,
} from "@/config/settings";
import store from "@/store";
import errorCode from '@/utils/errorCode'
import { blobValidate } from "@/utils/validate";

export default {
  temp(name) {
    var url = (process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL : baseURL) + "/file/tempDownload?fileName=" + encodeURI(name)
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { [tokenName]: store.getters["user/accessToken"] }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data])
        this.saveAs(blob, decodeURI(name))
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  resource(resource) {
    var url = (process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL : baseURL) + "/common/download/resource?resource=" + encodeURI(resource);
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data])
        this.saveAs(blob, decodeURI(res.headers['download-filename']))
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  zip(url, name) {
    var url = (process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL : baseURL) + url
    axios({
      method: 'get',
      url: url,
      responseType: 'blob',
      headers: { 'Authorization': 'Bearer ' + getToken() }
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data], { type: 'application/zip' })
        this.saveAs(blob, name)
      } else {
        this.printErrMsg(res.data);
      }
    })
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
    Message.error(errMsg);
  }
}

