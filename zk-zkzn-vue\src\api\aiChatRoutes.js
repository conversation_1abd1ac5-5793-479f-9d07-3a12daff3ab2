/**
 * AI聊天路由处理
 * 该文件用于处理AI聊天的API请求
 * 在实际项目中，这个文件应该在后端实现，这里提供一个前端模拟版本
 */

import { getRemainingChats, getChatHistory, sendChatMessage } from '@/utils/aiChatService';

/**
 * 处理获取剩余聊天次数的请求
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
export async function handleGetRemainingChats(req, res) {
  try {
    const { userId, courseId } = req.query;
    
    if (!userId || !courseId) {
      return res.status(400).json({
        code: 400,
        msg: '缺少必要参数',
        data: null
      });
    }
    
    const remainingChats = getRemainingChats(userId, courseId);
    
    return res.json({
      code: 200,
      msg: '获取成功',
      data: remainingChats
    });
  } catch (error) {
    console.error('获取剩余聊天次数失败:', error);
    return res.status(500).json({
      code: 500,
      msg: '服务器错误',
      data: null
    });
  }
}

/**
 * 处理获取聊天历史的请求
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
export async function handleGetChatHistory(req, res) {
  try {
    const { userId, courseId } = req.query;
    
    if (!userId || !courseId) {
      return res.status(400).json({
        code: 400,
        msg: '缺少必要参数',
        data: null
      });
    }
    
    const chatHistory = getChatHistory(userId, courseId);
    
    return res.json({
      code: 200,
      msg: '获取成功',
      data: chatHistory
    });
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    return res.status(500).json({
      code: 500,
      msg: '服务器错误',
      data: null
    });
  }
}

/**
 * 处理发送聊天消息的请求
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
export async function handleSendChatMessage(req, res) {
  try {
    const { userId, courseId, message } = req.body;
    
    if (!userId || !courseId || !message) {
      return res.status(400).json({
        code: 400,
        msg: '缺少必要参数',
        data: null
      });
    }
    
    const result = await sendChatMessage(userId, courseId, message);
    
    return res.json({
      code: 200,
      msg: '发送成功',
      data: result
    });
  } catch (error) {
    console.error('发送聊天消息失败:', error);
    return res.status(500).json({
      code: 500,
      msg: error.message || '服务器错误',
      data: null
    });
  }
}

export default {
  handleGetRemainingChats,
  handleGetChatHistory,
  handleSendChatMessage
}; 