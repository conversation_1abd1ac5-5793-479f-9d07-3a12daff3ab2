'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class DictData extends Model {
    static associate (models) {
      this.belongsTo(models.Dict, { foreignKey: 'dictCode', constraints: false })
    }
  }
  DictData.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },      
      dictCode: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "字典编码"
      },
      dictValue: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "字典项值"
      },
      dictLabel: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "字典项名称"
      },
      listClass: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "回显样式"
      },
      isDefault: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue:'N',
        comment: "是否默认值（Y是N否）"
      },
      sortBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "显示顺序"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "菜单状态（1正常 2停用）"
      },
      remark: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "备注"
      },    
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建人id"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'DictData',
      tableName: 'sys_dict_data',
      comment: '字典关联数据表',
    }
  )
  return DictData
}
