<template>
  <div>
    <vxe-modal v-model="dialogVisible" show-zoom resize show-footer width="1000" height="700" title="关联题目">
      <template #default>
        <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">

          <el-form-item v-show="isDeviceOrOper==1" label="议题名称" prop="issueName">
            <el-input placeholder="请输入议题名称" v-model="formSearch.issueName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
          </el-form-item>
          <el-form-item v-show="isDeviceOrOper==1" label="所属工艺" prop="processTypeId">
            <el-select v-model="formSearch.processTypeId" placeholder="所属工艺" clearable size="small">
              <el-option v-for="dict in processTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>

          <el-form-item v-show="isDeviceOrOper!=1" label="题干" prop="questionContent">
            <el-input placeholder="请输入题干" v-model="formSearch.questionContent" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
          </el-form-item>
          <el-form-item v-show="isDeviceOrOper!=1" label="题目类型" prop="questionType">
            <el-select v-model="formSearch.questionType" placeholder="题目类型" clearable size="small">
              <el-option v-for="dict in questionTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <vxe-grid ref="xGrid" max-height="500px" :row-id="isDeviceOrOper==1?'issueId':'questionId'" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" @checkbox-all="selectChangeEvent" @checkbox-change="selectChangeEvent" :checkbox-config="{reserve:true}" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
          <template #toolbar_left>
            <el-button type="primary" plain size="mini">已勾选数量：{{chooseData?chooseData.length:0}}</el-button>
            <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
          </template>
          <template v-slot:questionLevelSlot="{ row }">
            <el-rate v-model="row.questionLevel" disabled> </el-rate>
          </template>
          <template v-slot:processTypeSlot="{ row }">
            <el-tag>{{row.processTypeName}}</el-tag>
          </template>
          <!-- 题干 -->
          <template v-slot:questionContentSlot="{ row }">
            <div v-html="row.questionContent"></div>
          </template>
          <template v-slot:questionTypeSlot="{ row }">
            <el-tag :type="row.questionType==1?'':'success'">{{row.questionType==1?'单选题':'多选题'}}</el-tag>
          </template>
          <template #conIssueExpand="{ row }">
            <div class="expandBox">
              <el-row style="margin-bottom:10px;">
                <el-col>
                  <p v-html="row.issueContent"></p>
                </el-col>
              </el-row>
            </div>
          </template>
          <template #conExpand="{ row }">
            <div class="expandBox">
              <el-row style="margin-bottom:10px;">
                <el-col>
                  <p v-html="row.questionContent"></p>
                </el-col>
              </el-row>
              <el-row v-for="(item, i) in row.questionAnswers" :key="i" class="questionBox">
                <el-col>
                  <el-button :type="item.isRight == '1' ?'success':''" size="mini">{{item.answerNum}}</el-button>
                  <div class="qs_contentBox">
                    <p v-html="item.answerContent"></p>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <span class="title">分析:</span>
                  <div class="qs_contentBox">
                    <p class="analyBox" v-html="row.questionAnalysis"></p>
                  </div>
                </el-col>
              </el-row>
              <div>
                难度: <el-rate class="inlineRate" v-model="row.questionLevel" disabled></el-rate>
              </div>
            </div>
          </template>
        </vxe-grid>
      </template>
      <template #footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'expand', width: 60, slots: { content: 'conExpand' } },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'questionContent', title: '题干', slots: { default: 'questionContentSlot' } },
        { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
        { sortable: true, field: 'questionLevel', title: '题目难度', slots: { default: 'questionLevelSlot' } },
        { sortable: true, field: 'createBy', title: '创建人' },
        { sortable: true, field: 'createTime', title: '创建时间' },
        // { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      formSearch: {
        issueName: '',
        processTypeId: '',
        questionContent: '',
        questionType: '',
        page: 1,
        pageSize: 10,
        recordId: undefined,
        virtualprocessId: undefined,
        deviceId: undefined,
        deviceOperationId: undefined,
      },
      loading: false,
      dialogVisible: false,
      questionTypeOptions: [
        {
          label: '单选题',
          value: '1'
        },
        {
          label: '多选题',
          value: '2'
        }
      ],
      isExpand: false,
      chooseData: [],
      isDeviceOrOper: 1,//2022.2.24 czk添加区分是工艺设备还是操作的关联默认1
      processTypeOptions: [],
    };
  },
  methods: {
    open (val, flag) {
      this.chooseData = []
      this.formSearch.recordId = val.recordId
      this.formSearch.virtualId = val.virtualId
      this.formSearch.virtualprocessId = val.virtualprocessId
      this.formSearch.deviceId = val.deviceId
      this.formSearch.deviceOperationId = val.deviceOperationId
      this.dialogVisible = true
      this.tablePage.currentPage = 1
      this.formSearch.page = 1
      //2022.2.24 czk添加区分是工艺设备还是操作的关联
      this.isDeviceOrOper = flag || 1
      if (flag == 1) {
        this.tableColumn = [
          { type: 'checkbox', width: 60 },
          { type: 'expand', width: 60, slots: { content: 'conIssueExpand' } },
          { type: 'seq', width: 60, title: '序号' },
          { sortable: true, field: 'issueName', title: '议题名称' },
          { field: 'issueContent', title: '议题名称', type: 'html' },
          { field: 'processTypeName', title: '所属工艺', slots: { default: 'processTypeSlot' } },
          // { field: 'status', title: '状态' },
          { sortable: true, field: 'createBy', title: '创建人' },
          { sortable: true, field: 'createTime', title: '创建时间' },
        ]
      } else {
        this.tableColumn = [
          { type: 'checkbox', width: 60 },
          { type: 'expand', width: 60, slots: { content: 'conExpand' } },
          { type: 'seq', width: 60, title: '序号' },
          { sortable: true, field: 'questionContent', title: '题干', slots: { default: 'questionContentSlot' } },
          { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
          { sortable: true, field: 'questionLevel', title: '题目难度', slots: { default: 'questionLevelSlot' } },
          { sortable: true, field: 'createBy', title: '创建人' },
          { sortable: true, field: 'createTime', title: '创建时间' },
        ]
      }
      this.getProcessFlow()
      this.getData();
    },
    getData () {
      this.loading = true;
      if (!this.formSearch.recordId) {
        return this.$msg('参数缺失，请刷新界面后再试', 'warning')
      }
      let fun = 'questionQuery'
      if (this.isDeviceOrOper == 1) {
        fun = 'issueQuery'
      } else {
        fun = 'questionQuery'
      }
      this.$http[fun](this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    getProcessFlow () {
      this.$http.getDictList({ dictField: 'processFlow' }).then(res => {
        this.processTypeOptions = res.data;
      });
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage;
      this.tablePage.pageSize = pageSize;
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData();
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    selectChangeEvent ({ checked, records }) {
      this.chooseData = this.$refs.xGrid.getCheckboxReserveRecords()
      if (records.length > 0) {
        this.chooseData = this.chooseData.concat(records)
      }
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllRowExpand(true)
      } else {
        this.$refs.xGrid.clearRowExpand()
      }
    },
    save () {
      let formData = this.chooseData.map(item => {
        return {
          recordId: this.formSearch.recordId,
          virtualId: this.formSearch.virtualId,
          virtualprocessId: this.formSearch.virtualprocessId,
          deviceId: this.formSearch.deviceId,
          deviceOperationId: this.formSearch.deviceOperationId || null,
          issueId: item.issueId || null,
          questionId: item.questionId || null
        }
      })
      this.$http.addAssociation(formData).then(res => {
        this.close()
        this.$parent.getData();
        this.$msg('操作成功', 'success')
      });
    },
    close () {
      this.$refs.xGrid.clearCheckboxReserve()
      this.dialogVisible = false;
    },
  }
};
</script>

<style lang="scss" scoped>
.expandBox {
  font-size: 16px;
  // padding-left: 30px;
  .title {
    display: inline-block;
    vertical-align: top;
    margin: 3px 0;
  }
  .qs_contentBox {
    display: inline-block;
    margin-left: 10px;
  }
  .analyBox {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep p {
    margin: 3px 0;
    img {
      vertical-align: middle;
    }
  }
}
</style>
