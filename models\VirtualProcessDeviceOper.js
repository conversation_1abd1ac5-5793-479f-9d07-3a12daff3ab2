'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class VirtualProcessDeviceOper extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsTo(models.VirtualProcessDevice, { foreignKey: 'id', constraints: false })
      this.hasOne(models.UserExam, { foreignKey: 'deviceOperationId', constraints: false })
      this.belongsTo(models.PathPrefix, { foreignKey: 'prefixId', constraints: false })
    }
  }
  VirtualProcessDeviceOper.init(
    {
      name: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.operationName
        },
      },
      info: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.operationInfo
        },
      },
      level: {
        type: DataTypes.VIRTUAL,
        get () {
          return 3
        },
      },
      parentId: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.vpdId
        },
      },
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "实体ID"
      },
      vpdId: {
        // autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: true,
        // primaryKey: true,
        comment: "关联ID"
      },
      operName: {
        type: DataTypes.STRING(120),
        allowNull: true,
        comment: "操作名称"
      },
      assetName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "资源名称"
      },
      functionName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "函数名称"
      },
      operationName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "进程介绍"
      },
      operationInfo: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "操作信息"
      },
      typeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "操作类型（1自动/2点物体/3点UI/4提问/5细节窗）"
      },
      qizzTips: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: '',
        comment: "0代表可新增题目 1代表不可新增题目 默认0"
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      /* 2022.1.13 czk根据刘工要求，虚拟课程管理的树形结构应该是虚拟流程管理的树形数据，按照刘工提供的3个文件生成。
      原吴兴城又单独新建了一张表，不知道为什么？  
      所以现在添加部分字段，对应需要传递的数据信息，数据图片等
      */
      prefixId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "路径前缀id"
      },
      type: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "类型"
      },
      pic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "图片地址"
      },
      videoSrc: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "视频地址"
      },
      linkSrc: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "外链路径"
      },
      isPlatform:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "平台（1.web，2.window）"
      },
      webSrc:{
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "web端访问地址"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0正常 1停用）"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualProcessDeviceOper',
      tableName: 'zk_process_device_oper',
      comment:'仿真数据表（改版前）'
    }
  )
  return VirtualProcessDeviceOper
}
