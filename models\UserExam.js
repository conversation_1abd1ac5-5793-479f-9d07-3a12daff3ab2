'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class UserExam extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // this.belongsTo(models.TipArticle,{foreignKey:'articleId',constraints:false})
      // this.belongsTo(models.User,{foreignKey:'userId',constraints:false})
      // this.belongsTo(models.Question, {foreignKey:'questionId',constraints:false })

      this.hasMany(models.UserAnswer,{as:'userAnswers',foreignKey:'examId',constraints:false})
      this.belongsTo(models.User,{foreignKey:'userId'})
      this.belongsTo(models.VirtualProcessDeviceOper,{foreignKey:'deviceOperationId', constraints: false})
    }
  }
  UserExam.init(
    {      
      examId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "唯一ID"
      },
      userId:{
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "用户id"
      },      
      deviceOperationId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },  
      examScore:{
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "考试分数"
      },    
      isTrueNum:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "正确数量"
      },
      isFalseNum:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "错误数量"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'UserExam',
      tableName: 'zk_user_exam',
      comment:'用户考试结果表'
    }
  )
  return UserExam
}
