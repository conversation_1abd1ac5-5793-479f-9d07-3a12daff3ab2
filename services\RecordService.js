const { Record, Grade, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(Record.tableAttributes)

class RecordService {
  static async create (req, res) {
    let data = await Record.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let updateObj = req.body
    if (req.body.gradeId) {
      let isStatus = await Record.findOne({
        where: {
          gradeId: req.body.gradeId,
          status: 1
        }
      })
      if(isStatus) {
        updateObj['status'] = 0
      }
    }
    let data = await Record.update(updateObj, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await Record.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    // if (req.user && req.user.userName != 'admin') {
    where.userId = req.user.userId
    // }
    if (page && pageSize) {
      let { rows, count } = await Record.findAndCountAll({
        attributes: {
          include: [[$seq.col('Grade.gradeName'), 'gradeName']]
        },
        where,
        include: [
          {
            model: Grade,
            attributes: []
          }
        ],
        offset: (page - 1) * pageSize,
        limit: pageSize,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let data = await Record.findAll({
        where
      })
      res.sendSuccess({ code: 200, data })
    }
  }

  static async statusChange (req, res, next) {
    let { id, userId, gradeId } = req.body
    if (!id || !userId || !gradeId) return res.sendSuccess({ code: -1, msg: "参数缺失，请刷新后再试" })

    const transaction = await $seq.transaction()
    try {
      await Record.update({ status: 0 }, {
        where: {
          id: {
            $ne: id
          },
          gradeId,
          userId
        },
        transaction
      })
      let data = await Record.update({ status: 1 }, {
        where: {
          id,
          gradeId,
          userId
        },
        transaction
      })
      await transaction.commit();
      res.sendSuccess({ code: 200, data, msg: '修改成功' })
    } catch (error) {
      await transaction.rollback();
      next(error)
    }
  }

  static async defaultStatusChange (req, res, next) {
    let { id, userId } = req.body
    if (!id || !userId) return res.sendSuccess({ code: -1, msg: "参数缺失，请刷新后再试" })
    const transaction = await $seq.transaction()
    try {
      await Record.update({ defaultStatus: 0 }, {
        where: {
          id: {
            $ne: id
          },
          userId
        },
        transaction
      })
      let data = await Record.update({ defaultStatus: 1 }, {
        where: {
          id,
          userId
        },
        transaction
      })
      await transaction.commit();
      res.sendSuccess({ code: 200, data, msg: '修改成功' })
    } catch (error) {
      await transaction.rollback();
      next(error)
    }
  }

}

module.exports = RecordService
