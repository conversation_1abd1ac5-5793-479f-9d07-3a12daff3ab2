var express = require("express");
var router = express.Router();
const { uploadImg, uploadImport, tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/UserService");

//登录接口
router.post("/login", tryAsyncErrors(Service.login));
router.post("/logout", tryAsyncErrors(Service.logout));
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.post('/editInfo', tryAsyncErrors(Service.updateInfo))
router.post('/statusChange', tryAsyncErrors(Service.statusChange))
router.delete('/delete/:userId', tryAsyncErrors(Service.delete))
router.get('/list', tryAsyncErrors(Service.userList))
router.get('/serialList', tryAsyncErrors(Service.userSerialList))
router.post('/info', tryAsyncErrors(Service.info))
router.get('/info', tryAsyncErrors(Service.info))
router.get('/studentList', tryAsyncErrors(Service.studentList))
router.post('/getInfo', tryAsyncErrors(Service.getInfo))
router.post('/resetPwd', tryAsyncErrors(Service.resetPwd))
router.post('/pwdEdit', tryAsyncErrors(Service.pwdEdit))
router.post('/uploadImport', uploadImport.single('file'), tryAsyncErrors(Service.import))
router.post("/uploadAvatar", uploadImg.single("avatarfile"), tryAsyncErrors(Service.uploadAvatar));
router.post("/uploadUserAvatar", uploadImg.single("avatarfile"), tryAsyncErrors(Service.uploadUserAvatar));

module.exports = router;
