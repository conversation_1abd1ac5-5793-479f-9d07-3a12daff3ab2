/**
 * AI聊天服务
 * 该文件负责处理AI聊天的后端逻辑
 * 这里提供一个示例实现，实际项目中需要根据后端API进行适配
 */

// 导入AI配置
import aiConfig from '@/config/aiConfig'

// 模拟数据库，存储用户的聊天记录和剩余次数
const userChatData = {};

/**
 * 初始化用户聊天数据
 * @param {String} userId 用户ID
 * @param {String} courseId 课程ID
 */
function initUserChatData(userId, courseId) {
  const chatKey = `${userId}_${courseId}`;
  if (!userChatData[chatKey]) {
    userChatData[chatKey] = {
      remainingChats: aiConfig.maxQuestionsPerCourse || 5, // 从配置获取最大提问次数，默认5次
      history: [] // 聊天历史
    };
  }
  return userChatData[chatKey];
}

/**
 * 获取用户剩余聊天次数
 * @param {String} userId 用户ID
 * @param {String} courseId 课程ID
 * @returns {Number} 剩余聊天次数
 */
export function getRemainingChats(userId, courseId) {
  const userData = initUserChatData(userId, courseId);
  return userData.remainingChats;
}

/**
 * 获取聊天历史记录
 * @param {String} userId 用户ID
 * @param {String} courseId 课程ID
 * @returns {Array} 聊天历史记录
 */
export function getChatHistory(userId, courseId) {
  const userData = initUserChatData(userId, courseId);
  return userData.history;
}

/**
 * 发送聊天消息并获取回复
 * @param {String} userId 用户ID
 * @param {String} courseId 课程ID
 * @param {String} courseName 课程名称
 * @param {String} message 用户消息
 * @returns {Object} 包含AI回复和剩余次数的对象
 */
export async function sendChatMessage(userId, courseId, message, courseName = '') {
  const userData = initUserChatData(userId, courseId);
  
  // 检查剩余次数
  if (userData.remainingChats <= 0) {
    throw new Error('您已用完所有AI提问次数');
  }
  
  // 减少剩余次数
  userData.remainingChats--;
  
  // 记录用户消息
  const userMessage = {
    content: message,
    isUser: true,
    time: new Date().toLocaleTimeString()
  };
  userData.history.push(userMessage);
  
  // 这里可以调用真实的AI服务，如OpenAI API
  // 这里使用简单回复进行模拟
  const aiResponse = await simulateAIResponse(message, courseName || courseId);
  
  // 记录AI回复
  const aiMessage = {
    content: aiResponse,
    isUser: false,
    time: new Date().toLocaleTimeString()
  };
  userData.history.push(aiMessage);
  
  return {
    reply: aiResponse,
    remainingChats: userData.remainingChats
  };
}

/**
 * 模拟AI回复
 * 实际项目中应该调用真实的AI服务API
 * @param {String} message 用户消息
 * @param {String} courseInfo 课程名称或ID
 * @returns {Promise<String>} AI回复
 */
async function simulateAIResponse(message, courseInfo) {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 简单的关键词回复规则
  if (message.includes('你好') || message.includes('嗨') || message.includes('hi')) {
    return `你好！我是"${courseInfo}"课程的AI助手，有什么可以帮助您的吗？`;
  }
  
  if (message.includes('课程') || message.includes('学习')) {
    return `这门"${courseInfo}"课程包含了很多有用的知识点，建议您仔细阅读课程概述和学习资料部分。有具体问题可以随时向我提问！`;
  }
  
  if (message.includes('谢谢') || message.includes('感谢')) {
    return '不用谢！如果还有其他问题，随时可以问我。';
  }
  
  // 默认回复
  return `您的问题我已收到，这是关于"${courseInfo}"课程的问题。作为AI助手，我会尽力解答您的疑问。您可以更具体地描述您的问题，这样我能提供更准确的帮助。`;
}

export default {
  getRemainingChats,
  getChatHistory,
  sendChatMessage
}; 