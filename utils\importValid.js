const importObj = {
  virtualData: [
    { label: '名称', value: 'virtualName',valid:true },
    { label: '操作名称', value: 'operName',valid:true },
    { label: '简介', value: 'description' },
    { label: '排序', value: 'sortBy' },
    { label: '图片', value: 'pic',valid:true },
  ],
  zcSpaceHatchArr: [
    { label: '业务覆盖面', value: 'businessCoverage' },
    { label: '企业名称', value: 'enterPriseName' },
    { label: '所属空间', value: 'spaceNameUpload' },
    { label: '入孵时间', value: 'hatchTime' },
    { label: '入孵状态', value: 'incubationStatus' },
    { label: '所属行业', value: 'industryUpload' },
    { label: '企业产品/服务', value: 'productAndServer' },
    { label: '注册时间', value: 'registerTime' },
    { label: '注册资金（万元）', value: 'registeredCapital' },
    { label: '全年营收', value: 'yearRevenue' },
    { label: '带动就业人数', value: 'driveNumber' },
  ],
}


module.exports = {
  // excel表头部部分字段效验方法
  /** 
  * @params data:Array 要效验的字段名称validName 以及可返回的字段index名称indexName
            header:Array 要效验的excel表头数组
            res: Response http返回对象直接返回错误信息
  * @return {Object} 字段index下标
  */
  excelHeaderValid (compare, header, res) {
    if (!header || header.length == 0) {
      return res.sendSuccess({ code: -1, error: '', msg: '此表格未检测到表头数据！', })
    }
    let excelIndexObj = {}    

    //拼接sql
    let errField = []
    let str = ''
    let arr = importObj[compare]
    for (let i = 0; i < header.length; i++) {
      if(arr[i] && arr[i].valid) {
        excelIndexObj[arr[i].value+'Index'] = header.findIndex((item) => {
          return item == arr[i].label
        })
        if (excelIndexObj[arr[i].value+'Index'] == -1) {
          return res.sendSuccess({ code: -1, error: '表头存在异常', msg: `导入excel的表头中${arr[i].label}字段缺失` })
        }
      }

      if (arr[i] && header[i] == arr[i].label) {
        str += arr[i].value + ','
      } else {
        let index = arr.findIndex(item => item.label == header[i])
        if (index != -1) {
          str += arr[index].value + ','
        } else {
          errField.push(header[i])
        }
      }
    }
    str += 'createById'    
    excelIndexObj['sqlStr'] = str
    
    if (errField.length > 0) {
      let errmsg = 'excel表头部分字段未对应上(' + errField.join(',') + ')'
      return res.sendSuccess({ code: -1, error: '导入数据有误', msg: errmsg })
    }

    return excelIndexObj
  },  
}