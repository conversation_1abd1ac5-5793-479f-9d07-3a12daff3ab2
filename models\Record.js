'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Record extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsTo(models.Question, {foreignKey:'questionId',constraints:false })
      this.belongsTo(models.Grade, { foreignKey: 'gradeId', constraints: false })
      this.belongsTo(models.Class, { foreignKey: 'classId', constraints: false })
      this.hasMany(models.ProcessRecord, { foreignKey: 'recordId', constraints: false })
    }
  }
  Record.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "记录ID"
      },
      recordName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "记录名称"
      },
      gradeId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "年级id"
      },
      classId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "所属班级id"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        default:0,
        comment: '状态1是启用0是禁用（每个年级有且只有一个）',
      },
      defaultStatus: {
        type: DataTypes.INTEGER,
        allowNull: true,
        default:0,
        comment: '默认流程状态1是启用0是禁用（有且只有一个）',
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "创建人id"
      },
      createBy: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Record',
      tableName: 'zk_record',
      comment:'年级流程记录表'
    }
  )
  return Record
}
