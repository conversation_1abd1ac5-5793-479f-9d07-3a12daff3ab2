<template>
  <span class="icon-dot">
    <span></span>
  </span>
</template>

<script>
export default {
  name: "IconDot",
};
</script>

<style lang="scss" scoped>
$error-color:#F56C6C;
$success-color:#67C23A;
$warning-color:#E6A23C;
$primary-color:#409EFF;
.icon-dot {
  position: relative;
  display: inline-block;
  width: 6px;
  height: 6px;
  margin-right: 5px;
  vertical-align: middle;
  border-radius: 50%;
  background-color: $primary-color;
  span {
    border:1px solid $primary-color;
    // border-color:blue;
    position: absolute;
    top: 0;
    left: 0;
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transition: all .3s cubic-bezier(.645,.045,.355,1),border 0s,color .1s,font-size 0s;
    -webkit-animation: spreadDot 1.2s ease-in-out infinite;
    animation: spreadDot 1.2s ease-in-out infinite;
  }
}
.icon-dot.error {
  background-color: $error-color;
  span {
    border-color:$error-color;
  }
}
.icon-dot.primary {
  background-color: $primary-color;
  span {
    border-color:$primary-color;
  }
}
.icon-dot.success {
  background-color: $success-color;
  span {
    border-color:$success-color;
  }
}
.icon-dot.warning {
  background-color: $warning-color;
  span {
    border-color:$warning-color;
  }
}
@keyframes spreadDot {
  0% {
    opacity: .8;
    transform: scale(1);
  }
  // 50% {
  //   opacity: .4;
  //   transform: scale(1.8);
  // }
  100% {
    opacity: 0;
    transform: scale(2.6);
  }
}
</style>
