var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/TipArticleService");
const ServiceProcess = require("../../services/ProcessRecordService");

router.post('/articleList', tryAsyncErrors(Service.list))
// router.post('/questionList', tryAsyncErrors(ServiceProcess.list))
// router.post('/issueList', tryAsyncErrors(ServiceProcess.issueList))
// router.post('/queryQuestionById', tryAsyncErrors(ServiceProcess.queryList))
router.post('/add', tryAsyncErrors(ServiceProcess.create))
router.post('/edit', tryAsyncErrors(ServiceProcess.update))
router.delete('/delete/:processRecordId', tryAsyncErrors(ServiceProcess.delete))

//改版后接口修改
router.post('/questionList', tryAsyncErrors(ServiceProcess.associationQuestion))
router.post('/issueList', tryAsyncErrors(ServiceProcess.associationIssue))

router.post('/queryQuestionById', tryAsyncErrors(ServiceProcess.queryQuestionById))
router.post('/queryIssueById', tryAsyncErrors(ServiceProcess.queryIssueById))

module.exports = router;
