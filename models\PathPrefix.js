'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class PathPrefix extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.hasMany(models.VirtualData, { foreignKey: 'prefixId', constraints: false })
      // this.belongsTo(models.VirtualProcess, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcessDevice, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcessDeviceOper, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcess)
      // this.belongsTo(models.VirtualProcessDevice)
      // this.belongsTo(models.VirtualProcessDeviceOper)
    }
  }
  PathPrefix.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      prefixName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "路径前缀名称"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '状态0是启用1是禁用',
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      attchName: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "附件名称"
      },
      attchAddr: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "附件下载地址"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
    },
    {
      sequelize,
      modelName: 'PathPrefix',
      tableName: 'zk_path_prefix',
      comment: '协议/路径前缀表'
    }
  )
  return PathPrefix
}
