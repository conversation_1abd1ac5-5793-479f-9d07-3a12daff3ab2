<template>
    <div>
        <div class="user-info-head" @click="editCropper()">
            <img :src="$SOURCEURL + user.avatar" :class="user.avatar == null
                    ? 'img-circle img-lg avatarClass'
                    : 'img-circle img-lg '
                " />
            <div class="tooltip" v-show="user.avatar == null ? true : false">
                点击上传头像
            </div>
        </div>
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body @opened="modalOpened"
            @close="closeDialog()">
            <el-row>
                <el-col :xs="24" :md="12" :style="{ height: '350px' }">
                    <vue-cropper ref="cropper" :img="options.img" :info="true" :autoCrop="options.autoCrop"
                        :autoCropWidth="options.autoCropWidth" :autoCropHeight="options.autoCropHeight"
                        :fixedBox="options.fixedBox" @realTime="realTime" :outputType="'png'" v-if="visible" />
                </el-col>
                <el-col :xs="24" :md="12" :style="{ height: '350px' }">
                    <div class="avatar-upload-preview">
                        <img :src="previews.url" :style="previews.img" />
                    </div>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :lg="2" :md="2">
                    <el-upload action="#" :http-request="requestUpload" :show-file-list="false"
                        :before-upload="beforeUpload">
                        <el-button size="small">
                            选择
                            <i class="el-icon-upload el-icon--right"></i>
                        </el-button>
                    </el-upload>
                </el-col>
                <el-col :lg="{ span: 1, offset: 2 }" :md="2">
                    <el-button icon="el-icon-plus" size="small" @click="changeScale(1)"></el-button>
                </el-col>
                <el-col :lg="{ span: 1, offset: 1 }" :md="2">
                    <el-button icon="el-icon-minus" size="small" @click="changeScale(-1)"></el-button>
                </el-col>
                <el-col :lg="{ span: 1, offset: 1 }" :md="2">
                    <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></el-button>
                </el-col>
                <el-col :lg="{ span: 1, offset: 1 }" :md="2">
                    <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()"></el-button>
                </el-col>
                <el-col :lg="{ span: 2, offset: 6 }" :md="2">
                    <el-button type="primary" size="small" @click="uploadImg()">提 交</el-button>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>

<script>
import store from "@/store";
import { VueCropper } from "vue-cropper";

export default {
    components: { VueCropper },
    props: {
        user: {
            type: Object,
        },
    },
    data() {
        return {
            // 是否显示弹出层
            open: false,
            // 是否显示cropper
            visible: false,
            // 弹出层标题
            title: "修改头像",
            options: {
                img: null, //裁剪图片的地址
                autoCrop: true, // 是否默认生成截图框
                autoCropWidth: 200, // 默认生成截图框宽度
                autoCropHeight: 200, // 默认生成截图框高度
                fixedBox: true, // 固定截图框大小 不允许改变
            },
            previews: {},
        };
    },
    mounted() {
        this.options.img = this.$SOURCEURL + this.user.avatar;
    },
    methods: {
        // 编辑头像
        editCropper() {
            this.open = true;
        },
        // 打开弹出层结束时的回调
        modalOpened() {
            this.visible = true;
        },
        // 覆盖默认的上传行为
        requestUpload() { },
        // 向左旋转
        rotateLeft() {
            this.$refs.cropper.rotateLeft();
        },
        // 向右旋转
        rotateRight() {
            this.$refs.cropper.rotateRight();
        },
        // 图片缩放
        changeScale(num) {
            num = num || 1;
            this.$refs.cropper.changeScale(num);
        },
        // 上传预处理
        beforeUpload(file) {
            if (file.type.indexOf("image/") == -1) {
                this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
            } else {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => {
                    console.log(reader.result);
                    this.options.img = reader.result;
                };
            }
        },
        // 上传图片
        uploadImg() {
            this.$refs.cropper.getCropBlob((data) => {
                console.log(data);
                let formData = new FormData();
                formData.append("avatarfile", data);
                this.$http.uploadUserAvatar(formData).then((res) => {
                    this.open = false;
                    this.options.img = this.$SOURCEURL + res.imgUrl;
                    this.user.avatar = res.imgUrl;
                    //   this.$emit("flushedData",);
                    this.$msg("修改成功", "success");
                    this.visible = false;
                });
            });
        },
        // 实时预览
        realTime(data) {
            this.previews = data;
        },
        // 关闭窗口
        closeDialog() {
            //   this.options.img = store.getters["user/avatar"];
            this.visible = false;
        },
    },
};
</script>
<style scoped lang="scss">
.user-info-head {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    object-fit: cover;
}

.user-info-head:hover:after {
    content: "+";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    color: #eee;
    background: rgba(0, 0, 0, 0.5);
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    cursor: pointer;
    line-height: 120px;
    border-radius: 50%;
}

.tooltip {
    position: absolute;
    bottom: 38%;
    left: 48%;
    margin-left: -60px;
    /* background-color: black; */
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    width: 120px;
    font-size: 12px;
}

.user-info-head:hover .tooltip {
    visibility: hidden;
}

.avatarClass {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    background: #23272e;
    color: #fff;
}

.avatar-container:hover .tooltip {
    visibility: visible;
    opacity: 1;
}
</style>
