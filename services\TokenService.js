const { User, UserSerial, sequelize: $seq, $LikeWhere } = require('../models')
const { ADMIN_TOKEN, PWD_SALT, PRIVATE_KEY, REFRESH_PRIVATE_KEY, EXPIRESD } = require('../utils/constant')
const jwt = require('jsonwebtoken');
const { hqueryRedis, hsetRedis, hgetRedis, ttlRedis } = require('../utils/redisUtils');
class TokenService {

  static async check (req, res) {
    if (!req.body.token) return res.sendSuccess({ code: -1, msg: "token参数缺失" })
    if (!req.body.serialNumber) return res.sendSuccess({ code: -1, msg: "设备编号参数缺失" })
    let token = req.body.token

    if (token == ADMIN_TOKEN) {
      let userSerial = {
        userId: 1,
        serialNumber: req.body.serialNumber
      }
      await UserSerial.create(userSerial)
      return res.sendSuccess({ code: 200, isExpire: false, msg: "令牌未过期" })
    }

    try {
      // 使用verify方法验证token
      const decoded = jwt.verify(token, PRIVATE_KEY);
      let userSerial = {
        userId: decoded.userId,
        serialNumber: req.body.serialNumber
      }
      await UserSerial.create(userSerial)
      res.sendSuccess({ code: 200, isExpire: false, msg: "令牌未过期" })
    } catch (error) {
      // 如果token过期，会进入这里
      console.error('Token error', error.message);
      res.sendSuccess({ code: 200, isExpire: true, msg: "令牌已过期" })
    }
  }

  static async refresh (req, res) {
    if (!req.body.refreshToken) return res.sendSuccess({ code: -1, msg: "token参数缺失" })
    let refreshToken = req.body.refreshToken

    try {
      // 使用verify方法验证token
      const decoded = jwt.verify(refreshToken, REFRESH_PRIVATE_KEY);      
      let redisToken = await hqueryRedis(`user:${decoded.userId}`,"refreshToken")
      if (!redisToken) return res.send({ code: -1, success: false, isNotExpire: false, msg: "refresh令牌已过期,请重新登录" })
      let token = jwt.sign({ userName: decoded.userName, userId: decoded.userId, roleId: decoded.roleId }, PRIVATE_KEY, {
        expiresIn: EXPIRESD,
      })
      await hsetRedis(`user:${decoded.userId}`, "token", token)
      await ttlRedis(`user:${decoded.userId}`)
      res.send({ code: 200, token, data: token })
    } catch (error) {
      // 如果token过期，会进入这里
      console.error('Token error:', error.message);
      res.send({ code: -1, isNotExpire: false, msg: "refresh令牌已过期" })
    }
  }

  static async getRefreshToken (req, res) {
    if (!req.query.token) return res.sendSuccess({ code: -1, msg: "token参数缺失" })
    let token = req.query.token
    try {
      // 使用verify方法验证token
      const decoded = jwt.verify(token, REFRESH_PRIVATE_KEY);
      let refreshToken = await hgetRedis(`user:${decoded.userId}`, 'refreshToken')
      if (!refreshToken) return res.sendSuccess({ code: -1, success: false, isNotExpire: false, msg: "refresh令牌已过期,请重新登录" })
      res.sendSuccess({ code: 200, refreshToken, data: refreshToken })
    } catch (error) {
      // 如果token过期，会进入这里
      console.error('Token error', error.message);
      res.sendSuccess({ code: -1, isNotExpire: false, msg: "refresh令牌已过期" })
    }
  }

}

module.exports = TokenService
