<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="菜单名称" prop="menuName">
        <el-input v-model="formSearch.menuName" placeholder="请输入菜单名称" clearable size="small" @keyup.enter.native="getData" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="菜单状态" clearable size="small" @change="getData()">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="()=>{this.$resetForm('queryForm');getData()}">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid :height="$store.getters['table/srcTableHeight']" ref="xGrid" row-id="menuId" :tree-config="{children:'children',iconOpen: 'vxe-icon--arrow-bottom', iconClose: 'vxe-icon--arrow-right',iconLoaded:'vxe-icon--refresh roll'}" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}" v-loading="loading" :columns="tableColumn" :data="tableData">
      <!-- v-hasPermi="['system:menu:add']" -->
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['menu/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </template>
      <template #icon="{row}">
        <i :class="'iconfont '+row.icon"></i>
      </template>
      <template #status="{row}">
        <el-tag :type="row.status == 0?'default':'danger'">{{row.status == 0?'正常':'停用'}}</el-tag>
      </template>
      <template #menuType="{row}">
        <el-tag :type="row.menuType == 'M'?'default':row.menuType == 'C'?'success':'warning'">{{row.menuTypeName}}</el-tag>
      </template>
      <template #operate="{ row }">
        <el-button type="text" v-permissions="['menu/add']" icon="el-icon-edit" @click="handleChildAdd(row)">新增下级</el-button>
        <el-button type="text" v-permissions="['menu/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['menu/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal ref="xModal" v-model="modalVisible" width="800" height="600" :title="modalTitle" @show="$xModalOpen('xModal')" :show-zoom="$store.state.settings.formSpan ==12?true:false" mask-closable esc-closable show-footer resize transfer>
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :span="$store.state.settings.formSpan" :data="formData" :items="formItem" :rules="$rules.userRules" title-width="100" title-align="right" title-overflow='tooltip'>
          <template #treeSelect="{ row }">
            <tree-select :options="menuOptions" v-model="formData.parentId" :show-count="true" :normalizer="normalizer" placeholder="选择上级菜单" @select="getTreeNode" />
          </template>
          <template #iconSelect="{ row }">
            <el-popover placement="bottom-start" width="360" trigger="click" @show="$refs['iconSelect'].reset()">
              <IconSelect ref="iconSelect" @selected="selected" />
              <vxe-input slot="reference" v-model="formData.icon" placeholder="点击选择图标" readonly clearable>
                <i v-if="formData.icon" slot="prefix" :class="'iconfont '+formData.icon" style="height: 32px;width: 16px;color:#2b2b2b;" />
                <i v-else slot="prefix" class="el-icon-search el-input__icon" />
              </vxe-input>
            </el-popover>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import IconSelect from "@/layouts/components/IconSelect";
export default {
  name: "Menu",
  components: { IconSelect },
  data () {
    return {
      tableColumn: [
        { field: 'menuName', title: '菜单名称', treeNode: true, align: 'left', minWidth: 120 },
        { field: 'icon', title: '图标', slots: { default: 'icon' } },
        { field: 'menuType', title: '菜单类型', slots: { default: 'menuType' } },
        { field: 'path', title: '路由地址' },
        { field: 'component', title: '组件路径' },
        { field: 'perms', title: '权限' },
        { field: 'status', title: '状态', slots: { default: 'status' } },
        { field: 'sortBy', title: '排序' },
        { field: 'createTime', title: '创建时间' },
        { title: '操作', width: 200, slots: { default: 'operate' } }
      ],
      tableData: [],
      formSearch: {
        menuName: '',
        status: '',
      },
      statusOptions: [
        { value: '0', label: '正常' },
        { value: '1', label: '停用' },
      ],
      normalizer (node) {
        return {
          id: node.menuId,
          label: node.menuName,
          children: node.children
        };
      },
      menuOptions: [],
      formData: {
        menuId: undefined,
        parentId: 0,
        menuType: 'M',
        component: '',
        icon: '',
        menuName: '',
        path: '',
        sortBy: 0,
        perms: '',
        status: '0',
        isFrame: '1',
        isCache: '0',
        visible: '0',
      },
      formItem: [
        { field: 'parentId', title: '上级菜单', span: 24, slots: { default: 'treeSelect' } },
        { field: 'menuType', title: '菜单类型', span: 24, itemRender: { name: '$radio', options: [{ label: '目录', value: 'M' }, { label: '菜单', value: 'C' }, { label: '按钮', value: 'F' }], events: { change: this.menuTypeChange } } },
        { field: 'menuName', title: '菜单名称', itemRender: { name: '$input', props: { placeholder: '请输入菜单名称' } } },
        { field: 'sortBy', title: '排序', itemRender: { name: '$input', props: { type: 'integer', placeholder: '请输入排序号' } } },
        { field: 'icon', title: '图标', visibleMethod: ({ data }) => { return data.menuType != 'F' }, slots: { default: 'iconSelect' } },
        { field: 'path', title: '路由地址', visibleMethod: ({ data }) => { return data.menuType != 'F' }, itemRender: { name: '$input', props: { placeholder: '请输入路由地址' } } },
        { field: 'component', title: '组件路径', visibleMethod: ({ data }) => { return data.menuType == 'C' }, itemRender: { name: '$input', props: { placeholder: '请输入组件路径' } } },
        { field: 'perms', title: '权限', visibleMethod: ({ data }) => { return data.menuType == 'F' }, itemRender: { name: '$input', props: { placeholder: '请输入权限标识' } } },
        { field: 'isFrame', title: '是否外链', visibleMethod: ({ data }) => { return data.menuType != 'F' }, itemRender: { name: '$radio', options: [{ label: '是', value: '1' }, { label: '否', value: '0' }] } },
        { field: 'isCache', title: '是否缓存', visibleMethod: ({ data }) => { return data.menuType == 'C' }, itemRender: { name: '$radio', options: [{ label: '缓存', value: '0' }, { label: '不缓存', value: '1' }] } },
        { field: 'status', title: '菜单状态', visibleMethod: ({ data }) => { return data.menuType != 'F' }, itemRender: { name: '$radio', options: [{ label: '正常', value: '0' }, { label: '停用', value: '1' }] } },
        { field: 'visible', title: '显示状态', visibleMethod: ({ data }) => { return data.menuType != 'F' }, itemRender: { name: '$radio', options: [{ label: '显示', value: '0' }, { label: '隐藏', value: '1' }] } },
      ],
      modalTitle: '新增数据',
      modalVisible: false,
      isExpand: false,
    };
  },
  created () {
    this.getData();
  },
  methods: {
    // 选择图标
    selected (name) {
      this.formData.icon = name;
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllTreeExpand(true)
      } else {
        this.$refs.xGrid.clearTreeExpand()
      }
    },
    getTreeNode (node, id) {
      console.log(node, id, 'nodeid');
    },
    menuTypeChange ({ data }) {
      if (data.menuType == 'M') {
        data.component = 'Layout'
      }
      if (data.menuType == 'F') {
        data.component = ''
      }
    },
    // 更新页面数据
    getData () {
      this.loading = true;
      this.$http.menuList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.loading = false;
        this.$refs.xGrid.setAllTreeExpand(true);
      });
    },
    // 刷新菜单
    updateMenu () {
      // this.$http.menuList().then(res => {
      //   this.$store.commit('setMenu', res.data);
      //   this.$store.commit('addMenu', this.$router);
      // });
    },
    handleAdd () {
      this.getTreeSelect();
      this.modalTitle = '新增菜单';
      this.formData = {
        menuId: undefined,
        parentId: 0,
        menuType: 'M',
        component: 'Layout',
        icon: '',
        menuName: '',
        path: '',
        sortBy: 1,
        perms: '',
        status: '0',
        isFrame: '0',
        isCache: '0',
        visible: '0',
      }
      this.modalVisible = true;
    },
    handleChildAdd (row) {
      this.getTreeSelect();
      this.modalTitle = '新增菜单';
      this.formData = {
        menuId: undefined,
        parentId: row.menuId,
        menuType: 'M',
        component: 'Layout',
        icon: '',
        menuName: '',
        path: '',
        sortBy: 0,
        perms: '',
        status: '0',
        isFrame: '0',
        isCache: '0',
        visible: '0',
      }
      this.modalVisible = true;
    },
    handleEdit (row) {
      this.getTreeSelect();
      this.modalTitle = '修改菜单';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    // 删除菜单
    handleDel (row) {
      this.$confirm('请确认是否删除此数据及其所有子节点数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.menuDel(row.menuId).then(() => {
          this.$msg('删除成功', 'success');
          this.getData();
          this.updateMenu();
        });
      })
    },
    getTreeSelect () {
      this.$http.menuList().then(res => {
        this.menuOptions = [];
        let menu = { menuId: 0, menuName: '主类目', children: [] };
        menu.children = res.data;
        this.menuOptions.push(menu);
      });
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = JSON.parse(JSON.stringify(this.formData))
          if (!form.menuId) {
            this.$http.menuAdd(form).then(res => {
              this.$msg('新增成功', 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.menuEdit(form).then(res => {
              this.$msg('修改成功', 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
      this.modalVisible = false
    },
  }
};
</script>

<style scoped></style>
