'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Issue extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsTo(models.VirtualProcess, { foreignKey: 'processTypeId', constraints: false })
    }
  }
  Issue.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      issueName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "议题名称"
      },
      issueContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "议题内容"
      },
      processTypeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        // defaultValue: null,
        comment: "所属工艺流程id"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '状态0是启用1是禁用',
      },
      isDraft: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue:0,
        comment: '状态1是草稿0不是',
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      modelName: 'Issue',
      tableName: 'zk_issue',
      timestamps: true,
      comment: '议题表'      
    }
  )
  return Issue
}
