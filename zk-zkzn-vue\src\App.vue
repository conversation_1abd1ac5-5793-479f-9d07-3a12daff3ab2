<template>
  <div id="vue-admin-beautiful">
    <router-view />
  </div>
</template>

<script>
import router from "@/router";
import store from "@/store";
export default {
  name: "App",
  data () {
    return {
      inactivityTimer: '',
      beforeUnload_time: 0,
      gap_time: 0,
    }
  },
  created () {
    this.resetTimer()
    // 在用户活动时调用 resetTimer 重置定时器
    document.addEventListener('mousemove', this.resetTimer)
  },
  // mounted () {
  //   window.addEventListener("beforeunload", this.beforeunloadHandler);
  //   window.addEventListener("unload", this.unloadHandler);
  // },
  beforeDestroy () {
    document.removeEventListener('mousemove', this.resetTimer)
    // 移除事件监听
    // window.removeEventListener('beforeunload', this.beforeunloadHandler);
    // window.removeEventListener('unload', this.unloadHandler);    
  },
  methods: {
    resetTimer () {    
      // 30分钟无操作自动退回登录界面
      clearTimeout(this.inactivityTimer)
      this.inactivityTimer = setTimeout(() => {
        // 执行注销操作或者跳转到登录页面的代码
        store.dispatch("user/resetAccessToken");
        store.dispatch("user/resetRefreshToken");
        router.push('/login')
      }, 30 * 60 * 1000) // 30分钟
    },
    beforeunloadHandler (e) {
      this.beforeUnload_time = new Date().getTime();
    },
    unloadHandler (e) {
      this.gap_time = new Date().getTime() - this.beforeUnload_time;
      if (this.gap_time < 5) {
        store.dispatch("user/resetAccessToken");
        store.dispatch("user/resetRefreshToken");
      }
    },
  }
};
</script>

<style lang="scss">
@import "./styles/main.scss"; //全局css
</style>
