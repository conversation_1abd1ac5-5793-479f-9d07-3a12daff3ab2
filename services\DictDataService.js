const { DictData, sequelize: $seq, $LikeWhere } = require('../models')
const RedisService = require("./RedisService");
const fields = Object.keys(DictData.tableAttributes)

class DictDataService {
  static async create (req, res) {
    if (!req.body.dictCode || !req.body.dictValue || !req.body.dictLabel) return res.sendSuccess({ code: -1, msg: '字典编码或字典项值或字典项名称不能为空' })
    let data = await DictData.create(req.body)
    RedisService.setDictCodeOption(req.body.dictCode)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await DictData.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    RedisService.setDictCodeOption(req.body.dictCode)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    if (!id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await DictData.destroy({
      where: {
        id
      },
    })
    RedisService.setDictCodeOption(req.body.dictCode)
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async list (req, res) {
    let where = $LikeWhere(req, res, fields)
    if(req.query.dictCode) {
      where['dictCode'] = req.query.dictCode
    }
    let data = await DictData.findAll({ where })
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = DictDataService
