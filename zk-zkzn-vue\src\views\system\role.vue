<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="角色名称" prop="roleName">
        <el-input placeholder="请输入角色名称" v-model="formSearch.roleName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="角色状态" clearable size="small" @change="getData('formSearch')">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['role/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <!-- 角色的状态切换开关 -->
      <template v-slot:statusSlot="{ row, rowIndex }">
        <vxe-switch v-model="row.status" v-if="row.roleId != 1" open-label="启用" :open-value="1" close-label="停用" :close-value="0" @change="statusChange(row)"></vxe-switch>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row, rowIndex }">
        <el-button type="text" v-permissions="['role/edit']" icon="el-icon-edit" v-if="row.roleId != 1" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['role/del']" icon="el-icon-delete" v-if="row.roleId != 1" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal className="roleModal" width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.roleRules" title-width="120" title-align="right">
          <template #menuSelect="{ row }">
            <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
            <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
            <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menu" node-key="menuId" :check-strictly="false" empty-text="加载中，请稍后" :props="defaultProps"></el-tree>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  name: "Role",
  data () {
    return {
      normalizer (node) {
        return {
          belowHeight: '',
          id: node.menuId,
          label: node.menuName,
          children: node.children
        };
      },
      menuTable: [],
      // 菜单列表
      menuOptions: [],
      defaultProps: {
        children: 'children',
        label: 'menuName'
      },
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'roleName', title: '角色名称' },
        { field: 'roleSort', title: '排序' },
        { field: 'status', title: '是否禁用', slots: { default: 'statusSlot' } },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { field: 'remark', title: '备注', width: 120 },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        roleName: '',
        menuIds: '',
        halfIds: '',
        roleSort: 1,
        status: 1,
        remark: '',
      },
      formItem: [
        { field: 'roleName', title: '角色名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'roleSort', title: '角色排序', span: 24, itemRender: { name: '$input', props: { type: 'number', placeholder: '请输入名称' } } },
        { field: 'status', title: '角色状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea', props: { placeholder: '请输入备注' } } },
        { field: 'menuIds', title: '菜单权限', className: 'formTreeTitle', span: 24, slots: { default: 'menuSelect' } },
        // { align: 'center', span: 3, itemRender: { name: '$buttons', children: [{ props: { type: 'submit', content: '保存', status: 'primary' } }] } }
      ],
      statusOptions: [
        { value: '1', label: '正常' },
        { value: '0', label: '停用' },
      ],
      formSearch: {
        roleName: '',
        status: '',
        page: 1,
        pageSize: 10
      },
      modalTitle: '新增角色',
      modalVisible: false,
      menuExpand: false,
      menuNodeAll: false,
    };
  },
  created () {
    this.getMenuData()
    this.getData();
  },
  methods: {
    getMenuData () {
      //获取菜单列表
      this.$http.menuList({}).then(res => {
        this.menuTable = res.data;
        this.menuOptions = res.data;
      });
    },
    getData (params) {
      //角色列表
      if (params == 'formSearch') {
        this.tablePage.currentPage = 1
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.roleList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    statusChange (row) {
      this.loading = true;
      this.$http.roleEdit(row).then(res => {
        this.loading = false;
        this.$msg(res.msg, 'success')
      });
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {

      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          this.formData.menuIds = this.$refs.menu.getCheckedKeys().join()
          this.formData.halfIds = this.$refs.menu.getHalfCheckedKeys().join()
          if (this.modalTitle == '新增角色') {
            this.$http.roleAdd(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.roleEdit(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand (value, type) {
      if (type == 'menu') {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].menuId].expanded = value;
        }
      } else if (type == 'dept') {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].menuId].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll (value, type) {
      if (type == 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == 'dept') {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    handleAdd () {
      this.modalTitle = '新增角色';
      this.modalVisible = true;
      this.formData = {
        roleName: '',
        roleSort: 1,
        status: 1,
        menuIds: [],
      };
    },
    handleEdit (row) {
      this.modalTitle = '编辑角色';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
      this.$nextTick(() => {
        let arr = row.menuIds.split(',')
        if (this.$refs.menu) {
          this.$refs.menu.setCheckedKeys(arr)
        } else {
          setTimeout(() => {
            this.$refs.menu.setCheckedKeys(arr)
          }, 500)
        }
      })
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.roleId;
          } else {
            list += item.roleId + ',';
          }
        });
      } else {
        list = row.roleId;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.roleDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.roleModal ::v-deep .formTreeTitle {
  .vxe-form--item-inner {
    align-items: start;
    .vxe-form--item-title {
      vertical-align: top;
    }
  }
}
</style>
