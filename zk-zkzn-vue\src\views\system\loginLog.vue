<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input placeholder="请输入用户名称" v-model="formSearch.userName" clearable
          @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="operStatus">
        <el-select v-model="formSearch.operStatus" placeholder="操作状态" clearable size="small" @change="getData('formSearch')">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData"
      :seq-config="{ startIndex: (formSearch.page - 1) * formSearch.pageSize }"
      :pager-config="Object.assign({ currentPage: formSearch.page, total: tablePage.total }, formSearch)"
      @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading"
      :toolbarConfig="{ size: 'mini', custom: true }">
      <!-- 用户的状态切换开关 -->
      <template v-slot:statusSlot="{ row }">
        <el-tag :type="row.operStatus == 1 ? '' : 'danger'">{{ row.operStatus == 1 ? 'success' : 'error' }}</el-tag>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
    </vxe-grid>

    <!-- <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.userRules" title-width="120" title-align="right"></vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal> -->
  </div>
</template>

<script>
export default {
  name: "LoginLog",
  data() {
    return {
      modalTitle: '新增用户',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { field: 'unit', title: '用户单位' },
        { sortable: true, field: 'userName', title: '用户名称' },
        { sortable: true, field: 'operUrl', title: '请求地址' },
        { sortable: true, field: 'hostIp', title: 'IP地址' },
        { field: 'location', title: '归属地', width: 200  },
        { field: 'userSerialNum', title: '登录设备次数' },
        { field: 'serialNumberCount', title: '登录设备数量' },
        { field: 'operStatus', title: '状态', slots: { default: 'statusSlot' } },
        { sortable: true, field: 'operTime', title: '操作时间' },
      ],
      tableData: [],
      formData: {
        userName: '',
        nickName: '',
        password: '',
        phone: 1,
        status: 1,
        email: '',
        roleIds: ''
      },
      formSearch: {
        userName: '',
        status: '',
        page: 1,
        pageSize: 10
      },
      statusOptions: [
        { value: '1', label: '成功' },
        { value: '0', label: '失败' }
      ],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData(params) {
      //用户列表
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.loginLogList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    //重置按钮
    resetFormSearch() {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange({ currentPage, pageSize }) {

      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>
