<template>
  <div class="login-container">
    <!-- 左上角固定logo -->
    <div class="login-logo">
      <img src="@/assets/gkd.png" alt="logo" />
    </div>

    <el-row>
      <el-col :xs="24" :sm="24" :md="12" :lg="16" :xl="16">
        <div style="color: transparent">占位符</div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          class="login-form"
          label-position="left"
        >
          <div class="title">
            <div class="login-center">
              <!-- <img src="~@/assets/login_images/backcenter.png" alt=""> -->
            </div>
          </div>
          <div class="title-tips">{{ title }}</div>
          <el-form-item style="margin-top: 0px" prop="userName">
            <span class="svg-container svg-container-admin">
              <vab-icon :icon="['fas', 'user']" />
            </span>
            <el-input
              v-model.trim="form.userName"
              v-focus
              placeholder="请输入用户名"
              tabindex="1"
              type="text"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
          <el-form-item prop="password">
            <span class="svg-container">
              <vab-icon :icon="['fas', 'lock']" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model.trim="form.password"
              :type="passwordType"
              tabindex="2"
              placeholder="请输入密码"
              @keyup.enter.native="handleLogin"
            />
            <span
              v-if="passwordType === 'password'"
              class="show-password"
              @click="handlePassword"
            >
              <vab-icon :icon="['fas', 'eye-slash']"></vab-icon>
            </span>
            <span v-else class="show-password" @click="handlePassword">
              <vab-icon :icon="['fas', 'eye']"></vab-icon>
            </span>
          </el-form-item>
          <el-form-item>
            <el-row>
              <el-col :span="12">
                <div
                  style="
                    width: 150px;
                    height: 58px;
                    color: #fff;
                    background-color: #fff;
                    box-sizing: border-box;
                    border-radius: 4px;
                  "
                  v-html="captchaCode"
                  ref="captcha"
                  @click="getCaptcha"
                ></div>
              </el-col>
              <el-col :span="12">
                <el-input
                  type="text"
                  v-model="form.captcha"
                  placeholder="请输入验证码"
                  @keyup.enter.native="handleLogin"
                >
                  <i
                    slot="prefix"
                    class="el-icon-chat-line-square"
                    style="font-size: 20px; padding-top: 20px"
                  ></i>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>

          <el-button
            :loading="loading"
            class="login-btn"
            type="primary"
            @click="handleLogin"
            >登 录
          </el-button>
          <router-link to="/register" v-if="functionDisplay">
            <div style="margin-top: 20px; font-size: 17px">
              <span style="color: #fff">还没有账号？</span>
              <span style="color: #22a1ff">【立刻注册】</span>
              <!-- <span style="color: #df5813;">【立刻注册】</span> -->
            </div>
          </router-link>
        </el-form>
      </el-col>
    </el-row>
    <!-- 底部文字 -->
    <div class="bottom-text">
      <div class="text-line">机电工程与自动化国家级虚拟仿真实验教学中心</div>
      <div class="text-line">电路与测试技术实验中心</div>
    </div>
  </div>
</template>

<script>
import { isPassword } from "@/utils/validate";
import { getRouterList } from "@/api/router";
export default {
  name: "Login",
  directives: {
    focus: {
      inserted(el) {
        el.querySelector("input").focus();
      },
    },
  },
  data() {
    const validateUserName = (rule, value, callback) => {
      if ("" == value) {
        callback(new Error("用户名不能为空"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!isPassword(value)) {
        callback(new Error("密码不能少于6位"));
      } else {
        callback();
      }
    };
    return {
      nodeEnv: process.env.NODE_ENV,
      title: this.$baseTitle,
      form: {
        userName: "",
        password: "",
        captcha: "",
        uuid: "",
      },
      captchaCode: "",
      rules: {
        userName: [
          {
            required: true,
            trigger: "blur",
            validator: validateUserName,
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            validator: validatePassword,
          },
        ],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
      getIp: "",
      getLocation: "",
      functionDisplay: false,
    };
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = (route.query && route.query.redirect) || "/";
      },
      immediate: true,
    },
  },
  created() {
    this.queryRegistration();
    this.getCaptcha();
  },
  mounted() {
    // if ("production" !== process.env.NODE_ENV) {
    //   this.form.userName = "admin";
    //   this.form.password = "123456";
    // }
  },
  methods: {
    queryRegistration() {
      this.$http.comConfig().then((res) => {
        if (Number(res.data.isRegister) == 1) {
          this.functionDisplay = true;
        } else {
          this.functionDisplay = false;
        }
      });
    },
    getCaptcha() {
      this.$http.comCaptcha().then((res) => {
        this.captchaCode = res.data;
        this.form.uuid = res.uuid;
      });
    },
    handlePassword() {
      this.passwordType === "password"
        ? (this.passwordType = "")
        : (this.passwordType = "password");
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    // searchChildren(children, path) {
    //   for (const child of children) {
    //     // 如果当前项的 component 与传入的 path 匹配，则返回 true
    //     if (child.component === path) {
    //       return true;
    //     }
    //     // 如果当前项有子项，则递归搜索子项
    //     if (child.children && child.children.length > 0) {
    //       if (this.searchChildren(child.children, path)) {
    //         return true;
    //       }
    //     }
    //   }
    //   // 如果没有找到匹配的 component，则返回 false
    //   return false;
    // },
    handleLogin() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            await this.$store.dispatch("user/login", this.form);
            // 获取路由列表
            let { data } = await getRouterList();
            let routerPath = "virtual/course"; // 默认路径

            // 检查路径是否存在
            const exists = this.$searchChildren(data, routerPath);

            if (exists.found) {
              routerPath = "virtual/course";
            } else {
              routerPath = "personalCenter";
            }
            // 路由跳转
            await this.$router.push("/" + routerPath);
          } catch (error) {
            this.getCaptcha(); // 获取验证码
          } finally {
            this.loading = false; // 无论成功还是失败，都要关闭加载状态
          }
        } else {
          // 表单验证失败的处理
          this.$msg("表单验证失败，请检查输入", "warning");
        }
      });
    },
    // deepRouter(data) {
    //   for (let i = 0; i < data.length; i++) {
    //     let fPath = data[i].path;
    //     if (data[i].children && data[i].children.length > 0) {
    //       for (let j = 0; j < data[i].children.length; j++) {
    //         if (data[i].children[j].component != "Layout") {
    //           return fPath == "/"
    //             ? "/" + data[i].children[j].path
    //             : fPath + "/" + data[i].children[j].path;
    //         }
    //       }
    //     }
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
/* 针对自动填充输入框的背景色、文字颜色 */
// .el-input input:-webkit-autofill {
//     -webkit-box-shadow: 0 0 0px 1000px rgba(204, 204, 204, 0.3) inset; /* 半透明灰色背景 */
//     -webkit-text-fill-color: #ffffff; /* 白色文字 */
//     background-color: rgba(204, 204, 204, 0.3) !important; /* 保证透明灰色 */
//     color: #ffffff !important; /* 保证文字颜色为白色 */
// }

.login-container {
  height: 100vh;
  background: url("~@/assets/login_images/back.jpg") no-repeat;
  background-size: cover;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;

  /* 左上角固定logo样式 */
  .login-logo {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;

    img {
      width: 120px;
      height: 120px;
      // max-height: 60px;
      object-fit: contain;
    }
  }

  .title {
    font-size: 54px;
    font-weight: 500;
    // color: rgba(14, 18, 26, 1);
    color: #fff;
  }

  .title-tips {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 28px;
    font-weight: 400;
    text-justify: distribute-all-lines;
    text-align-last: justify;
    // color: rgba(14, 18, 26, 1);
    // color: #409eff;
    color: #fff;
    // color: #81B4D1;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .login-btn {
    display: inherit;
    width: 100%;
    height: 60px;
    margin-top: 5px;
    border: 0;
    letter-spacing: 2px;
    font-size: 20px;
    background: #3480ac;
    &:hover {
      opacity: 0.9;
    }
  }

  .login-form {
    position: relative;
    max-width: 100%;
    margin: calc((100vh - 420px) / 2) 10% 10%;
    overflow: hidden;

    .forget-password {
      width: 100%;
      margin-top: 40px;
      text-align: left;

      .forget-pass {
        width: 129px;
        height: 19px;
        font-size: 20px;
        font-weight: 400;
        color: rgba(92, 102, 240, 1);
      }
    }
  }

  .tips {
    margin-bottom: 10px;
    font-size: $base-font-size-default;
    color: $base-color-white;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .title-container {
    position: relative;

    .title {
      margin: 0 auto 40px auto;
      font-size: 34px;
      font-weight: bold;
      color: $base-color-blue;
      text-align: center;
    }
  }

  .svg-container {
    position: absolute;
    top: 14px;
    left: 15px;
    z-index: $base-z-index;
    font-size: 16px;
    color: #d7dee3;
    cursor: pointer;
    user-select: none;
  }

  .show-password {
    position: absolute;
    top: 14px;
    right: 25px;
    font-size: 16px;
    color: #d7dee3;
    cursor: pointer;
    user-select: none;
  }

  ::v-deep {
    .el-form-item {
      padding-right: 0;
      margin: 20px 0;
      color: #454545;
      background: transparent;
      border: 1px solid transparent;
      border-radius: 2px;

      &__content {
        min-height: $base-input-height;
        line-height: $base-input-height;
      }

      &__error {
        position: absolute;
        top: 100%;
        left: 18px;
        font-size: $base-font-size-small;
        line-height: 18px;
        color: $base-color-red;
      }
    }

    .el-input {
      box-sizing: border-box;

      input {
        height: 58px;
        padding-left: 45px;
        font-size: $base-font-size-default;
        line-height: 58px;
        // color: $base-font-color;
        background: #f6f4fc;
        color: #fff;
        background-color: rgba($color: #cccccc, $alpha: 0.3) !important;
        border: 0;
        // caret-color: $base-font-color;
      }
    }
  }

  /* 底部文字样式 */
  .bottom-text {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 1000;

    .text-line {
      color: #fff;
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
      margin-bottom: 5px;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>




