<template>
  <div class="ai-chat-container">
    <div v-if="remainingChats > 0" class="chat-box">
      <div class="chat-remaining">
        <el-alert
          :title="`您还剩余 ${remainingChats} 次AI提问机会`"
          type="info"
          show-icon
          :closable="false"
        ></el-alert>
      </div>

      <div class="chat-history" ref="chatHistory">
        <div v-for="(msg, index) in chatMessages" :key="index" :class="['chat-message', msg.isUser ? 'user-message' : 'ai-message']">
          <div class="message-avatar">
            <el-image 
              :src="msg.isUser ? userAvatar : aiAvatar" 
              style="width: 40px; height: 40px; border-radius: 50%;"
            ></el-image>
          </div>
          <div class="message-content">
            <div class="message-name">{{ msg.isUser ? '您' : 'AI助手' }}</div>
            <div class="message-text">{{ msg.content }}</div>
            <div class="message-time">{{ msg.time }}</div>
          </div>
        </div>
      </div>

      <div class="chat-input">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="输入您的问题..."
          v-model="messageInput"
          :disabled="loading"
          @keyup.enter.native="sendMessage"
        ></el-input>
        <el-button 
          type="primary" 
          :loading="loading" 
          @click="sendMessage" 
          :disabled="!messageInput.trim()"
        >
          <i class="el-icon-s-promotion"></i> 发送
        </el-button>
      </div>
    </div>
    
    <div v-else class="chat-limit-reached">
      <el-empty description="您已用完所有AI提问次数">
        <template #description>
          <p>您已用完本课程的AI提问次数(5次/课程)</p>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script>
import aiConfig from '@/config/aiConfig';

export default {
  name: "AiChat",
  props: {
    courseId: {
      type: [String, Number],
      required: true
    },
    courseName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      remainingChats: aiConfig.maxQuestionsPerCourse || 5, // 从配置获取默认值
      chatMessages: [],
      messageInput: "",
      loading: false,
      userAvatar: require('@/assets/img/avater.jpg'),
      aiAvatar: require('@/assets/img/ai-avatar.png'),
      userId: null
    };
  },
  created() {
    // 从store或者其他地方获取用户ID
    this.getUserInfo();
  },
  mounted() {
    this.getChatHistory();
  },
  methods: {
    getUserInfo() {
      this.$http.userInfo().then(res => {
        if (res && res.data && res.data.id) {
          this.userId = res.data.id;
          this.getRemainingChats();
        } else {
          // 如果没有获取到用户ID，使用一个默认ID
          console.warn("未获取到用户ID，使用临时ID");
          this.userId = 'guest-' + new Date().getTime();
          this.getRemainingChats();
        }
      }).catch(err => {
        console.error("获取用户信息失败", err);
        // 如果获取用户信息失败，使用一个默认ID
        this.userId = 'guest-' + new Date().getTime();
        this.getRemainingChats();
      });
    },
    
    getRemainingChats() {
      if (!this.userId) return;
      
      this.$http.aiChatRemaining({
        userId: this.userId,
        courseId: this.courseId,
        courseName: this.courseName
      }).then(res => {
        if (res && typeof res.data === 'number') {
          this.remainingChats = res.data;
        }
      }).catch(err => {
        console.error("获取剩余对话次数失败", err);
      });
    },
    
    getChatHistory() {
      if (!this.userId) return;
      
      this.$http.aiChatHistory({
        userId: this.userId,
        courseId: this.courseId,
        courseName: this.courseName
      }).then(res => {
        if (res && res.data && Array.isArray(res.data)) {
          this.chatMessages = res.data;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      }).catch(err => {
        console.error("获取聊天历史失败", err);
      });
    },
    
    // 过滤AI回复，移除<think>...</think>标签内容
    filterAIResponse(response) {
      if (!response) return '';
      
      // 移除<think>...</think>标签内的内容
      let filtered = response.replace(/<think>[\s\S]*?<\/think>/g, '');
      
      // 移除可能的其他思考格式，如markdown代码块中的思考过程
      filtered = filtered.replace(/```思考[\s\S]*?```/g, '');
      filtered = filtered.replace(/```think[\s\S]*?```/g, '');
      filtered = filtered.replace(/```thinking[\s\S]*?```/g, '');
      
      // 移除以"思考："或"思考过程："开头的段落
      filtered = filtered.replace(/^(思考[：:]|思考过程[：:]|[Tt]hinking[：:]).*?(?=\n\n|\n$|$)/gms, '');
      
      // 移除可能嵌入在括号内的思考过程
      filtered = filtered.replace(/\(思考:.*?\)/g, '');
      filtered = filtered.replace(/（思考:.*?）/g, '');
      
      // 清理可能产生的多余空行
      filtered = filtered.replace(/\n{3,}/g, '\n\n');
      
      return filtered.trim();
    },
    
    sendMessage() {
      const message = this.messageInput.trim();
      if (!message || this.loading || this.remainingChats <= 0) return;
      
      const currentTime = new Date().toLocaleTimeString();
      
      // 添加用户消息到聊天列表
      this.chatMessages.push({
        content: message,
        isUser: true,
        time: currentTime
      });
      
      this.scrollToBottom();
      this.loading = true;
      this.messageInput = "";
      
      // 发送消息到后端
      this.$http.aiChatSend({
        userId: this.userId,
        courseId: this.courseId,
        courseName: this.courseName,
        message: message
      }).then(res => {
        // 添加AI回复到聊天列表
        if (res && res.data) {
          // 过滤AI回复内容，移除思考过程
          const filteredReply = this.filterAIResponse(res.data.reply);
          
          this.chatMessages.push({
            content: filteredReply,
            isUser: false,
            time: new Date().toLocaleTimeString()
          });
          
          // 更新剩余对话次数
          this.remainingChats = res.data.remainingChats;
          this.scrollToBottom();
        }
      }).catch(err => {
        console.error("发送消息失败", err);
        this.$message.error("发送消息失败，请稍后重试");
      }).finally(() => {
        this.loading = false;
      });
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.chatHistory) {
          this.$refs.chatHistory.scrollTop = this.$refs.chatHistory.scrollHeight;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ai-chat-container {
  height: 480px; /* 调整整个聊天组件的高度 */
  display: flex;
  flex-direction: column;
  
  .chat-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .chat-remaining {
      margin-bottom: 5px; /* 减小底部边距 */
      flex-shrink: 0; /* 防止此元素被压缩 */
    }
    
    .chat-history {
      flex: 1;
      overflow-y: auto;
      padding: 8px; /* 减小内边距 */
      background-color: #f9f9f9;
      border-radius: 4px;
      margin-bottom: 5px; /* 减小底部边距 */
      height: 330px; /* 调整高度与整体容器匹配 */
      min-height: 330px;
      max-height: 330px;
      
      .chat-message {
        display: flex;
        margin-bottom: 8px; /* 减小消息之间的间距 */
        
        &.user-message {
          flex-direction: row-reverse;
          
          .message-content {
            margin-right: 8px; /* 减小边距 */
            margin-left: 0;
            align-items: flex-end;
            
            .message-text {
              background-color: #409EFF;
              color: white;
              border-radius: 10px 0 10px 10px;
            }
          }
        }
        
        &.ai-message {
          .message-content {
            margin-left: 8px; /* 减小边距 */
            
            .message-text {
              background-color: #EBEEF5;
              color: #333;
              border-radius: 0 10px 10px 10px;
            }
          }
        }
        
        .message-avatar {
          flex-shrink: 0;
        }
        
        .message-content {
          display: flex;
          flex-direction: column;
          max-width: 70%;
          
          .message-name {
            font-size: 11px; /* 减小字体大小 */
            color: #909399;
            margin-bottom: 3px; /* 减小底部边距 */
          }
          
          .message-text {
            padding: 8px 12px; /* 减小内边距 */
            word-break: break-word;
            white-space: pre-wrap; /* 保留换行并自动换行 */
            line-height: 1.5; /* 增加行间距，提高可读性 */
            max-width: 100%; /* 确保文本不会溢出 */
          }
          
          .message-time {
            font-size: 11px; /* 稍微减小字体 */
            color: #C0C4CC;
            margin-top: 3px; /* 减小顶部边距 */
            text-align: right;
          }
        }
      }
    }
    
    .chat-input {
      display: flex;
      align-items: flex-end;
      margin-top: 5px; /* 减小顶部边距 */
      flex-shrink: 0; /* 防止输入区域被压缩 */
      height: 65px; /* 减小输入区域的高度 */
      
      .el-textarea {
        margin-right: 10px;
        flex: 1;
      }
      
      .el-button {
        height: 48px; /* 减小按钮高度 */
        flex-shrink: 0; /* 确保按钮不被压缩 */
      }
    }
  }
  
  .chat-limit-reached {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 480px; /* 与整体容器高度一致 */
    width: 100%;
    
    .el-empty {
      width: 100%;
    }
  }
}
</style> 