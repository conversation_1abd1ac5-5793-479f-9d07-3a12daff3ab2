import axios from "axios";
import {
  baseURL,
  contentType,
  invalidCode,
  messageDuration,
  noPermissionCode,
  loginElseCode,
  requestTimeout,
  successCode,
  tokenName,
  debounce,
} from "@/config/settings";
import { Loading, Message, MessageBox } from "element-ui";
import store from "@/store";
import qs from "qs";
import router from "@/router";
import _ from "lodash";
import { saveAs } from 'file-saver'
import { blobValidate } from "@/utils/validate"
import errorCode from '@/utils/errorCode'

const service = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL : baseURL,
  timeout: requestTimeout,
  headers: {
    "Content-Type": contentType,
    // 'Access-Control-Allow-Origin': '*',
    // 'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    // 'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  },
});

//2022.2.28---start--- czk添加此段代码，解决重复请求问题
const pending = []
const cancelToken = axios.CancelToken;
/**
 * 移除请求
 * @param {Object} config 
 */
const removePending = (config) => {
  for (let p in pending) {
    if (pending[p].u === config.url + '&' + config.method) { //当当前请求在数组中存在时执行函数体
      pending[p].f(); //执行取消操作
      pending.splice(p, 1); //把这条记录从数组中移除
    }
  }
}
//2022.2.28---end--- czk添加此段代码，解决重复请求问题


let loadingInstance;
service.interceptors.request.use(
  (config) => {
    //2022.2.28---start--- czk添加此段代码，解决重复请求问题
    removePending(config) // 在请求开始前，对之前的请求做检查取消操作
    config.cancelToken = new cancelToken(function executor (c) {
      // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
      pending.push({ u: config.url + '&' + config.method, f: c });
    });
    //2022.2.28---end--- czk添加此段代码，解决重复请求问题

    if (store.getters["user/accessToken"]) {
      config.headers[tokenName] = store.getters["user/accessToken"];
    }
    //2021.12.20 czk修改此处，此处的loadsh会进行数据转换，导致上传文件的formData异常
    //2021.12.27 czk修改此处，此处的loadsh会进行数据转换，导致传递对象中的属性值为null或''或undefined时，会删除此属性，导致编辑时需要为空的字段无法发送给后端
    //2022.2.17  czk修改此处，此处的loadsh会进行数据转换，array数组会被转换为对象，新增了!Array.isArray(config.data)判断
    //2024.9.26  czk修改此处，垃圾玩意，注释了拉倒
    // if (config.url.toLocaleLowerCase().indexOf('upload') == -1 && config.url.toLocaleLowerCase().indexOf('import') == -1 && config.url.toLocaleLowerCase().indexOf('edit') == -1 && !Array.isArray(config.data) && config.data) {
    //   config.data = _.pickBy(config.data, _.identity);
    // }
    if (process.env.NODE_ENV !== "test") {
      if (contentType === "application/x-www-form-urlencoded;charset=UTF-8") {
        if (config.data && !config.data.param) {
          config.data = qs.stringify(config.data);
        }
      }
    }
    const needLoading = () => {
      let status = false;
      debounce.forEach((item) => {
        if (_.includes(config.url, item)) {
          status = true;
        }
      });
      return status;
    };
    if (needLoading()) {
      loadingInstance = Loading.service();
    }
    return config;
  },
  (error) => {
    console.log(error, 'DevTools failed to load source map');
    return Promise.reject(error);
  }
);

const errorMsg = (message) => {
  return Message({
    message: message,
    type: "error",
    duration: messageDuration,
  });
};

let isNotResFlag = false
//标志当前是否正在刷洗token
let isNotRefreshing = true;
//请求队列
let cancelRequests = [];

service.interceptors.response.use(
  (response) => {
    removePending(response) // 在请求结束后，移除本次请求
    if (loadingInstance) {
      loadingInstance.close();
    }
    const { status, data, config } = response;
    // console.log(response,'{ args: [2, 10], msg: "用户名长度须在2-10之间" }');
    //2022.3.10 czk添加了confirm参数，用来导入数据报错信息不会自动消失
    const { code, msg, confirm } = data;
    if (code !== successCode) {
      if (confirm) {
        Message({
          duration: messageDuration,
          showClose: true,
          message: msg,
          type: 'error'
        })
        return Promise.reject(
          "vue-admin-beautiful请求异常拦截:" +
          JSON.stringify({ url: config.url, code, msg }) || "Error"
        )
      }

      switch (code) {
        case loginElseCode:
          if (isNotResFlag == false) {
            isNotResFlag = true
            MessageBox.alert('此账号已在其它地方登录', '提示', {
              confirmButtonText: '确定',
              type: 'warning',
            });
            store.dispatch("user/resetAccessToken");
            router.push({
              path: "/login",
            });
            isNotResFlag = false
          }
          break;
        case invalidCode:
          let refreshToken = store.getters["user/refreshToken"]
          if (refreshToken) {
            if (isNotRefreshing) {
              isNotRefreshing = false
              return service({ url: "/token/refresh", method: "post", data: { refreshToken } }).then(res => {
                store.commit("user/setAccessToken", res.token);
                //执行requests队列中的请求，（requests中存的不是请求参数，而是请求的Promise函数，这里直接拿来执行就好）
                cancelRequests.forEach(run => run())
                //将请求队列置空
                cancelRequests = []
                //重新执行当前未执行成功的请求并返回
                return service(config);
              }).catch((error) => {
                cancelRequests = []
                // errorMsg("登录令牌已过期，请重新登录");
                store.dispatch("user/resetAccessToken");
                store.dispatch("user/resetRefreshToken");
                return router.push({
                  path: "/login",
                });
              }).finally(() => {
                isNotRefreshing = true;
              })
            } else {
              //如果当前已经是处于刷新token的状态，就将请求置于请求队列中，这个队列会在刷新token的回调中执行，由于new关键子存在声明提升，所以不用顾虑会有请求没有处理完的情况，这段添加请求的程序一定会在刷新token的回调执行之前执行的
              return new Promise(resolve => {
                //这里加入的是一个promise的解析函数，将响应的config配置对应解析的请求函数存到requests中，等到刷新token回调后再执行
                cancelRequests.push(() => {
                  resolve(service(config));
                })
              })
            }
          } else {
            errorMsg("登录令牌已过期，请重新登录");
            store.dispatch("user/resetAccessToken");
            store.dispatch("user/resetRefreshToken");
            router.push({
              path: "/login",
            });
          }
          break;
        case noPermissionCode:
          router.push({
            path: "/401",
          });
          break;
        default:
          errorMsg(msg || `后端接口${code}异常`);
          break;
      }

      return Promise.reject(
        JSON.stringify({ url: config.url, code, msg }) || "Error"
      );
    } else {
      return data;
    }
  },
  (error) => {
    //2022.2.28---start--- czk添加重复请求处理，新加的判断，解决取消重复请求后会报错的问题
    if (axios.isCancel(error)) {
      console.log('Request canceled', error.message);
      return new Promise(() => { });
    }
    //2022.2.28---end--- czk添加重复请求处理，新加的判断，解决取消重复请求后会报错的问题
    if (loadingInstance) {
      loadingInstance.close();
    }
    /*网络连接过程异常处理*/
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    }
    if (message && message.includes("timeout")) {
      message = "后端接口请求超时";
    }
    if (message && message.includes("Request failed with status code")) {
      message = "后端接口" + message.substr(message.length - 3) + "异常";
    }
    errorMsg(message || "后端接口未知异常");
    return Promise.reject(error);
  }
);

// 通用下载方法
let downloadLoadingInstance;
export function download (url, params, filename) {
  downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      // 'Content-Type': 'application/x-www-form-urlencoded',
      tokenName: store.getters["user/accessToken"]
    },
    responseType: 'blob'
  }).then(async (data) => {
    const isLogin = await blobValidate(data);
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      Message.error(errMsg);
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    Message.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

export default service;
