var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index");
const Service = require("../../services/VirtualClassService");
const ServiceProcess = require("../../services/VirtualProcessService");
const VirtualDataService = require("../../services/VirtualDataService");

router.get("/homePageList", tryAsyncErrors(VirtualDataService.homeList));
router.post("/homePageList", tryAsyncErrors(Service.homePageList));
router.post("/list", tryAsyncErrors(Service.list));
router.post("/menuBeauList", tryAsyncErrors(Service.menuBeauList));
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
// router.delete('/delete/:homePageId',tryAsyncErrors(Service.del))
router.post('/delete', tryAsyncErrors(Service.del))
// router.post('/treeList', tryAsyncErrors(ServiceProcess.treeList))
router.post('/treeList', tryAsyncErrors(VirtualDataService.treeList))
router.post('/upload', tryAsyncErrors(Service.upload))

router.post("/operList", tryAsyncErrors(Service.operList));

module.exports = router;
