import request from "@/utils/request";
// import qs from 'qs'

export default {
  //验证码生成
  comCaptcha: (params) => request({ url: '/common/captcha', method: 'get', params }),
  comConfig: (params) => request({ url: '/common/config', method: 'get', params }),
  gradeNotAssociatedStudentList (params) { return request({ url: '/common/grade/notAssociatedStudentList', method: 'get', params }) },
  getRefreshToken: (params) => request({ url: '/token/getRefreshToken', method: 'get', params }),

  //字典列表
  roleOption: (params) => request({ url: "/dict/roleOption", method: "get", params }),
  roleMenuOption: (params) => request({ url: "/dict/roleMenuOption", method: "get", params }),
  gradeOption: (params) => request({ url: "/dict/gradeOption", method: "get", params }),
  dictOption: (params) => request({ url: "/dict/option", method: "get", params }),
  dictCodeOption: (params) => request({ url: "/dict/codeOption", method: "get", params }),
  dictCodeTreeDataOption: (params) => request({ url: "/dict/codeTreeDataOption", method: "get", params }),

  virtualTreeOption: (params) => request({ url: "/dict/virtualTreeOption", method: "get", params }),
  /* 操作类型下拉列表 */
  operTypeOption: (params) => request({ url: '/dict/operTypeOption', method: 'get', params }),
  pathPrefixOption: (params) => request({ url: '/dict/pathPrefixOption', method: 'get', params }),
  pathWebOption: (params) => request({ url: '/dict/pathWebOption', method: 'get', params }),
  virtualCateOption: (params) => request({ url: '/dict/virtualCateOption', method: 'get', params }),
  virtualDataListOption: (params) => request({ url: '/dict/virtualDataListOption', method: 'get', params }),
  getDictList: (params) => request({ url: '/dict/getDictList', method: 'get', params }),

  /* 仿真数据管理 */
  virtualDataList: (params) => request({ url: '/virtualData/list', method: 'get', params }),
  // virtualDataTreeList: (params) => request({ url: '/virtualData/treeList', method: 'get', params }),
  virtualDataAdd: (params) => request({ url: '/virtualData/add', method: 'post', data: params }),
  virtualDataEdit: (params) => request({ url: '/virtualData/edit', method: 'post', data: params }),
  virtualDataEditBatch: (params) => request({ url: '/virtualData/editBatch', method: 'post', data: params }),
  virtualDataDel: (params) => request({ url: '/virtualData/delete/' + params, method: 'delete' }),
  virtualDataChildDel: (params) => request({ url: '/virtualData/childDel', method: 'post', data: params }),
  virtualDataImport: (params) => request({ url: '/virtualData/uploadImport', method: 'post', data: params, timeout: 10000 }),
  virtualDataExport: (params) => download('/virtualData/export', params),
  virtualDataStatus: (params) => request({ url: '/virtualData/status', method: 'post', data: params }),
  virtualDataSort: (params) => request({ url: '/virtualData/sort', method: 'post', data: params }),
  virtualDataSortValid: (params) => request({ url: '/virtualData/sortValid', method: 'post', data: params }),

  //课程学习
  coursewareLearnList (params) { return request({ url: '/coursewareLearn/list', method: 'get', params }) },
  coursewareLearnOne (params) { return request({ url: '/coursewareLearn/list/' + params, method: 'get', }) },

  /* 操作类型管理 */
  operTypeList: (params) => request({ url: '/operType/list', method: 'get', params }),
  operTypeAdd: (params) => request({ url: '/operType/add', method: 'post', data: params }),
  operTypeEdit: (params) => request({ url: '/operType/edit', method: 'post', data: params }),
  operTypeDel: (params) => request({ url: '/operType/delete/' + params, method: 'delete' }),

  /* web地址管理 */
  pathWebList: (params) => request({ url: '/web/list', method: 'get', params }),
  pathWebAdd: (params) => request({ url: '/web/add', method: 'post', data: params }),
  pathWebEdit: (params) => request({ url: '/web/edit', method: 'post', data: params }),
  pathWebDel: (params) => request({ url: '/web/delete/' + params, method: 'delete' }),

  /* 仿真分类管理 */
  virtualCateList: (params) => request({ url: '/virtualCate/list', method: 'get', params }),
  virtualCateAdd: (params) => request({ url: '/virtualCate/add', method: 'post', data: params }),
  virtualCateEdit: (params) => request({ url: '/virtualCate/edit', method: 'post', data: params }),
  virtualCateDel: (params) => request({ url: '/virtualCate/delete/' + params, method: 'delete' }),
  virtualCateRelevance: (params) => request({ url: '/virtualCate/relevance', method: 'post', data: params }),
  virtualCateNotRelevance: (params) => request({ url: '/virtualCate/notRelevance', method: 'post', data: params }),

  /* 问卷管理 */
  questionnaireList: (params) => request({ url: '/questionnaire/list', method: 'get', params }),
  questionnaireById: (params) => request({ url: '/questionnaire/byId', method: 'get', params }),
  questionnaireAdd: (params) => request({ url: '/questionnaire/add', method: 'post', data: params }),
  questionnaireEdit: (params) => request({ url: '/questionnaire/edit', method: 'post', data: params }),
  questionnaireDel: (params) => request({ url: '/questionnaire/delete/' + params, method: 'delete' }),
  questionnaireOption: (params) => request({ url: '/questionnaire/option', method: 'get', params }),
  /* 问卷数据相关接口 */
  questionnaireDataList: (params) => request({ url: '/questionnaireData/list', method: 'get', params }),
  questionnaireDataById: (params) => request({ url: '/questionnaireData/byId', method: 'get', params }),
  questionnaireDataAdd: (params) => request({ url: '/questionnaireData/add', method: 'post', data: params }),
  questionnaireDataEdit: (params) => request({ url: '/questionnaireData/edit', method: 'post', data: params }),
  questionnaireDataDel: (params) => request({ url: '/questionnaireData/delete/' + params, method: 'delete' }),

  //*****用户登录
  // 登录请求
  login (params) { return request({ url: '/user/login', method: 'post', data: params }) },
  // 查询用户信息
  loginInfo (params) { return request({ url: '/user/getInfo/' + params, method: 'get' }) },

  //*****议题管理
  // 新增议题
  topicsAdd (params) { return request({ url: '/tipArticle/add', method: 'post', data: params }) },
  topicsEdit (params) { return request({ url: '/tipArticle/edit', method: 'post', data: params }) },
  topicsDel (params) { return request({ url: '/tipArticle/delete/' + params, method: 'delete' }) },
  topicsQuery (params) { return request({ url: '/processRecord/articleList', method: 'post', data: params }) },

  //*****议题标签
  // 新增议题标签
  topicsAddTitle (params) { return request({ url: '/tipArticleType/add', method: 'post', data: params }) },
  topicsEditTitle (params) { return request({ url: '/tipArticleType/edit', method: 'post', data: params }) },
  topicsDelTitle (params) { return request({ url: '/tipArticleType/delete/' + params, method: 'delete' }) },
  topicsQueryTitle (params) { return request({ url: '/tipArticleType/list', method: 'post', data: params }) },

  //*****评论管理
  // 新增评论
  commentAdd (params) { return request({ url: '/tipComment/add', method: 'post', data: params }) },
  commentEdit (params) { return request({ url: '/tipComment/edit', method: 'post', data: params }) },
  commentDel (params) { return request({ url: '/tipComment/delete/' + params, method: 'delete' }) },
  commentList (params) { return request({ url: '/tipComment/list', method: 'post', data: params }) },

  //***** 题目管理
  questionAdd (params) { return request({ url: '/question/add', method: 'post', data: params }) },
  questionEdit (params) { return request({ url: '/question/edit', method: 'post', data: params }) },
  questionDel (params) { return request({ url: '/question/delete/' + params, method: 'delete' }) },
  questionOne (params) { return request({ url: '/question/list/' + params, method: 'get' }) },
  questionQuery (params) { return request({ url: '/question/list', method: 'post', data: params }) },

  // 查询题目关联
  queryAssociation (params) { return request({ url: '/processRecord/questionList', method: 'post', data: params }) },
  // 查询题目关联
  delAssociation (params) { return request({ url: '/processRecord/delete/' + params, method: 'delete' }) },
  addAssociation (params) { return request({ url: '/processRecord/add', method: 'post', data: params }) },

  // 查询议题关联
  queryTopicRelevance (params) { return request({ url: '/processRecord/issueList', method: 'post', data: params }) },
  // 查询题目关联
  delTopicRelevance (params) { return request({ url: '/processRecord/delete/' + params, method: 'delete' }) },
  addTopicRelevance (params) { return request({ url: '/processRecord/add', method: 'post', data: params }) },

  //议题管理接口
  issueAdd (params) { return request({ url: '/issue/add', method: 'post', data: params }) },
  issueEdit (params) { return request({ url: '/issue/edit', method: 'post', data: params }) },
  issueDel (params) { return request({ url: '/issue/delete/' + params, method: 'delete' }) },
  issueOne (params) { return request({ url: '/issue/list/' + params, method: 'get' }) },
  issueQuery (params) { return request({ url: '/issue/list', method: 'get', params }) },
  issueList (params) { return request({ url: '/issue/list', method: 'get', params }) },
  issueAssociation (params) { return request({ url: '/processRecord/issueList', method: 'post', data: params }) },

  // 查询议题关联
  submitTestPaperApi (params) { return request({ url: '/userAnswer/add', method: 'post', data: params }) },
  // 查询题目关联
  queryAnswer (params) { return request({ url: '/userAnswer/list', method: 'post', data: params }) },

  //学生信息
  studentList (params) { return request({ url: '/student/list', method: 'get', params }) },
  studentAdd (params) { return request({ url: '/student/add', method: 'post', data: params }) },
  studentEdit (params) { return request({ url: '/student/edit', method: 'post', data: params }) },
  studentDel (params) { return request({ url: '/student/delete/' + params, method: 'delete' }) },
  studentResetPwd (params) { return request({ url: '/student/resetPwd', method: 'post', data: params }) },
  studentImport (data) { return request({ url: '/student/uploadImport', method: 'post', data, }) },
  studentExport (params) { return request({ url: '/student/export', method: 'post', data: params, responseType: 'blob' }) },

  // 用户管理
  // 查询单个的用户
  userSearch (params) { return request({ url: '/user/getInfo/' + params, method: 'get' }) },
  userInfo (params) { return request({ url: '/user/info', method: 'get' }) },
  uploadAvatar (data) { return request({ url: '/user/uploadAvatar', method: 'post', data, }) },
  uploadUserAvatar (data) { return request({ url: '/user/uploadUserAvatar', method: 'post', data, }) },
  // 查询所有的用户
  userList (params) { return request({ url: '/user/list', method: 'get', params }) },
  userAdd (params) { return request({ url: '/user/add', method: 'post', data: params }) },
  userEdit (params) { return request({ url: '/user/edit', method: 'post', data: params }) },
  userStatusChange (params) { return request({ url: '/user/statusChange', method: 'post', data: params }) },
  userEditInfo (params) { return request({ url: '/user/editInfo', method: 'post', data: params }) },
  importUser (data) { return request({ url: '/user/uploadImport', method: 'post', data, }) },
  userEditPwd (params) { return request({ url: '/user/pwdEdit', method: 'post', data: params }) },
  userDel (params) { return request({ url: '/user/delete/' + params, method: 'delete' }) },
  userResetPwd (params) { return request({ url: '/user/resetPwd', method: 'post', data: params }) },

  // 角色菜单
  roleList (params) { return request({ url: '/role/list', method: 'post', data: params }) },
  roleAdd (params) { return request({ url: '/role/add', method: 'post', data: params }) },
  roleEdit (params) { return request({ url: '/role/edit', method: 'post', data: params }) },
  roleDel (params) { return request({ url: '/role/delete/' + params, method: 'delete' }) },
  ///
  //字典管理
  dictList: (params) => request({ url: "/dict/list", method: "get", params }),
  dictAdd: (params) => request({ url: "/dict/add", method: "post", data: params }),
  dictEdit: (params) => request({ url: "/dict/edit", method: "post", data: params }),
  dictDel: (params) => request({ url: "/dict/delete/" + params, method: "delete", params }),

  //字典数据管理
  dictDataList: (params) => request({ url: "/dictData/list", method: "get", params }),
  dictDataAdd: (params) => request({ url: "/dictData/add", method: "post", data: params }),
  dictDataEdit: (params) => request({ url: "/dictData/edit", method: "post", data: params }),
  dictDataDel: (params) => request({ url: "/dictData/delete/" + params, method: "delete", params }),

  //菜单管理
  menuList (params) { return request({ url: '/menu/menuList', method: 'get', params }) },
  menuAdd (params) { return request({ url: '/menu/add', method: 'post', data: params }) },
  menuEdit (params) { return request({ url: '/menu/edit', method: 'post', data: params }) },
  menuDel (params) { return request({ url: '/menu/delete/' + params, method: 'delete' }) },

  //虚拟课程中心前端界面
  virtualCourseList (params) { return request({ url: '/virtualCourse/list', method: 'get', params }) },

  //2022.1.19 czk start
  //登录日志接口
  loginLogList (params) { return request({ url: '/log/loginLogList', method: 'get', params }) },
  //操作日志接口
  operLogList (params) { return request({ url: '/log/operLogList', method: 'get', params }) },

  userSerialList (params) { return request({ url: '/user/serialList', method: 'get', params }) },
  //用户仿真操作记录
  userOperList (params) { return request({ url: '/userOper/list', method: 'get', params }) },
  userOperAdd (params) { return request({ url: '/userOper/add', method: 'post', data: params }) },
  userOperEdit (params) { return request({ url: '/userOper/edit', method: 'post', data: params }) },
  userOperDel (params) { return request({ url: '/userOper/delete/' + params, method: 'delete' }) },

  //年级管理
  gradeList (params) { return request({ url: '/grade/list', method: 'get', params }) },
  gradeStudentList (params) { return request({ url: '/grade/studentList', method: 'get', params }) },
  gradeStudentDel (params) { return request({ url: '/grade/studentDel/' + params, method: 'delete' }) },
  addAssociationStudent (params) { return request({ url: '/grade/addAssociationStudent', method: 'post', data: params }) },
  gradeAdd (params) { return request({ url: '/grade/add', method: 'post', data: params }) },
  gradeEdit (params) { return request({ url: '/grade/edit', method: 'post', data: params }) },
  gradeDel (params) { return request({ url: '/grade/delete/' + params, method: 'delete' }) },
  //班级管理
  classList (params) { return request({ url: '/class/list', method: 'get', params }) },
  classAdd (params) { return request({ url: '/class/add', method: 'post', data: params }) },
  classEdit (params) { return request({ url: '/class/edit', method: 'post', data: params }) },
  classDel (params) { return request({ url: '/class/delete/' + params, method: 'delete' }) },
  //学科管理
  subjectList (params) { return request({ url: '/subject/list', method: 'get', params }) },
  subjectAdd (params) { return request({ url: '/subject/add', method: 'post', data: params }) },
  subjectEdit (params) { return request({ url: '/subject/edit', method: 'post', data: params }) },
  subjectDel (params) { return request({ url: '/subject/delete/' + params, method: 'delete' }) },
  //流程题目记录
  recordList (params) { return request({ url: '/record/list', method: 'get', params }) },
  recordAdd (params) { return request({ url: '/record/add', method: 'post', data: params }) },
  recordEdit (params) { return request({ url: '/record/edit', method: 'post', data: params }) },
  recordDel (params) { return request({ url: '/record/delete/' + params, method: 'delete' }) },
  recordStatusChange (params) { return request({ url: '/record/statusChange', method: 'post', data: params }) },
  recordDefaultStatusChange (params) { return request({ url: '/record/defaultStatusChange', method: 'post', data: params }) },
  //虚拟程序路径管理
  pathList (params) { return request({ url: '/path/list', method: 'get', params }) },
  pathAdd (params) { return request({ url: '/path/add', method: 'post', data: params }) },
  pathEdit (params) { return request({ url: '/path/edit', method: 'post', data: params }) },
  pathDel (params) { return request({ url: '/path/delete/' + params, method: 'delete' }) },
  //课程列表
  coursewareList (params) { return request({ url: '/courseware/list', method: 'get', params }) },
  coursewareAdd (params) { return request({ url: '/courseware/add', method: 'post', data: params }) },
  coursewareEdit (params) { return request({ url: '/courseware/edit', method: 'post', data: params }) },
  coursewareDel (params) { return request({ url: '/courseware/delete/' + params, method: 'delete' }) },
  coursewareSourceDel (params) { return request({ url: '/courseware/sourceDelete', method: 'post', data: params }) },
  coursewareOne (params) { return request({ url: '/courseware/list/' + params, method: 'get' }) },
  //2022.1.19 czk end

  // 2024.9.26
  configInfo (params) { return request({ url: '/config/info', method: 'get', params }) },
  configEdit (params) { return request({ url: '/config/edit', method: 'post', data: params }) },

  registerList (params) { return request({ url: '/register/list', method: 'get', params }) },
  registerAdd (params) { return request({ url: '/register/add', method: 'post', data: params }) },
  registerApproval (params) { return request({ url: '/register/approval', method: 'post', data: params }) },
  registerBatchApproval (params) { return request({ url: '/register/approvalBatch', method: 'post', data: params }) },

  // AI聊天相关接口
  aiChatRemaining(params) { return request({ url: '/ai-chat/remaining', method: 'get', params }) },
  aiChatSend(params) { return request({ url: '/ai-chat/send', method: 'post', data: params }) },
  aiChatHistory(params) { return request({ url: '/ai-chat/history', method: 'get', params }) },

};
