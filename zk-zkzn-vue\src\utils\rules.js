/*
 * @Author: eay
 * @Date: 2021-01-14 15:28:45
 * @LastEditTime: 2021-07-26 16:06:54
 * @LastEditors: eay
 * @Description:
 * @FilePath: \predict\src\utils\rules.js
 * @Keep moving.Don't settle.
 */
// // 类似金钱,首位不为0,最多2位小数
// export function checkNumPot2(rule, value, callback) {
//   const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
//   if (!value) {
//     return callback(new Error('请填写数字'))
//   } else if (!reg.test(value)) {
//     return callback(new Error('请填写数字,最多2位小数'))
//   } else {
//     callback()
//   }
// }

// 整数
export function checkInterNum(rule, value, callback) {
  const reg = /^[0-9]*[1-9][0-9]*$/
  if (!value) {
    return callback(new Error('请填写整数'))
  } else if (!reg.test(value)) {
    return callback(new Error('请输入整数'))
  } else {
    callback()
  }
}
let answerValid = (rule, value, callback) => {
  console.log(rule, value,'rule, value');
  if (value === '') {
    callback(new Error('请输入密码'));
  } else {
    callback();
  }
};
// var checkNumber = (rule, value, callback) => {
//   if (!value) {
//     return callback()
//   } else if (/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value) == false) {
//     return callback(new Error('输入数字,可保留两位小数'))
//   } else {
//     return callback()
//   }
// }
export default {
  menu: {
    menuName: [{ required: true, message: '请输入名称' }],
    menuType: [{ required: true, message: '请选择类型' }],
    path: [{ required: true, message: '请输入路由地址' }],
    perms: [{ required: true, message: '请输入授权标识' }],
    orderBy: [{ required: true, message: '请选择序号' }],
  },
  homePageOne: {
    // menuName: [{ required: true, message: '请输入名称' }],
    // menuType: [{ required: true, message: '请选择类型' }],
    // path: [{ required: true, message: '请输入路由地址' }],
    // perms: [{ required: true, message: '请输入授权标识' }],
    // orderBy: [{ required: true, message: '请选择序号' }],
  },
  indicatorRules: {
    title: [{ required: true, message: '请输入指标名称', trigger: 'change' }],
    // isPrediction: [{ required: true, trigger: 'blur' }],
    dynamicType: [{ required: true, message: '请选择指标类型', trigger: 'change' }],
    baseWarshipTypes: [{ required: true, message: '请选择所属舰类', trigger: 'change' }],
  },
  shipClassRules: {
    warshipTypeName: [{ required: true, message: '请输入舰类名称', trigger: 'change' }],
    baseDynamicTableTitles: [{ required: true, message: '请选择所属指标', trigger: 'change' }],
  },
  AdministratorRules: {
    roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }],
    userName: [{ required: true, message: '请输入姓名', trigger: 'change' }],
    nickName: [{ required: true, message: '请输入昵称', trigger: 'change' }],
    phoneNumber: [{ required: true, message: '请输入手机号', trigger: 'change' }],
    email: [{ required: true, message: '请输入邮箱', trigger: 'change' }],

    password: [{ required: true, message: '请输入密码', trigger: 'change' }],
  },
  rolesRules: {
    roleName: [{ required: true, message: '请输入姓名', trigger: 'change' }],
    menuIds: [{ required: true, message: '请选择菜单', trigger: 'change' }],
    // userName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  },
  expenseInforRules: {
    title: [{ required: true, message: '请输入名称', trigger: 'change' }],
    children: [{ required: true, message: '请选择标题', trigger: 'change' }],
    // remark: [{ required: true, message: '请输入说明', trigger: 'blur' }],
  },
  dictOneRules: {
    dictName: [{ required: true, message: '请输入字典名称', trigger: 'change' }],
    dictType: [{ required: true, message: '请输入字典类型', trigger: 'change' }],
    // textarea: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  },
  dictTwoRules: {
    dictLabel: [{ required: true, message: '请输入字典标签', trigger: 'change' }],
    dictValue: [{ required: true, message: '请输入字典值', trigger: 'change' }],
    dictSort: [{ required: true, message: '请输入整数', trigger: 'change' }],
    dictType: [{ required: true, message: '请输入字典类型', trigger: 'change' }],
  },
  developmentFeeRules: {
    developmentStage: [{ required: true, message: '请输入研制阶段' }],
    startTime: [{ required: true, message: '请选择开始时间' }],
    endTime: [{ required: true, message: '请选择结束时间' }],
    expenditure: [{ required: true, message: '请输入总费用' }],
    priceYear: [{ required: true, message: '请输入折算年度' }],
  },
  purchaseCostRules: {
    acquisitionStage: [{ required: true, message: '请输入购置阶段' }],
    startTime: [{ required: true, message: '请选择开始时间' }],
    endTime: [{ required: true, message: '请选择结束时间' }],
    expenditure: [{ required: true, message: '请输入总费用' }],
    priceYear: [{ required: true, message: '请输入折算年度' }],
  },
  maintenanceFeesRules: {
    maintenanceLevel: [{ required: true, message: '请选择修别' }],
    maintenanceUnit: [{ required: true, message: '请输入承修单位' }],
    startTime: [{ required: true, message: '请选择开始时间' }],
    endTime: [{ required: true, message: '请选择结束时间' }],
    expenditure: [{ required: true, message: '请输入总费用' }],
    priceYear: [{ required: true, message: '请输入折算年度' }],
  },
  usageFeeRules: {
    // maintenanceLevel: [{ required: true, message: '请选择修别', trigger: 'change' }],
    // maintenanceUnit: [{ required: true, message: '请输入承修单位', trigger: 'change' }],
    startTime: [{ required: true, message: '请选择开始时间' }],
    endTime: [{ required: true, message: '请选择结束时间' }],
    expenditure: [{ required: true, message: '请输入总费用' }],
  },
  addModelRules: {
    // maintenanceLevel: [{ required: true, message: '请选择修别', trigger: 'change' }],
    // maintenanceUnit: [{ required: true, message: '请输入承修单位', trigger: 'change' }],
    title: [{ required: true, message: '请输入阶段分系统名称' }],
    // endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
    // expenditure: [{ required: true, message: '请输入总费用', trigger: 'change' }],
  },

  // 2021.11.2 czk新增的角色效验规则
  userRules:{
    userName: [{ required: true, message: '请输入用户名', trigger: 'change' }],
    password: [{ required: true, message: '请输入密码', trigger: 'change' }]
  },
  roleRules:{
    roleName: [{ required: true, message: '请输入角色名称', trigger: 'change' }]
  },
  virtualClassRules:{
    // name: [{ required: true, message: '请输入名称', trigger: 'change' }],
    // linkSrc: [{ required: true, message: '请输入外链路径', trigger: 'change' }],
    // parentId: [{ required: true, message: '请选择上级', trigger: 'change' }]    
  },
  questionRules:{
    classType: [{ required: true, message: '请选择所属工艺流程', trigger: 'change' }],
    questionContent: [{ required: true, message: '请输入题干', trigger: 'change' }],
    // questionAnswers: [{ required: true,type: 'array',message: '请至少选择一个活动性质', trigger: 'change' }]    
  },
  virtualDataRules:{
    virtualName: [{ required: true, message: '请输入名称', trigger: 'change' }],
    virtualId: [{ required: true, message: '请输入进程id', trigger: 'change' }],
    virtualParentId: [{ required: true, message: '请输入父进程id', trigger: 'change' }],   
  },
}
