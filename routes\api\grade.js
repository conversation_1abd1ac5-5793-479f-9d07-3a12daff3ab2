var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/GradeService");

router.get('/list', tryAsyncErrors(Service.list))
router.get('/studentList', tryAsyncErrors(Service.studentList))
router.delete('/studentDel/:id', tryAsyncErrors(Service.studentDel))
router.get('/notAssociatedStudentList', tryAsyncErrors(Service.notAssociatedStudentList))
router.post('/addAssociationStudent', tryAsyncErrors(Service.addAssociationStudent))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

module.exports = router;
