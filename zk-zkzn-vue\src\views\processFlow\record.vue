<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="记录名称" prop="recordName">
        <el-input placeholder="请输入记录名称" v-model="formSearch.recordName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="记录状态" clearable size="small">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['record/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <template v-slot:statusSlot="{ row }">
        <vxe-switch v-model="row.status" :disabled="row.status==1" open-label="启用" :open-value="1" close-label="停用" :close-value="0" @change="statusChange(row)"></vxe-switch>
      </template>
      <template v-slot:defaultStatusSlot="{ row }">
        <vxe-switch v-model="row.defaultStatus" :disabled="row.defaultStatus==1" open-label="启用" :open-value="1" close-label="停用" :close-value="0" @change="defaultStatusChange(row)"></vxe-switch>
      </template>
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['processRecord/questionList']" icon="el-icon-setting" @click="handleConfig(row)">配置流程题目</el-button>
        <el-button type="text" v-permissions="['record/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['record/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.recordRules" title-width="120" title-align="right"></vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  name: "Record",
  data () {
    return {
      srcList: [],
      modalTitle: '新增记录',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'recordName', title: '记录名称' },
        { sortable: true, field: 'gradeName', title: '所属届/年级' },
        // { sortable: true, field: 'className', title: '所属班级' },
        // { sortable: true, field: 'roleName', title: '角色', formatter: ({ row }) => { return row.roleIds.map(item => item.roleName).join(',') } },
        { sortable: true, field: 'createBy', title: '创建人' },
        { field: 'status', title: '状态', slots: { default: 'statusSlot' } },
        { field: 'defaultStatus', title: '默认流程', slots: { default: 'defaultStatusSlot' } },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { field: 'remark', title: '备注' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        recordName: '',
        gradeId: '',
        classId: undefined,
        status: 0,
        remark: '',
      },
      formItem: [
        { field: 'recordName', title: '记录名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'gradeId', title: '届/年级', span: 24, itemRender: { name: '$select', props: { placeholder: '请选择届/年级' }, options: [], optionProps: { value: 'value', label: 'label' }, } },
        // { field: 'classId', title: '班级', span: 24, itemRender: { name: '$select', props: { multiple: true, placeholder: '请选择' }, options: [], optionProps: { value: 'id', label: 'className' }, } },
        // { field: 'status', title: '记录状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$textarea', props: { placeholder: '请输入备注' } } },
      ],
      formSearch: {
        recordName: '',
        status: undefined,
        page: 1,
        pageSize: 10
      },
      statusOptions: [
        { value: '0', label: '未启用' },
        { value: '1', label: '启用' },
      ],
      gradeOption: [],
      classOption: [],
    };
  },
  created () {
    this.getGradeOption()
    // this.getClassList()
    this.getData();
  },
  methods: {
    getGradeOption () {
      this.$http.gradeOption().then(res => {
        this.formItem[1].itemRender.options = res.data
        this.gradeOption = res.data
      })
    },
    // getClassList () {
    //   this.$http.classList().then(res => {
    //     this.classOption = res.data
    //   })
    // },
    getData (params) {
      //记录列表
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.recordList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    statusChange (row) {
      this.loading = true;
      this.$http.recordStatusChange(row).then(res => {
        this.loading = false;
        this.getData()
        this.$msg(res.msg, 'success')
      });
    },
    defaultStatusChange (row) {
      this.loading = true;
      this.$http.recordDefaultStatusChange(row).then(res => {
        this.loading = false;
        this.getData()
        this.$msg(res.msg, 'success')
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        console.log(visible, 'visible');
        if (!visible) {
          if (this.modalTitle == '新增记录') {
            this.$http.recordAdd(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.recordEdit(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleAdd () {
      this.modalTitle = '新增记录';
      this.modalVisible = true;
      this.formData = {
        recordName: '',
        gradeId: '',
        classId: undefined,
        status: 0,
        remark: '',
      };
      // 确保每次打开新增模态框时都获取最新的年级数据
      this.getGradeOption();
    },
    handleEdit (row) {
      this.modalTitle = '编辑记录';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
      // 编辑时也确保有年级数据
      this.getGradeOption();
    },
    handleConfig (row) {
      this.$router.push({ path: '/processFlow/recordConfig', query: { recordId: row.id } })
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.recordDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>


