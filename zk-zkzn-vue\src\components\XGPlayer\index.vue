```vue
<template>
  <div class="xgplayer-wrapper">
    <div class="xgplayer-container" ref="playerContainer"></div>
  </div>
</template>

<script>
import Player from 'xgplayer';
// 注释掉HLS插件导入但保留代码
// import HlsPlugin from 'xgplayer-hls';
import 'xgplayer/dist/index.min.css';

export default {
  name: 'XGPlayer',
  props: {
    src: { type: String, required: true },
    poster: { type: String, default: '' },
    width: { type: [String, Number], default: '100%' },
    height: { type: [String, Number], default: '100%' },
    autoplay: { type: Boolean, default: false },
    loop: { type: Boolean, default: false },
    muted: { type: Boolean, default: false },
    volume: { type: Number, default: 0.6 },
    controls: { type: Boolean, default: true },
    disableContextmenu: { type: Boolean, default: true },
    playbackRates: { type: Array, default: () => [0.5, 0.75, 1, 1.25, 1.5, 2] },
    fullscreenBtn: { type: Boolean, default: true },
    pip: { type: Boolean, default: false },
  },
  data() {
    return {
      player: null,
      playerId: 'xgplayer-' + Date.now(),
      isControlsVisible: false,
      hideTimeout: null,
      moveTimeout: null,
      observer: null,
      rafId: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.initPlayer();
      }, 300);
    });
  },
  beforeDestroy() {
    this.destroyPlayer();
  },
  methods: {
    initPlayer() {
      const container = this.$refs.playerContainer;
      if (!container) {
        console.error('播放器容器DOM未找到');
        return;
      }

      container.id = this.playerId;

      try {
        // 注释掉HLS相关代码但保留它
        // const isHLS = this.src.includes('.m3u8');
        // const hasNativeHlsSupport = document.createElement('video').canPlayType('application/vnd.apple.mpegurl') !== '';

        console.log('初始化播放器', { src: this.src });

        const playerConfig = {
          id: this.playerId,
          url: this.src,
          poster: this.poster,
          width: this.width,
          height: this.height,
          autoplay: this.autoplay,
          loop: this.loop,
          volume: this.volume,
          videoInit: true,
          ignores: [],
          controls: this.controls ? {
            mode: 'top',
            initShow: false,
            autoHide: true,
            autoHideTime: 3000,
            controlsList: ['play', 'progress', 'time', 'volume', 'fullscreen', 'playbackrate'],
            css: true,
            disableAutoControl: true,
          } : false,
          playbackRate: this.playbackRates,
          playsinline: true,
          fluid: true,
          lang: 'zh-cn',
          fitVideo: true,
          cssFullscreen: false,
        };

        console.log('播放器配置:', JSON.stringify(playerConfig.controls));

        // 注释掉HLS相关代码，直接使用标准播放器播放原mp4
        // if (isHLS && !hasNativeHlsSupport && HlsPlugin.isSupported()) {
        //   console.log('使用xgplayer-hls插件播放HLS视频');
        //   this.player = new Player({
        //     ...playerConfig,
        //     plugins: [HlsPlugin],
        //     hls: {
        //       retryCount: 5,
        //       retryDelay: 1000,
        //       loadTimeout: 15000,
        //       disconnectTime: 10,
        //       maxLatency: 30,
        //       targetLatency: 15,
        //     },
        //   });
        // } else {
          console.log('使用标准播放器播放原视频');
          this.player = new Player(playerConfig);
        // }

        this.bindEvents();
        this.applyExtraProtection();
        this.observeControls();
        this.startControlCheck();
      } catch (error) {
        console.error('初始化播放器失败:', error);
        this.$emit('error', error);
      }
    },

    bindEvents() {
      if (!this.player) return;

      try {
        this.player.on('play', () => {
          console.log('视频开始播放');
          this.$emit('play');
          this.hideControls();
          this.startHideTimeout();
        });

        this.player.on('pause', () => {
          console.log('视频已暂停');
          this.$emit('pause');
          this.showControls();
        });

        this.player.on('ended', () => {
          console.log('视频播放结束');
          this.$emit('ended');
          this.showControls();
        });

        this.player.on('error', (err) => {
          console.error('播放器错误:', err, {
            error: this.player.video?.error,
            networkState: this.player.video?.networkState,
            readyState: this.player.video?.readyState,
            src: this.player.video?.src,
            currentSrc: this.player.video?.currentSrc,
          });
          this.$emit('error', err);
        });

        // 注释掉HLS相关的事件监听        // this.player.on('core_event', ({ eventName, ...detail }) => {        //   console.log('HLS事件:', eventName, detail);        //   if (eventName === 'STREAM_EXCEPTION') {        //     console.error('流异常:', detail);        //   }        // });

        this.player.on('ready', () => {
          console.log('播放器准备就绪');
          this.$emit('ready');
          setTimeout(() => {
            this.hideControls();
            console.log('ready事件触发隐藏控制栏');
          }, 500);
        });

        this.player.on('controlsshow', () => {
          console.log('controlsshow触发, isControlsVisible:', this.isControlsVisible);
          if (!this.isControlsVisible) {
            this.hideControls();
          }
        });

        this.player.on('controlshide', () => {
          console.log('控制栏隐藏');
          this.isControlsVisible = false;
        });

        this.player.on('loadedmetadata', () => {
          console.log('视频元数据加载完成');
          const video = this.player.video;
          const videoAspectRatio = video.videoWidth / video.videoHeight;
          const container = this.$refs.playerContainer;
          const containerAspectRatio = container.offsetWidth / container.offsetHeight;
          video.style.objectFit = Math.abs(videoAspectRatio - containerAspectRatio) > 0.1 ? 'cover' : 'contain';
        });

        const playerEl = document.getElementById(this.playerId);
        if (playerEl) {
          playerEl.addEventListener('mousemove', () => {
            clearTimeout(this.moveTimeout);
            this.showControls();
            this.moveTimeout = setTimeout(() => {
              if (!this.player.paused) {
                this.hideControls();
              }
            }, 3000);
          });

          playerEl.addEventListener('mouseover', () => {
            this.showControls();
          });

          playerEl.addEventListener('mouseout', () => {
            if (!this.player.paused) {
              this.hideControls();
            }
          });

          playerEl.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.isControlsVisible = !this.isControlsVisible;
            if (this.isControlsVisible) {
              this.showControls();
              if (!this.player.paused) {
                this.startHideTimeout();
              }
            } else {
              this.hideControls();
            }
          });
        }
      } catch (error) {
        console.error('绑定事件失败:', error);
      }
    },

    observeControls() {
      const playerEl = document.getElementById(this.playerId);
      if (!playerEl) return;

      const controlsEl = playerEl.querySelector('.xgplayer-controls');
      if (!controlsEl) {
        console.warn('控制栏DOM未找到，延迟重试');
        setTimeout(() => this.observeControls(), 500);
        return;
      }

      this.observer = new MutationObserver(() => {
        if (!this.isControlsVisible && (controlsEl.style.opacity !== '0' || controlsEl.style.visibility !== 'hidden' || controlsEl.style.display !== 'none')) {
          console.log('MutationObserver检测到控制栏显示，强制隐藏');
          controlsEl.style.opacity = '0';
          controlsEl.style.visibility = 'hidden';
          controlsEl.style.display = 'none';
        }
      });

      this.observer.observe(controlsEl, {
        attributes: true,
        attributeFilter: ['style', 'class', 'display'],
        subtree: true,
      });
    },

    startControlCheck() {
      const checkControls = () => {
        if (!this.player || !this.isControlsVisible) {
          const playerEl = document.getElementById(this.playerId);
          if (playerEl) {
            const controlsEl = playerEl.querySelector('.xgplayer-controls');
            if (controlsEl && (controlsEl.style.opacity !== '0' || controlsEl.style.visibility !== 'hidden' || controlsEl.style.display !== 'none')) {
              console.log('RAF检测到控制栏显示，强制隐藏');
              controlsEl.style.opacity = '0';
              controlsEl.style.visibility = 'hidden';
              controlsEl.style.display = 'none';
            }
          }
        }
        this.rafId = requestAnimationFrame(checkControls);
      };
      this.rafId = requestAnimationFrame(checkControls);
    },

    showControls() {
      const playerEl = document.getElementById(this.playerId);
      if (playerEl) {
        const controlsEl = playerEl.querySelector('.xgplayer-controls');
        if (controlsEl) {
          controlsEl.style.opacity = '1';
          controlsEl.style.visibility = 'visible';
          controlsEl.style.display = 'flex';
        }
      }
      clearTimeout(this.hideTimeout);
      this.isControlsVisible = true;
    },

    hideControls() {
      const playerEl = document.getElementById(this.playerId);
      if (playerEl) {
        const controlsEl = playerEl.querySelector('.xgplayer-controls');
        if (controlsEl) {
          controlsEl.style.opacity = '0';
          controlsEl.style.visibility = 'hidden';
          controlsEl.style.display = 'none';
        }
      }
      clearTimeout(this.hideTimeout);
      this.isControlsVisible = false;
    },

    startHideTimeout() {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = setTimeout(() => {
        if (!this.player.paused) {
          this.hideControls();
        }
      }, 3000);
    },

    applyExtraProtection() {
      try {
        if (this.disableContextmenu) {
          const playerEl = document.getElementById(this.playerId);
          if (playerEl) {
            playerEl.addEventListener('contextmenu', (e) => {
              e.preventDefault();
              return false;
            });
          }
        }
      } catch (error) {
        console.error('应用保护措施失败:', error);
      }
    },

    destroyPlayer() {
      if (this.observer) {
        this.observer.disconnect();
        this.observer = null;
      }
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
        this.rafId = null;
      }
      if (this.player) {
        try {
          this.player.destroy();
        } catch (error) {
          console.error('销毁播放器失败:', error);
        }
        this.player = null;
      }
      clearTimeout(this.hideTimeout);
      clearTimeout(this.moveTimeout);
    },

    play() {
      if (this.player && typeof this.player.play === 'function') {
        try {
          this.player.play();
        } catch (error) {
          console.error('播放失败:', error);
        }
      }
    },

    pause() {
      if (this.player && typeof this.player.pause === 'function') {
        try {
          this.player.pause();
        } catch (error) {
          console.error('暂停失败:', error);
        }
      }
    },

    toggle() {
      if (!this.player) return;
      try {
        if (this.player.paused) {
          this.play();
        } else {
          this.pause();
        }
      } catch (error) {
        console.error('切换播放状态失败:', error);
      }
    },

    seek(time) {
      if (this.player && typeof this.player.currentTime !== 'undefined') {
        try {
          this.player.currentTime = time;
        } catch (error) {
          console.error('跳转失败:', error);
        }
      }
    },

    setVolume(volume) {
      if (this.player && typeof this.player.volume !== 'undefined') {
        try {
          this.player.volume = volume;
        } catch (error) {
          console.error('设置音量失败:', error);
        }
      }
    },
  },
  watch: {
    src(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.destroyPlayer();
        this.$nextTick(() => {
          setTimeout(() => {
            this.initPlayer();
          }, 300);
        });
      }
    },
  },
};
</script>

<style scoped>
.xgplayer-wrapper,
.xgplayer-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.xgplayer-container {
  background-color: #000;
}

.xgplayer-container video {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 默认使用 cover，可根据需要调整为 contain */
  display: block;
}

/* 隐藏下载按钮 */
.xgplayer-download {
  display: none;
}

/* 控制栏基础样式 */
.xgplayer .xgplayer-controls,
.xgplayer-skin-default .xgplayer-controls {
  display: flex;
  visibility: hidden;
  opacity: 0;
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease, visibility 0.3s ease;
  pointer-events: auto;
}

/* 鼠标悬停显示控制栏 */
.xgplayer-wrapper:hover .xgplayer-controls,
.xgplayer-wrapper:hover .xgplayer-skin-default .xgplayer-controls {
  visibility: visible;
  opacity: 1;
}

/* 全屏模式样式优化 */
:fullscreen .xgplayer,
:-webkit-full-screen .xgplayer,
:-moz-full-screen .xgplayer,
:-ms-fullscreen .xgplayer {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  background: #000 !important;
}

:fullscreen .xgplayer-container,
:-webkit-full-screen .xgplayer-container,
:-moz-full-screen .xgplayer-container,
:-ms-fullscreen .xgplayer-container {
  width: 100% !important;
  height: 100% !important;
}

:fullscreen .xgplayer-container video,
:-webkit-full-screen .xgplayer-container video,
:-moz-full-screen .xgplayer-container video,
:-ms-fullscreen .xgplayer-container video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover; /* 确保视频铺满屏幕 */
}

/* 控制栏在全屏模式下的调整 */
:fullscreen .xgplayer-controls,
:-webkit-full-screen .xgplayer-controls,
:-moz-full-screen .xgplayer-controls,
:-ms-fullscreen .xgplayer-controls {
  width: 100%;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7); /* 更深的背景以提高可读性 */
}

/* 禁用用户选择 */
.xgplayer {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 隐藏浏览器原生下载按钮 */
video::-internal-media-controls-download-button {
  display: none;
}

video::-webkit-media-controls-enclosure {
  overflow: hidden;
}

video::-webkit-media-controls-panel {
  width: calc(100% + 30px);
}

/* 自定义控制栏图标文本 */
.xgplayer .xgplayer-icon-play:before { content: '播放'; }
.xgplayer .xgplayer-icon-pause:before { content: '暂停'; }
.xgplayer .xgplayer-icon-fullscreen:before { content: '全屏'; }
.xgplayer .xgplayer-icon-exit-fullscreen:before { content: '退出全屏'; }
.xgplayer .xgplayer-icon-volume:before { content: '音量'; }
.xgplayer .xgplayer-icon-mute:before { content: '静音'; }
</style>
```