const fs =require('fs')
const readline = require('readline')
//text文本转化为array
function readTxtFileToArr (params, path, cb) {
  let fRead = fs.createReadStream(path)
  let objReadline = readline.createInterface({
    input: fRead,
  })
  let arr = new Array()
  objReadline.on('line', function (line) {
    arr.push(line)
  })
  objReadline.on('close', function () {
    let head = arr[1].split('\t')
    let info = arr[3]
    switch (params) {
      case 1:
        if (info.indexOf('实体编号') > -1 && info.indexOf('资源名称') > -1 && info.indexOf('实验名称') > -1 && info.indexOf('使用设备') > -1) {
          let i_id = head.findIndex((v) => { return v == 'Id' })
          let i_assetName = head.findIndex((v) => { return v == 'AssetName' })
          let i_name = head.findIndex((v) => { return v == 'Name' })
          let i_deviceIds = head.findIndex((v) => { return v == 'DeviceIDs' })
          let i_procInfo = head.findIndex((v) => { return v == 'ProcInfo' })
          if(!i_id || !i_assetName || !i_name || !i_deviceIds || !i_procInfo) return cb('未检测到Id，AssetName，Name，DeviceIDs，ProcInfo中的某些字段，请使用规定文件VirtaulProcess.txt')
          let initData = arr.map((item) => {
            if (item.indexOf('#') == -1) {
              let line = item.split('\t')
              return [line[i_id], line[i_assetName], line[i_name], line[i_deviceIds].replace(/_/g,','), line[i_procInfo]]
            }
          }).filter(v=>{return v !=null})
          cb(null, initData)
        } else {
          return cb('未检测到实体编号，资源名称，实验名称等信息，请使用规定文件VirtaulProcess.txt')
        }
        break
      case 2:
        if (info.indexOf('实体编号') > -1 && info.indexOf('资源名称') > -1 && info.indexOf('设备名称') > -1 && info.indexOf('部件序列') > -1 && info.indexOf('操作序列') > -1 && info.indexOf('设备说明') > -1) {
          let i_id = head.findIndex((v) => { return v == 'Id' })
          let i_assetName = head.findIndex((v) => { return v == 'AssetName' })
          let i_processName = head.findIndex((v) => { return v == 'ProcessName' })
          let i_deviceName = head.findIndex((v) => { return v == 'DeviceName' })
          let i_position = head.findIndex((v) => { return v == 'Position' })
          let i_rotation = head.findIndex((v) => { return v == 'Rotation' })
          let i_scaling = head.findIndex((v) => { return v == 'Scaling' })
          let i_unitsIds = head.findIndex((v) => { return v == 'UnitsIDs' })
          let i_operationIds = head.findIndex((v) => { return v == 'OperationIDs' })
          let i_processInfo = head.findIndex((v) => { return v == 'ProcessInfo' })
          let i_deviceInfo = head.findIndex((v) => { return v == 'DeviceInfo' })
          if(!i_id || !i_assetName || !i_processName || !i_deviceName || !i_position || !i_rotation || !i_scaling || !i_unitsIds || !i_operationIds || !i_processInfo || !i_deviceInfo) 
           return cb('未检测到Id，AssetName，ProcessName，DeviceName，Position，Rotation，Scaling，UnitsIDs，OperationIDs，ProcessInfo，DeviceInfo，中的某些字段，请使用规定文件VirtaulProcess.txt')
          let initData = arr.map((item) => {
            if (item.indexOf('#') == -1) {
              let line = item.split('\t')
              return [line[i_id], line[i_assetName], line[i_processName], line[i_deviceName], line[i_position],line[i_rotation], line[i_scaling], line[i_unitsIds].replace(/_/g,','), line[i_operationIds].replace(/_/g,','), line[i_processInfo], line[i_deviceInfo]]
            }
          }).filter(v=>{return v !=null})
          cb(null, initData)
        } else {
          return cb('未检测到实体编号，资源名称，设备名称，部件序列等信息，请使用规定文件ProcessDevice.txt')
        }
        break
      case 3:
        console.log(info,'info');
        if (info.indexOf('实体编号') > -1 && info.indexOf('资源名称') > -1 && info.indexOf('函数名称') > -1 && info.indexOf('操作名称') > -1 && info.indexOf('操作描述') > -1) {
          let i_id = head.findIndex((v) => { return v == 'Id' })
          let i_assetName = head.findIndex((v) => { return v == 'AssetName' })
          let i_remark = head.findIndex((v) => { return v == 'Remark' })
          let i_functionName = head.findIndex((v) => { return v == 'FunctionName' })
          let i_operationName = head.findIndex((v) => { return v == 'OperationName' })
          let i_typeID = head.findIndex((v) => { return v == 'TypeID' })
          let i_operationInfo = head.findIndex((v) => { return v == 'OperationInfo' })
          let i_qizzTips = head.findIndex((v) => { return v == 'QizzTips' })
          if(!i_id || !i_assetName || !i_remark || !i_functionName || !i_operationName || !i_typeID || !i_operationInfo || !i_qizzTips) 
           return cb('未检测到Id，AssetName，Remark，FunctionName，OperationName，TypeID，OperationInfo，QizzTips，中的某些字段，请使用规定文件VirtaulProcess.txt')
          let initData = arr.map((item) => {
            if (item.indexOf('#') == -1) {
              let line = item.split('\t')
              if(line[i_id] != null) {//2021.11.9 czk发现存在部分都为null的情况，导致数据库添加的数据很奇怪，加的判断
                return [line[i_id], line[i_assetName], line[i_remark], line[i_functionName], line[i_operationName], line[i_typeID], line[i_operationInfo], line[i_qizzTips]]
              } else {
                return null
              }
            }
          }).filter(v=>{return v !=null})
          cb(null, initData)
        } else {
          return cb('未检测到实体编号，资源名称，函数名称，操作名称等信息，请使用规定文件DeviceOperation.txt')
        }
        break
    }
    // cb(arr)
  })
}

module.exports = {
  readTxtFileToArr
}