import store from "@/store";
/**
 * @<NAME_EMAIL>
 * @description 格式化时间
 * @param time
 * @param cFormat
 * @returns {string|null}
 */
export function parseTime (time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

/**
 * @<NAME_EMAIL>
 * @description 格式化时间
 * @param time
 * @param option
 * @returns {string}
 */
export function formatTime (time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @<NAME_EMAIL>
 * @description 将url请求参数转为json格式
 * @param url
 * @returns {{}|any}
 */
export function paramObj (url) {
  const search = url.split("?")[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, " ") +
    '"}'
  );
}

/**
 * @<NAME_EMAIL>
 * @description 父子关系的数组转换成树形结构数据
 * @param data
 * @returns {*}
 */
export function translateDataToTree (data) {
  const parent = data.filter(
    (value) => value.parentId === "undefined" || value.parentId == null
  );
  const children = data.filter(
    (value) => value.parentId !== "undefined" && value.parentId != null
  );
  const translator = (parent, children) => {
    parent.forEach((parent) => {
      children.forEach((current, index) => {
        if (current.parentId === parent.id) {
          const temp = JSON.parse(JSON.stringify(children));
          temp.splice(index, 1);
          translator([current], temp);
          typeof parent.children !== "undefined"
            ? parent.children.push(current)
            : (parent.children = [current]);
        }
      });
    });
  };
  translator(parent, children);
  return parent;
}

/**
 * @<NAME_EMAIL>
 * @description 树形结构数据转换成父子关系的数组
 * @param data
 * @returns {[]}
 */
export function translateTreeToData (data) {
  const result = [];
  data.forEach((item) => {
    const loop = (data) => {
      result.push({
        id: data.id,
        name: data.name,
        parentId: data.parentId,
      });
      const child = data.children;
      if (child) {
        for (let i = 0; i < child.length; i++) {
          loop(child[i]);
        }
      }
    };
    loop(item);
  });
  return result;
}

/**
 * @<NAME_EMAIL>
 * @description 10位时间戳转换
 * @param time
 * @returns {string}
 */
export function tenBitTimestamp (time) {
  const date = new Date(time * 1000);
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? "" + m : m;
  let d = date.getDate();
  d = d < 10 ? "" + d : d;
  let h = date.getHours();
  h = h < 10 ? "0" + h : h;
  let minute = date.getMinutes();
  let second = date.getSeconds();
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;
  return y + "年" + m + "月" + d + "日 " + h + ":" + minute + ":" + second; //组合
}

/**
 * @<NAME_EMAIL>
 * @description 13位时间戳转换
 * @param time
 * @returns {string}
 */
export function thirteenBitTimestamp (time) {
  const date = new Date(time / 1);
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? "" + m : m;
  let d = date.getDate();
  d = d < 10 ? "" + d : d;
  let h = date.getHours();
  h = h < 10 ? "0" + h : h;
  let minute = date.getMinutes();
  let second = date.getSeconds();
  minute = minute < 10 ? "0" + minute : minute;
  second = second < 10 ? "0" + second : second;
  return y + "年" + m + "月" + d + "日 " + h + ":" + minute + ":" + second; //组合
}

/**
 * @<NAME_EMAIL>
 * @description 获取随机id
 * @param length
 * @returns {string}
 */
export function uuid (length = 32) {
  const num = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
  let str = "";
  for (let i = 0; i < length; i++) {
    str += num.charAt(Math.floor(Math.random() * num.length));
  }
  return str;
}

/**
 * @<NAME_EMAIL>
 * @description m到n的随机数
 * @param m
 * @param n
 * @returns {number}
 */
export function random (m, n) {
  return Math.floor(Math.random() * (m - n) + n);
}

/**
 * @<NAME_EMAIL>
 * @description addEventListener
 * @type {function(...[*]=)}
 */
export const on = (function () {
  return function (element, event, handler, useCapture = false) {
    if (element && event && handler) {
      element.addEventListener(event, handler, useCapture);
    }
  };
})();

/**
 * @<NAME_EMAIL>
 * @description removeEventListener
 * @type {function(...[*]=)}
 */
export const off = (function () {
  return function (element, event, handler, useCapture = false) {
    if (element && event) {
      element.removeEventListener(event, handler, useCapture);
    }
  };
})();

/**
 * @copyright czk
 * @description 重置form表单
 * @param ref
 */
export function resetForm (refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

/**
 * @copyright czk
 * @description vxe的模态框打开事件
 * @param ref
 */
export function xModalOpen (refName) {
  if (this.$refs[refName]) {
    if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
      this.$refs[refName].maximize();
    } else {
      return 
    }
  }
}

/**
 * @copyright czk
 * @description vxe的模态框打开事件
 * @param ref
 */
export function xModalClose (refName) {
  console.log(refName, 'refName');
  // if (Vue.$refs[refName]) {
  //   Vue.$refs[refName].close();
  // }
}

/**
 * @copyright czk
 * @description 递归函数
 * @param ref
 */
export function recursiveFun (arr, str, func) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][str]) {
      recursiveFun(arr[i][str], str, func)
    } else {
      func()
    }
  }
}

export function msg (params, type = 'success', bool = false) {
  let defaultOpt = {
    message: '',
    type,
    duration: 1500,
    showClose: bool
  };
  let isMsg = typeof params === 'string';
  let resParams = isMsg ? { message: params } : params;
  return this.$message({
    ...defaultOpt,
    ...resParams
  });
};


export function searchChildren(children, path) {
  let defaultOpt = {
    found: false,
    searchedPath: path,
  };

  for (const child of children) {
    // 如果当前项的 component 与传入的 path 匹配，则返回 true
    if (child.component === path) {
      return { ...defaultOpt, found: true };
    }
    // 如果当前项有子项，则递归搜索子项
    if (child.children && child.children.length > 0) {
      const result = searchChildren(child.children, path); // 直接调用
      if (result.found) {
        return result;
      }
    }
  }
  // 如果没有找到匹配的 component，则返回 false
  return defaultOpt;
}


// 判断浏览器内核版本
export function getBrowserKernelVersion() {
  const userAgent = navigator.userAgent;
  let browserInfo = {
    name: 'unknown',
    version: 'unknown',
    isChromeAbove130: false // 新增字段，判断 Chrome 是否大于等于 130
  };

  // 检测是否为 WebKit 内核 (Chrome, Safari)
  if (/AppleWebKit/.test(userAgent)) {
    browserInfo.name = 'WebKit';
    const webkitVersion = userAgent.match(/AppleWebKit\/([\d.]+)/);
    if (webkitVersion) {
      browserInfo.version = webkitVersion[1];
    }

    // 检测 Chrome
    if (/Chrome/.test(userAgent)) {
      const chromeVersion = userAgent.match(/Chrome\/([\d.]+)/);
      if (chromeVersion) {
        browserInfo.name = 'Chrome';
        browserInfo.version = chromeVersion[1];

        // 提取 Chrome 版本号，并判断是否大于或等于 130
        const majorVersion = parseInt(chromeVersion[1].split('.')[0], 10);
        if (majorVersion >= 130) {
          browserInfo.isChromeAbove130 = true; // 如果版本大于等于 130，返回 true
        }
      }
    }

    // 检测 Safari
    else if (/Safari/.test(userAgent)) {
      const safariVersion = userAgent.match(/Version\/([\d.]+)/);
      if (safariVersion) {
        browserInfo.name = 'Safari';
        browserInfo.version = safariVersion[1];
      }
    }
  }
  // 检测 Firefox (Gecko 内核)
  else if (/Gecko/.test(userAgent) && /Firefox/.test(userAgent)) {
    browserInfo.name = 'Firefox';
    const firefoxVersion = userAgent.match(/Firefox\/([\d.]+)/);
    if (firefoxVersion) {
      browserInfo.version = firefoxVersion[1];
    }
  }
  // 检测 IE/Edge (Trident/EdgeHTML/Blink)
  else if (/Trident/.test(userAgent) || /Edge/.test(userAgent)) {
    const ieVersion = userAgent.match(/(MSIE|rv:|Edge\/)([\d.]+)/);
    if (ieVersion) {
      browserInfo.name = /Edge/.test(userAgent) ? 'Edge' : 'IE';
      browserInfo.version = ieVersion[2];
    }
  }

  return browserInfo;
}
