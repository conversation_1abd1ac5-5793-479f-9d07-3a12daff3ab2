var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/TipArticleService");

router.post('/list', tryAsyncErrors(Service.list))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:articleId', tryAsyncErrors(Service.delete))

module.exports = router;
