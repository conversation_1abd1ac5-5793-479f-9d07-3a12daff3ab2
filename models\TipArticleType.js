'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class TipArticleType extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // this.belongsTo(models.TipArticle,{foreignKey:'articleTypeId',constraints:false})
    }
  }
  TipArticleType.init(
    {      
      articleTypeId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "选项ID"
      },
      articleTypeName:{
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue:'',
        comment: "议题类型名称"
      },
      articleTypeInfo:{
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue:'',
        comment: "议题类型介绍"
      },     
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },      
      // createTime: {
      //   type: DataTypes.DATE,
      //   defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      //   allowNull: true,
      //   comment: "创建时间"
      // },
      // updateTime: {
      //   type: DataTypes.DATE,
      //   defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      //   allowNull: true,
      //   comment: "更新时间"
      // },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },      
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'TipArticleType',
      tableName: 'zk_tip_article_type',
      comment:'议题类型表'
    }
  )
  return TipArticleType
}
