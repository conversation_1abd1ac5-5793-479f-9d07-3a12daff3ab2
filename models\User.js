'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsToMany(models.Role, { as: 'roleIds', through: sequelize.models.UserRole, foreignKey: 'userId' })
      this.hasMany(models.UserExam, { as: 'userExams', foreignKey: 'userId', constraints: false })
      this.belongsTo(models.UserRole, { foreignKey: 'id', targetKey: 'userId', constraints: false })

      this.belongsTo(models.GradeUser, { foreignKey: 'id', constraints: false })
    }
  }
  User.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: '唯一id'
      },
      userName: {
        type: DataTypes.STRING(20),
        allowNull: false,
        // unique: true,
        comment: '用户名',
        validate: {
          len: { args: [2, 10], msg: "用户名长度须在2-10之间" }
        }
      },
      nickName: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '昵称',
        validate: {
          len: { args: [0, 15], msg: "昵称长度须在0-15之间" }
        }
      },
      password: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '密码'
      },
      unit: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '单位',
      },
      regionId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '地区id',
      },
      regionName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment:
          '所属工会/地区名称。20210525讨论后添加，由于工会关联关系不明确，会有混淆',
      },
      sex: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        comment: "性别id"
      },
      phone: {
        type: DataTypes.STRING(30),
        allowNull: true,
        comment: '手机号',
      },
      email: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '邮箱',
        // validate: {
        //   isEmail: { msg: '请填写正确邮箱格式' }
        // }
      },
      avatar: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '头像地址',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '状态1是启用0是禁用',
      },
      isRegister: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "是否注册用户（1.是，0.否）"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注',
      },
      isDelete: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '逻辑删除0否1是',
      },
      levelCode: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: '层次码',
      },
      regionLevelCode: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: '地区层次码',
      },
      gradeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '届年级id',
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '创建时间',
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '更新时间',
      }      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'User',
      tableName: 'sys_user',
      comment: '用户表'
    }
  )
  return User
}
