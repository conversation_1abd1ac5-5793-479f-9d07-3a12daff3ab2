{"name": "vue-admin-beautiful-template", "version": "1.0.0", "private": true, "author": "chuz<PERSON><PERSON>", "participants": [], "homepage": "https://chu1204505056.gitee.io/vue-admin-beautiful", "scripts": {"serve": "npm run helper&&vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:preview": "npm run helper&&vue-cli-service build --mode preview", "globle": "npm install -g cnpm --registry=https://registry.npm.taobao.org&&cnpm i rimraf npm-check-updates nrm -g&&rimraf node_modules&&cnpm i", "inspect": "vue-cli-service inspect", "template": "plop", "clear": "rimraf node_modules&&cnpm i&&increase-memory-limit", "use:npm": "nrm use npm", "use:taobao": "nrm use taobao", "update": "ncu -u --concurrency 10 --timeout 80000&&cnpm i", "update:globle": "ncu -g --concurrency 10 --timeout 80000", "svgo": "svgo -f src/remixIcon/svg --config=svgo.yml", "push": "start ./push.sh", "deploy": "start ./deploy.sh", "increase-memory-limit": "increase-memory-limit", "helper": "node node_modules/zx-layouts"}, "repository": {"type": "git", "url": "git+https://github.com/chuzhixin/vue-admin-beautiful-template.git"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.19.2", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.8.29", "echarts": "^5.2.1", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "js-cookie": "^2.2.1", "jsencrypt": "^3.2.1", "lodash": "^4.17.19", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "qs": "^6.9.4", "screenfull": "^5.1.0", "sortablejs": "^1.14.0", "vue": "^2.6.14", "vue-cropper": "^0.6.5", "vue-echarts": "6.0.0", "vue-router": "^3.5.2", "vue-ueditor-wrap": "^2.5.6", "vuex": "^3.5.1", "vxe-table": "^3.6.17", "wangeditor": "^4.7.15", "xe-utils": "^3.5.4", "xgplayer": "^3.0.22", "xgplayer-hls": "^3.0.22", "xgplayer-hls.js": "^3.0.22", "zx-count": "^0.3.7", "zx-icon": "^1.1.1", "zx-keel": "^0.9.4", "zx-layouts": "^0.5.7", "zx-templates": "^0.0.10"}, "devDependencies": {"@babel/register": "^7.10.4", "@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-plugin-router": "^4.4.6", "@vue/cli-plugin-vuex": "^4.4.6", "@vue/cli-service": "^4.4.6", "@vue/composition-api": "^1.2.4", "autoprefixer": "^9.8.5", "compression-webpack-plugin": "^4.0.0", "filemanager-webpack-plugin": "^2.0.5", "husky": "^4.2.5", "image-webpack-loader": "^6.0.0", "increase-memory-limit": "^1.0.7", "mockjs": "^1.1.0", "plop": "^2.7.1", "prettier": "^2.0.5", "sass": "~1.42.1", "sass-loader": "^10.1.1", "script-ext-html-webpack-plugin": "^2.1.4", "svg-sprite-loader": "^5.0.0", "svgo": "^1.3.2", "vue-template-compiler": "^2.6.11", "webpackbar": "^4.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "volta": {"node": "14.21.3"}}