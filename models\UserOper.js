'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class UserOper extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.belongsTo(models.User,{foreignKey:'userId', constraints: false})
      this.belongsTo(models.VirtualProcess,{foreignKey:'virtualprocessId', constraints: false})
      this.belongsTo(models.VirtualProcessDevice,{foreignKey:'deviceId', constraints: false})
      this.belongsTo(models.VirtualProcessDeviceOper,{foreignKey:'deviceOperationId', constraints: false})
    }
  }
  UserOper.init(
    {      
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "唯一ID"
      },
      userId:{
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "用户id"
      },  
      virtualprocessId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },       
      deviceId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },       
      deviceOperationId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },   
      status:{
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "状态（0.打开界面 1.开始 2.结束）"
      },     
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'UserOper',
      tableName: 'zk_user_oper',
      comment: '用户仿真操作记录表'
    }
  )
  return UserOper
}
