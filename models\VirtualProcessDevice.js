'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class VirtualProcessDevice extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsToMany(models.Role, {as:'roles', through: sequelize.models.UserRole,foreignKey:'roleId',otherKey:'userId' })
      this.hasMany(models.VirtualProcessDeviceOper, { as: 'children', foreignKey: 'vpdId', constraints: false })
      this.belongsTo(models.VirtualProcess, { foreignKey: 'id', constraints: false })
      this.belongsTo(models.PathPrefix, { foreignKey: 'prefixId', constraints: false })
    }
  }
  VirtualProcessDevice.init(
    {
      name: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.processName + (this.deviceName?'_' + this.deviceName:'')
        },
      },
      info: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.processInfo
        },
      },
      level: {
        type: DataTypes.VIRTUAL,
        get () {
          return 2
        },
      },
      parentId: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.vpId
        },
      },
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "进程ID"
      },
      vpId: {
        // autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: true,
        // primaryKey: true,
        comment: "关联ID"
      },
      operName: {
        type: DataTypes.STRING(120),
        allowNull: true,
        comment: "操作名称"
      },
      assetName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "资源名称"
      },
      processName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "流程(工艺)名称"
      },
      processInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "流程(工艺)介绍"
      },
      deviceName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "设备名称"
      },
      deviceInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "设备介绍"
      },
      position: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "位置"
      },
      rotation: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: 0,
        comment: "旋转角度"
      },
      scaling: {
        type: DataTypes.BIGINT(11),
        allowNull: true,
        defaultValue: 0,
        comment: "放大倍数"
      },
      unitsIds: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "部件序列ID（可多个，使用_分割）",
      },
      operationIds: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "部件序列ID（可多个，使用_分割）",
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      /* 2022.1.13 czk根据刘工要求，虚拟课程管理的树形结构应该是虚拟流程管理的树形数据，按照刘工提供的3个文件生成。
      原吴兴城又单独新建了一张表，不知道为什么？  
      所以现在添加部分字段，对应需要传递的数据信息，数据图片等
      */
      prefixId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "路径前缀id"
      },
      type: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "类型"
      },
      pic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "图片地址"
      },
      videoSrc: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "视频地址"
      },
      linkSrc: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "外链路径"
      },
      isPlatform:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "平台（1.web，2.window）"
      },
      webSrc:{
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "web端访问地址"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0正常 1停用）"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualProcessDevice',
      tableName: 'zk_process_device',
      comment:'仿真数据表（改版前）'
    }
  )
  return VirtualProcessDevice
}
