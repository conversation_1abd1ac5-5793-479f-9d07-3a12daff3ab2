var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index");
const Service = require("../../services/MenuService");

router.get("/menuList", tryAsyncErrors(Service.menuList));
router.post("/menuList", tryAsyncErrors(Service.menuList));
router.post("/menuBeauList", tryAsyncErrors(Service.menuBeauList));
router.post("/getRoutes", tryAsyncErrors(Service.getRoutes));
router.post('/add', tryAsyncErrors(Service.create))
router.get('/list', tryAsyncErrors(Service.menuList))

module.exports = router;
