var express = require("express");
var router = express.Router();
const path = require("path");
const { uploadImport, tryAsyncErrors } = require("../../utils/index");
const { transactionTryAsyncErrors } = require("../../utils/transaction");
const Service = require("../../services/VirtualDataService");

router.get("/list", tryAsyncErrors(Service.list));
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.post('/editBatch', transactionTryAsyncErrors(Service.updateBatch))
router.post('/childDel', transactionTryAsyncErrors(Service.childDel))
router.delete('/delete/:id', transactionTryAsyncErrors(Service.del))
router.post('/updateParams', transactionTryAsyncErrors(Service.updateParams))

router.post('/uploadImport', uploadImport.single('file'), tryAsyncErrors(Service.upload))
router.post('/export', tryAsyncErrors(Service.export))
router.post('/status', transactionTryAsyncErrors(Service.status))
router.post('/sort', tryAsyncErrors(Service.sort))
router.post('/sortValid', tryAsyncErrors(Service.sortValid))
router.post('/readXml', tryAsyncErrors(Service.readXml))
router.post('/readXlsx', tryAsyncErrors(Service.readXlsx))
router.post('/zipFile', tryAsyncErrors(Service.zipFile))
router.post('/sharpFile', tryAsyncErrors(Service.sharpFile))

router.get("/operList", tryAsyncErrors(Service.operList));


module.exports = router;
