const { BlackList, $LikeWhere, sequelize: $seq } = require('../models')

const { setRedis, getRedis, delRedis } = require('../utils/redisUtils')

class LoginService {
    //验证码接口
    static async setCaptcha(id, data, timer = 18000) {
        await setRedis(id, data)
    }
    static async getCaptcha(id) {
        return await getRedis(id)
    }
    static async delCaptcha(id) {
        await delRedis(id)
    }
    //登录时如果存在错误记录IP以及错误次数
    static async checkHostIpLogin(ip) {
        let num = await getRedis(ip)
        if (num) {
            if (Number(num) == 4) {
                await BlackList.create({ hostIp: ip })
                await LoginService.setBlackList()
                await delRedis(ip)
            } else {
                await setRedis(ip, Number(num) + 1)
            }
        } else {
            await setRedis(ip, 1)
        }
    }

    static async setBlackList() {
        let data = await BlackList.findAll({
            raw: true
        })
        data = data.map(t => t.hostIp)
        await setRedis('BlackList',data)
        return data
    }

    static async getBlackList() {
        let data = await getRedis('BlackList')
        if(data && data.length > 0) return data
        return LoginService.setBlackList()
    }
}

module.exports = LoginService