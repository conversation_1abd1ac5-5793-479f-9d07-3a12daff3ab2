'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Role extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsToMany(models.User, { as: 'userIds', through: sequelize.models.UserRole, foreignKey: 'roleId' })

      this.belongsTo(models.UserRole, { as: 'roles', foreignKey: 'roleId', constraints: false })
    }
  }
  Role.init(
    {
      roleId: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: '唯一id',
      },
      roleName: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: '角色名称',
      },
      roleSort: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '排序字段',
      },
      menuIds: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '权限id',
      },
      halfIds: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '半选中id，用来处理前端tree回显的bug',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '状态1是启用0是禁用',
      },
      // createTime: {
      //   type: DataTypes.DATE,
      //   allowNull: true,
      //   defaultValue: sequelize.Sequelize.literal('CURRENT_TIMESTAMP'),
      //   comment: '创建时间',
      // },
      // updateTime: {
      //   type: DataTypes.DATE,
      //   allowNull: true,
      //   defaultValue: sequelize.Sequelize.literal('CURRENT_TIMESTAMP'),
      //   comment: '更新时间',
      // },
      isDelete: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '是否删除1是0否，默认0',
      },
      remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注',
      },
      createUser: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '创建人',
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Role',
      tableName: 'sys_role',
      comment: '系统角色表'
    }
  )
  return Role
}
