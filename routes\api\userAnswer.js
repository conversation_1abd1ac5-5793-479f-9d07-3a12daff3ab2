var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/UserAnswerService");

router.post('/list', tryAsyncErrors(Service.list))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:commentId', tryAsyncErrors(Service.delete))
router.post('/queryAnswer', tryAsyncErrors(Service.queryAnswer))
router.post('/userAnswerList', tryAsyncErrors(Service.userAnswerList))

module.exports = router;
