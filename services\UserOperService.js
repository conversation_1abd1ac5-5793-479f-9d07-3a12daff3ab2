const { UserOper, User, VirtualProcess, VirtualProcessDevice, VirtualProcessDeviceOper, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(UserOper.tableAttributes)
class UserOperService {
  static async create (req, res) {
    if (!req.body.virtualprocessId && !req.body.deviceId && !req.body.deviceOperationId) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await UserOper.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await UserOper.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await UserOper.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    if (req.query.userName) {
      where['User.userName'] = $seq.literal('User.userName ' + ` LIKE '%${req.query.userName}%'`)
    }

    if (page && pageSize) {
      let { rows, count } = await UserOper.findAndCountAll({
        attributes: {
          include: [
            [$seq.col('User.userName'), 'userName'],
            [$seq.col('VirtualProcess.vrProName'), 'vrProName'],
          ]
        },
        include: [
          {
            model: User,
            attributes: []
          },
          {
            model: VirtualProcess,
            attributes: []
          }
        ],
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }
}

module.exports = UserOperService
