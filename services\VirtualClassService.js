const { VirtualClass, VirtualProcess, VirtualProcessDevice, VirtualProcessDeviceOper, PathPrefix, sequelize: $seq } = require('../models')
const { handleTree } = require('../utils/index')

function getLevelCode(str, splitStr) {
    let strOne
    if (!splitStr) {
        strOne = parseInt(str)
    } else {
        strOne = parseInt(str.substr(splitStr.length))
    }
    let num = strOne + 1
    switch ((num + '').length) {
        case 1:
            return splitStr + '000' + num
        case 2:
            return splitStr + '00' + num
        case 3:
            return splitStr + '0' + num
        case 4:
            return splitStr + num
    }
}
class VirtualClassService {
    static async create(req, res) {
        // if (!req.body.parentId) {
        //   return res.sendSuccess({ code: -1, msg: '字段缺失' })
        // }
        // let checkDate = await VirtualClass.findOne({ where: { homePageName } })
        // if (checkDate) return res.sendSuccess({ code: -1, msg: '此名称已存在' })
        let obj = {
            assetName: req.body.assetName || '',
            operName: req.body.operName,
            prefixId: req.body.prefixId,
            linkSrc: req.body.linkSrc,
            type: req.body.type,
            pic: req.body.pic,
            videoSrc: req.body.videoSrc,
            isPlatform: req.body.isPlatform,
            webSrc: req.body.webSrc,
            status: req.body.status,
            orderNum: req.body.orderNum,
            remark: req.body.remark
        }

        let data = null
        let maxId = null
        if (req.body.level) {
            switch (req.body.level) {
                case 1:
                    if (req.body.name.indexOf('_') > -1) {
                        let nameArr = req.body.name.split('_')
                        obj['processName'] = nameArr[0]
                        obj['deviceName'] = nameArr[1]
                    } else {
                        obj['processName'] = req.body.name
                    }
                    obj['processInfo'] = req.body.info
                    obj['vpId'] = req.body.parentId
                    maxId = await VirtualProcessDevice.max('id', { where: { vpId: req.body.parentId } })
                    if (maxId) {
                        obj['id'] = maxId + 1
                    } else {
                        let str = req.body.parentId + ''
                        obj['id'] = Number(str + '001')
                    }
                    data = await VirtualProcessDevice.create(obj)
                    break;
                case 2:
                    obj['operationName'] = req.body.name
                    obj['operationInfo'] = req.body.info
                    obj['vpdId'] = req.body.parentId
                    maxId = await VirtualProcessDeviceOper.max('id', { where: { vpdId: req.body.parentId } })
                    if (maxId) {
                        obj['id'] = maxId + 1
                    } else {
                        let str = req.body.parentId + ''
                        obj['id'] = Number(str + '001')
                    }
                    data = await VirtualProcessDeviceOper.create(obj)
                    break;
            }
        } else {
            obj['vrProName'] = req.body.name
            obj['vrProInfo'] = req.body.info
            // obj['vrProInfo'] = req.body.info
            data = await VirtualProcess.create(obj)
        }
        res.sendSuccess({ code: 200, data })
    }

    static async update(req, res) {
        // const { homePageId, homePageName } = req.body
        if (!req.body.id || !req.body.level) {
            return res.sendSuccess({ code: -1, msg: 'id或其它字段缺失' })
        }
        let obj = {
            assetName: req.body.assetName,
            operName: req.body.operName,
            prefixId: req.body.prefixId,
            linkSrc: req.body.linkSrc,
            type: req.body.type,
            pic: req.body.pic,
            videoSrc: req.body.videoSrc,
            isPlatform: req.body.isPlatform,
            webSrc: req.body.webSrc,
            status: req.body.status,
            orderNum: req.body.orderNum,
            remark: req.body.remark
        }

        let data = ''
        switch (req.body.level) {
            case 1:
                obj['vrProName'] = req.body.name
                obj['vrProInfo'] = req.body.info
                data = await VirtualProcess.update(obj, {
                    where: { id: req.body.id }
                })
                break;
            case 2:
                if (req.body.name.indexOf('_') > -1) {
                    let nameArr = req.body.name.split('_')
                    obj['processName'] = nameArr[0]
                    obj['deviceName'] = nameArr[1]
                } else {
                    obj['processName'] = req.body.name
                }
                obj['processInfo'] = req.body.info
                data = await VirtualProcessDevice.update(obj, {
                    where: { id: req.body.id }
                })
                break;
            case 3:
                obj['operationName'] = req.body.name
                obj['operationInfo'] = req.body.info
                data = await VirtualProcessDeviceOper.update(obj, {
                    where: { id: req.body.id }
                })
                break;
        }
        res.sendSuccess({ code: 200, data })
    }

    static async del(req, res) {
        if (!req.body.id || !req.body.level) return res.sendSuccess({ code: -1, msg: '参数缺失' })
        let data = null
        switch (req.body.level) {
            case 1:
                data = await VirtualProcess.destroy({
                    where: { id: req.body.id },
                })
                break;
            case 2:
                data = await VirtualProcessDevice.destroy({
                    where: { id: req.body.id },
                })
                break;
            case 3:
                data = await VirtualProcessDeviceOper.destroy({
                    where: { id: req.body.id },
                })
                break;
        }
        res.sendSuccess({ code: 200, data, msg: '删除成功' })
    }

    static async operList(req, res) {
        if (!req.body.deviceId) return res.sendSuccess({ code: -1, msg: 'virtualprocessId参数缺失' })
        let data = await VirtualProcessDeviceOper.findAll({
            attributes: ['id', ['operationName', 'virtualName'], ['operationInfo', 'description'], 'operName', ['orderNum', 'sortBy']],
            where: {
                vpdId: req.body.deviceId
            },
            order: [
                ['orderNum']
            ],
            raw: true
        })
        res.sendSuccess({ code: 200, data })
    }

    static async list(req, res) {
        let whereOne, whereTwo, whereThree
        let name = req.body.name
        let data
        if (name) {
            whereOne = {
                vrProName: {
                    $like: `%${name}%`
                }
            }
            whereTwo = {
                $or: {
                    deviceName: {
                        $like: `%${name}%`
                    },
                    processName: {
                        $like: `%${name}%`
                    }
                }
            }
            whereThree = {
                operationName: {
                    $like: `%${name}%`
                }
            }
            //先从第三张表筛选出符合的数据，反向查找第二张表符合的数据，再反向查找第一张表的数据
            let threeD = await VirtualProcessDeviceOper.findAll({
                attributes: [[$seq.fn('group_concat', $seq.literal('distinct vpdId')), 'vpdIds']],
                where: {
                    operationName: {
                        $like: `%${name}%`
                    }
                },
                raw: true
            })

            let twoD = await VirtualProcessDevice.findAll({
                attributes: [[$seq.fn('group_concat', $seq.literal('distinct vpId')), 'vpIds']],
                where: {
                    $or: [
                        $seq.literal('CONCAT(deviceName,processName) like "%' + name + '%"'),
                        $seq.literal('FIND_IN_SET(id,"' + threeD[0].vpdIds + '")')
                    ]
                },
                raw: true
            })

            data = await VirtualProcess.findAll({
                attributes: {
                    include: [[$seq.col('PathPrefix.prefixName'), 'prefixName']]
                },
                where: $seq.literal("(VirtualProcess.vrProName like '%" + name + "%' OR FIND_IN_SET(VirtualProcess.id,'" + twoD[0].vpIds + "'))"),
                include: [
                    {
                        model: VirtualProcessDevice,
                        as: 'children',
                        required: false,
                        attributes: {
                            include: [[$seq.literal('`children->PathPrefix`.`prefixName`'), 'prefixName']]
                        },
                        where: $seq.literal("(children.processName like '%" + name + "%' OR children.deviceName like '%" + name + "%' OR FIND_IN_SET(children.id,'" + threeD[0].vpdIds + "'))"),
                        include: [
                            {
                                model: VirtualProcessDeviceOper,
                                as: 'children',
                                required: false,
                                attributes: {
                                    include: [[$seq.literal('`children->children->PathPrefix`.`prefixName`'), 'prefixName']]
                                },
                                where: $seq.literal("`children->children`.`operationName` like '%" + name + "%'"),
                                include: {
                                    model: PathPrefix,
                                    attributes: []
                                }
                            },
                            {
                                model: PathPrefix,
                                attributes: []
                            }
                        ]
                    },
                    {
                        model: PathPrefix,
                        attributes: []
                    }
                ],
            })
        } else {
            //此处为前台学生端虚拟课程中心访问接口
            data = await VirtualProcess.findAll({
                attributes: {
                    include: [[$seq.col('PathPrefix.prefixName'), 'prefixName']]
                },
                // where: $seq.literal("(VirtualProcess.vrProName like '%" + name + "%')"),
                include: [
                    {
                        model: VirtualProcessDevice,
                        as: 'children',
                        required: false,
                        // right: true,
                        attributes: {
                            include: [[$seq.literal('`children->PathPrefix`.`prefixName`'), 'prefixName']]
                        },
                        // where: whereTwo,
                        include: [
                            {
                                model: VirtualProcessDeviceOper,
                                as: 'children',
                                required: false,
                                attributes: {
                                    include: [[$seq.literal('`children->children->PathPrefix`.`prefixName`'), 'prefixName']]
                                },
                                include: {
                                    model: PathPrefix,
                                    attributes: []
                                }
                            },
                            {
                                model: PathPrefix,
                                attributes: []
                            }
                        ]
                    },
                    {
                        model: PathPrefix,
                        attributes: []
                    }
                ],
                order: [
                    ['orderNum'],
                    [$seq.col('`children`.orderNum')],
                    [$seq.col('`children->children`.orderNum')]
                ]
            })
        }
        res.sendSuccess({ code: 200, data })
    }

    static async homePageList(req, res) {
        /* 2022.1.25 czk修改此处，按照刘工要求，虚拟课程管理的树形结构基础数据，可直接从虚拟数据导入中获取。
        然后添加参数，图片信息等 */
        // console.log(req.body, 'req.body');
        let whereOne, whereTwo, whereThree
        let name = req.body.name
        let data
        if (name) {
            whereOne = {
                vrProName: {
                    $like: `%${name}%`
                }
            }
            whereTwo = {
                $or: {
                    deviceName: {
                        $like: `%${name}%`
                    },
                    processName: {
                        $like: `%${name}%`
                    }
                }
            }
            whereThree = {
                operationName: {
                    $like: `%${name}%`
                }
            }
            //先从第三张表筛选出符合的数据，反向查找第二张表符合的数据，再反向查找第一张表的数据
            let threeD = await VirtualProcessDeviceOper.findAll({
                attributes: [[$seq.fn('group_concat', $seq.literal('distinct vpdId')), 'vpdIds']],
                where: {
                    operationName: {
                        $like: `%${name}%`
                    }
                },
                raw: true
            })

            let twoD = await VirtualProcessDevice.findAll({
                attributes: [[$seq.fn('group_concat', $seq.literal('distinct vpId')), 'vpIds']],
                where: {
                    $or: [
                        $seq.literal('CONCAT(deviceName,processName) like "%' + name + '%"'),
                        $seq.literal('FIND_IN_SET(id,"' + threeD[0].vpdIds + '")')
                    ]
                },
                raw: true
            })

            data = await VirtualProcess.findAll({
                attributes: {
                    include: [[$seq.col('PathPrefix.prefixName'), 'prefixName']]
                },
                where: $seq.literal("(VirtualProcess.vrProName like '%" + name + "%' OR FIND_IN_SET(VirtualProcess.id,'" + twoD[0].vpIds + "'))"),
                include: [
                    {
                        model: VirtualProcessDevice,
                        as: 'children',
                        required: false,
                        attributes: {
                            include: [[$seq.literal('`children->PathPrefix`.`prefixName`'), 'prefixName']]
                        },
                        where: $seq.literal("(children.processName like '%" + name + "%' OR children.deviceName like '%" + name + "%' OR FIND_IN_SET(children.id,'" + threeD[0].vpdIds + "'))"),
                        include: [
                            {
                                model: VirtualProcessDeviceOper,
                                as: 'children',
                                required: false,
                                attributes: {
                                    include: [[$seq.literal('`children->children->PathPrefix`.`prefixName`'), 'prefixName']]
                                },
                                where: $seq.literal("`children->children`.`operationName` like '%" + name + "%'"),
                                include: {
                                    model: PathPrefix,
                                    attributes: []
                                }
                            },
                            {
                                model: PathPrefix,
                                attributes: []
                            }
                        ]
                    },
                    {
                        model: PathPrefix,
                        attributes: []
                    }
                ],
            })
        } else {
            //此处为前台学生端虚拟课程中心访问接口
            data = await VirtualProcess.findAll({
                attributes: {
                    include: [[$seq.col('PathPrefix.prefixName'), 'prefixName']]
                },
                // where: $seq.literal("(VirtualProcess.vrProName like '%" + name + "%')"),
                include: [
                    {
                        model: VirtualProcessDevice,
                        as: 'children',
                        required: false,
                        // right: true,
                        attributes: {
                            // include: [[$seq.literal('`children->PathPrefix`.`prefixName`'), 'prefixName']]
                        },
                        // where: whereTwo,
                        // include: [
                        //   {
                        //     model: VirtualProcessDeviceOper,
                        //     as: 'children',
                        //     required: false,
                        //     attributes: {
                        //       include: [[$seq.literal('`children->children->PathPrefix`.`prefixName`'), 'prefixName']]
                        //     },
                        //     include: {
                        //       model: PathPrefix,
                        //       attributes: []
                        //     }
                        //   },
                        //   {
                        //     model: PathPrefix,
                        //     attributes: []
                        //   }
                        // ]
                    },
                    {
                        model: PathPrefix,
                        attributes: []
                    }
                ],
                order: [
                    ['orderNum']
                ]
            })
        }
        res.sendSuccess({ code: 200, data })
    }

    static async menuBeauList(req, res) {
        let result = await VirtualClass.findAll()
        let data = handleTree(result, 'menuId')
        res.sendSuccess({ code: 200, data })
    }

    static async upload(req, res, next) {
        let file = req.file
        if (!file) return res.sendSuccess({ code: -1, msg: '文件上传异常，请联系管理员' })
        let obj = xlsx.parse(file.path)
        let xlsxData = obj[0].data//原始表格获取数据
        let header = xlsxData.shift()
        let len = header.length
        let excelIndexObj = excelHeaderValid([
            { validName: '项目名称', indexName: 'sIndex' },
            { validName: '所属企业', indexName: 'enterIndex' },
        ], header, res)

        //拼接sql
        let errField = []
        let str = ''
        for (let i = 0; i < header.length; i++) {
            if (WorkmateCupTypicalArr[i] && header[i] == WorkmateCupTypicalArr[i].label) {
                str += WorkmateCupTypicalArr[i].value + ','
            } else {
                let index = WorkmateCupTypicalArr.findIndex(item => item.label == header[i])
                if (index != -1) {
                    str += WorkmateCupTypicalArr[index].value + ','
                } else {
                    errField.push(header[i])
                }
            }
        }
        str += 'createById'

        if (errField.length > 0) {
            let errmsg = 'excel表头部分字段未对应上(' + errField.join(',') + ')'
            return res.sendSuccess({ code: -1, error: '导入数据有误', msg: errmsg })
        }
        let insertData = []//处理后的导入数据库数据
        let errData = []//处理excel文件中数据异常的项
        xlsxData.forEach((item, i) => {
            if (item.length && item.length != 0) {
                //1.补全node-xlsx读取缺失部分信息问题
                if (item.length < len) {
                    let num = len - item.length
                    for (let j = 0; j < num; j++) {
                        item.push('')
                    }
                }
                item.push(req.user.userId)
                insertData.push(item)
            }
        })

        if (errData.length > 0) {
            let errStr = '<div style="color:red">'
            errData.forEach((item) => {
                if (item.row) {
                    errStr += `<p>EXCEL表格第${item.row}行数据异常,${item.err};</p>`
                }
            })
            errStr += '</div>'
            return res.sendSuccess({ code: -1, error: '导入数据有误', msg: errStr })
        }

        try {
            const result = await $seq.transaction(async t => {
                let sTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
                insertData = JSON.parse(JSON.stringify(insertData))
                let data = await $seq.query(`INSERT INTO zk_workmatecup_typical(${str}) values ?`, { replacements: [insertData], raw: true, transaction: t })
                let eTime = dayjs().add(1, 'second').format('YYYY-MM-DD HH:mm:ss')
                await $seq.query(`UPDATE zk_workmatecup_typical w JOIN zk_workmatecup u ON w.projectName = u.projectName SET w.pId = u.id,w.enterPriseId = u.enterPriseId WHERE w.pId IS NULL AND w.createTime >= '${sTime}'`, { transaction: t })
                await $seq.query(`UPDATE zk_workmatecup_typical w JOIN zk_zcspace u ON w.spaceName = u.spaceName SET w.spaceId = u.id WHERE w.spaceId IS NULL AND w.createTime >= '${sTime}'`, { transaction: t })
                return data
            });
            res.sendSuccess({ code: 200, success: result, msg: '导入成功' + insertData.length + '条数据' })
        } catch (error) {
            console.log(error, '\n------------------error-----------------');
            next(error)
        }
    }
}

module.exports = VirtualClassService
