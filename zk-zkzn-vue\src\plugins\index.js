/* 公共引入,勿随意修改,修改时需经过确认 */
import Vue from "vue";
import "./element";
import "./support";
import "./f12";
import '@/assets/icon/iconfont.css'
import '@/assets/icon/icon.css'
import "@/styles/vab.scss";
import vab from "@/utils/vab";
import "@/remixIcon";
import "@/colorfulIcon";
import "@/config/permission";
import "@/utils/errorLog";
import drag from "@/directive/drag";
import permissions from "@/directive/permissions";
import modal from "@/directive/modal";
import VabCount from 'zx-count'
import "./vabIcon";
import VabQueryForm from "@/components/VabQueryForm";
import VabQueryFormTopPanel from "@/components/VabQueryForm/VabQueryFormTopPanel";
import VabQueryFormBottomPanel from "@/components/VabQueryForm/VabQueryFormBottomPanel";
import VabQueryFormLeftPanel from "@/components/VabQueryForm/VabQueryFormLeftPanel";
import VabQueryFormRightPanel from "@/components/VabQueryForm/VabQueryFormRightPanel";
import SelfPopver from '@/components/SelfPopver/index'
import TableCellTooltip from '@/components/TableCellTooltip/index'
import IconDot from '@/components/IconDot/index'
import 'xe-utils';
import VXETable from 'vxe-table';
VXETable.setup({
  table: {
    showOverflow: true,
    showHeaderOverflow: true,
    resizable: true,
    autoResize: true,
    border: 'inner',
    align: 'center',
    highlightHoverRow: true,
    highlightCurrentRow: true,
    sortConfig: {
      remote: true,
      trigger: 'default',
      orders: ['asc', 'desc', null],
      sortMethod: null
    },
    columnConfig: {
      useKey: "id"
    }
  },
  pager: {
    size: null,
    autoHidden: false,
    align: 'left',
    perfect: true,
    pageSize: 2,
    pagerCount: 7,
    pageSizes: [10, 20, 100, 200, 500],
    layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']
  },
  modal: {
    fullscreen: true
  }
})
// 自定义全局的格式化处理函数
VXETable.formats.mixin({
  // 格式化性别
  formatSex ({ cellValue }) {
    return cellValue ? (cellValue === '1' ? '男' : '女') : ''
  },
  // 格式化下拉选项
  formatSelect ({ cellValue }, list) {
    const item = list.find(item => item.value === cellValue)
    return item ? item.label : ''
  },
  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss
  formatDate ({ cellValue }, format) {
    return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd HH:mm:ss')
  },
  // 四舍五入金额，每隔3位逗号分隔，默认2位数
  formatAmount ({ cellValue }, digits = 2) {
    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
  },
  // 格式化银行卡，默认每4位空格隔开
  formatBankcard ({ cellValue }) {
    return XEUtils.commafy(XEUtils.toValueString(cellValue), { spaceNumber: 4, separator: ' ' })
  },
  // 四舍五入,默认两位数
  formatFixedNumber ({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.round(cellValue, digits), digits)
  },
  // 向下舍入,默认两位数
  formatCutNumber ({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits)
  },
  //格式化根据搜索框内容变色
  formatSearch ({ cellValue }, searchName) {
    if (searchName) {
      cellValue = cellValue.replace(new RegExp(searchName, 'g'), `<span style="color:red;">${searchName}</span>`)
    }
    return cellValue
  }
})
import 'vxe-table/lib/style.css';
//下拉树
import TreeSelect from '@riophae/vue-treeselect';
// import the styles
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { trimLeft } from "xe-utils";
// 注册 tree-select 组件
Vue.use(VXETable);

Vue.use(permissions);
Vue.use(drag);
Vue.use(modal);
Vue.use(vab);
Vue.use(VabCount)

Vue.component('tree-select', TreeSelect);
Vue.component('self-popver', SelfPopver);
Vue.component('table-cell-tooltip', TableCellTooltip);
Vue.component("vab-query-form", VabQueryForm);
Vue.component("vab-query-form-left-panel", VabQueryFormLeftPanel);
Vue.component("vab-query-form-right-panel", VabQueryFormRightPanel);
Vue.component("vab-query-form-top-panel", VabQueryFormTopPanel);
Vue.component("vab-query-form-bottom-panel", VabQueryFormBottomPanel);
Vue.component("icon-dot", IconDot);
