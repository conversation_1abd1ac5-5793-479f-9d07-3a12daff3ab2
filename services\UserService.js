const { User, Role, Menu, UserRole, UserSerial, sequelize: $seq, $LikeWhere, $Order } = require('../models')
const { set<PERSON>aptcha, get<PERSON>aptcha, delCaptcha, checkHostIpLogin, getBlackList } = require("./LoginService")
const { nanoid } = require('nanoid');
const svgCaptcha = require('svg-captcha');
const { PWD_SALT, PRIVATE_KEY, EXPIRESD, REFRESH_EXPIRESD, REFRESH_PRIVATE_KEY } = require('../utils/constant')
const { md5 } = require('../utils/md5')
const config = require(__dirname + '/../config/config.js')["student_role"]
const jwt = require('jsonwebtoken')
const fields = Object.keys(User.tableAttributes)
const xlsx = require('node-xlsx');
const ConfigService = require('./ConfigService');
const { hmsetRedis, delRedis } = require('../utils/redisUtils');

class UserService {
  static async create (req, res) {
    let userInfo = req.body
    let checkDate = await User.findOne({
      where: { userName: userInfo.userName },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    userInfo.password = md5(`${userInfo.password}${PWD_SALT}`)
    let data = await User.create(userInfo)
    let roles = await Role.findAll({
      where: { roleId: userInfo['roleIds'] || [] },
    })
    await data.setRoleIds(roles)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    const { id, userName, roleIds } = req.body
    // if (req.body.avatar) {
    //   delete req.body.avatar
    // }
    if (id == 1) return res.sendSuccess({ code: -1, msg: '超级管理员用户不可修改' })
    let checkDate = await User.findOne({
      where: {
        userName,
        id: {
          $ne: id,
        },
      },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    let data = await User.findByPk(id)
    let roles = await Role.findAll({
      where: { roleId: roleIds || [] },
    })
    await data.update(req.body)
    await data.setRoleIds(roles)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async statusChange (req, res) {
    const { id, status } = req.body
    if (id == 1) return res.sendSuccess({ code: -1, msg: '超级管理员用户不可修改' })
    let data = await User.update({ status }, {
      where: {
        id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  // 用户修改自身信息
  static async updateInfo (req, res) {
    const { id, userName } = req.body
    if (req.body.avatar) {
      delete req.body.avatar
    }
    if (id == 1) return res.sendSuccess({ code: -1, msg: '超级管理员用户不可修改' })
    let checkDate = await User.findOne({
      where: {
        userName,
        id: {
          $ne: id,
        },
      },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    let data = await User.findByPk(id)
    await data.update(req.body)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { userId } = req.params
    if (!userId) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let idArr = userId.split(',')
    if (idArr.includes('1')) return res.sendSuccess({ code: -1, msg: '超级管理员（admin）用户不可删除' })
    let data = await User.destroy({
      where: {
        id: {
          $in: idArr,
        },
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async uploadAvatar (req, res) {
    if (!req.file) return res.sendSuccess({ code: -1, msg: '上传失败' })
    let filePath = req.file.path.split('public')[1]
    let { userId: id } = req.user
    await User.update({
      avatar: filePath
    }, {
      where: {
        id
      }
    })
    res.sendSuccess({ code: 200, msg: '上传成功', data: filePath ? 'http://' + req.headers.host + filePath : null, imgUrl: filePath ? 'http://' + req.headers.host + filePath : null })
  }

  static async uploadUserAvatar (req, res) {
    if (!req.file) return res.sendSuccess({ code: -1, msg: '上传失败' })
    let filePath = req.file.path.split('public')[1]
    res.sendSuccess({ code: 200, msg: '上传成功', data: filePath ? filePath : null, imgUrl: filePath ? filePath : null })
  }

  static async userSerialList (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    let { rows, count } = await UserSerial.findAndCountAll({
      attributes: {
        include: [
          [$seq.col("User.userName"), 'userName'],
          [$seq.col("User.unit"), 'unit'],
          [$seq.col("User.avatar"), 'avatar'],
        ]
      },
      include: [
        {
          model: User,
          attributes: [],
        },
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [
        ["id", 'desc']
      ]
      // raw:true
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  static async getUserData (userName) {
    return await User.findOne({
      where: {
        userName
      },
      raw: true
    })
  }

  static async import (req, res, next) {//excel导入用户信息接口
    let file = req.file
    if (!file) return res.sendSuccess({ code: -1, msg: '文件上传异常，请联系管理员' })
    let obj = xlsx.parse(file.path)
    // console.log(obj, 'obj');
    let xlsxData = obj[0].data//原始表格获取数据
    let header = xlsxData.shift()
    let len = header.length
    //获取用户名称所在位置
    let userNameIndex = header.findIndex(item => {
      return item == '用户名称'
    })
    if (!header || header.length == 0) {
      return res.send({
        code: 403,
        error: 403,
        message: '此表格未检测到表头数据！',
      })
    }
    // console.log(header, 'header');
    let insertData = []//处理后的导入数据库数据
    let errData = []//处理excel文件中数据异常的项
    let userNameAll = []//收集excel中的用户名，后面执行数据库对比，存在重复的就报错
    xlsxData.forEach((item, i) => {
      if (item.length && item.length != 0) {
        if (item[userNameIndex]) {
          userNameAll.push(item[userNameIndex])
        }
        if (!item[userNameIndex]) {
          errData.push({ row: i + 1, err: '用户名称不能为空' })
        }
        //1.补全node-xlsx读取缺失部分信息问题
        if (item.length < len) {
          let num = len - item.length
          for (let j = 0; j < num; j++) {
            item.push('')
          }
        }
        item.push(req.user.userName)
        item.push(md5(`${123456}${PWD_SALT}`))
        insertData.push(item)
      }
    })
    // return res.sendSuccess({ code: 200, xlsxData, insertData })
    if (errData.length > 0) {
      let errStr = '<div style="color:red">'
      errData.forEach((item) => {
        if (item.row) {
          errStr += `<p>EXCEL表格第${item.row}行数据异常,${item.err};</p>`
        }
      })
      errStr += '</div>'
      return res.sendSuccess({
        code: -1,
        error: '导入数据有误',
        msg: errStr,
      })
    }

    let repetUser = await User.findAll({
      where: {
        userName: {
          $in: userNameAll
        }
      },
      raw: true
    })
    let repetError = repetUser.map(v => v.userName).join()
    if (repetUser && repetUser.length > 0) return res.sendSuccess({ code: -1, msg: '导入失败，用户名称存在重复' + repetError, confirm: true })

    try {
      const result = await $seq.transaction(async t => {
        await $seq.query(`CREATE TEMPORARY TABLE tmp_table (userName VARCHAR(100) NOT NULL, roleName VARCHAR(100),phone VARCHAR(30),email VARCHAR(20),createBy VARCHAR(100) NOT NULL,password VARCHAR(100) NOT NULL)`, { transaction: t })
        // await $seq.query(`CREATE TABLE IF NOT EXISTS tmp_table (userName VARCHAR(100) NOT NULL, roleName VARCHAR(100),phone VARCHAR(30),email VARCHAR(20),createBy VARCHAR(100) NOT NULL)`)
        await $seq.query(`INSERT INTO tmp_table(userName,roleName,phone,email,createBy,password) values ?`, { replacements: [JSON.parse(JSON.stringify(insertData))], transaction: t })
        // await $seq.query(`INSERT INTO sys_user (userName,tmp_roleName,phone,email,createBy,createTime,updateTime) values ?`, { replacements: [insertData], type: QueryTypes.INSERT })
        let data = await $seq.query(`INSERT INTO sys_user (userName,password,phone,email,createBy,createTime,updateTime) SELECT userName,password,phone,email,createBy,now(),now() FROM tmp_table`, { transaction: t })
        await $seq.query(`INSERT INTO sys_user_role (userId,roleId) SELECT tr.userId,tr.roleId FROM (SELECT u.id userId,r.roleId FROM tmp_table p INNER JOIN sys_user u ON p.userName = u.userName  INNER JOIN sys_role r ON p.roleName = r.roleName WHERE p.roleName !='' AND p.roleName IS NOT NULL) tr`, { transaction: t })
        // await $seq.query(`INSERT INTO sys_user_role (userId,roleId) SELECT t.userId,t.roleId FROM (SELECT u.id userId,r.roleId FROM sys_role r INNER JOIN sys_user u ON r.roleName = u.tmp_roleName WHERE p.roleName !='' AND p.roleName IS NOT NULL) t`)
        // await $seq.query(`DROP TABLE tmp_table`)
        return data
      });
      res.sendSuccess({ code: 200, success: result, msg: '导入成功' })
    } catch (error) {
      // await $seq.query(`DELETE sys_user FROM sys_user,tmp_table WHERE sys_user = tmp_table`)
      // await $seq.query(`DROP TABLE IF EXISTS tmp_table`)
      console.log(error, '\n------------------error-----------------');
      next(error)
    }
  }

  static async resetPwd (req, res) {
    const { id } = req.body
    let data = await User.update(
      {
        password: md5(`111111${PWD_SALT}`)
      },
      {
        where: {
          id
        }
      })
    res.sendSuccess({ code: 200, data, msg: '重置密码成功' })
  }

  static async getInfo (req, res) {
    const obj = jwt.verify(req.body.token, PRIVATE_KEY)
    let data = await User.findOne({
      attributes: {
        exclude: ['password']
      },
      where: { userName: obj.userName },
    })
    data.setDataValue('avatar', data.avatar ? 'http://' + req.headers.host + data.avatar : null)
    data.setDataValue('permissions', ['admins'])
    res.sendSuccess({ code: 200, data })
  }

  //修改密码
  static async pwdEdit (req, res) {
    let userInfo = req.body
    // const obj = jwt.verify(req.body.token, PRIVATE_KEY)
    // if(req.body.newPassword == req.body.oldPassword) {
    //   return res.sendSuccess({code:-1,msg:'新密码不能和旧密码相同'})
    // }
    let oldPwd = md5(`${userInfo.oldPassword}${PWD_SALT}`)
    let newPwd = md5(`${userInfo.newPassword}${PWD_SALT}`)
    let data = await User.findByPk(req.user.userId)
    if (oldPwd != data.password) return res.sendSuccess({ code: -1, msg: '输入的旧密码不正确' })
    await User.update({ password: newPwd }, { where: { id: req.user.userId } })
    res.sendSuccess({ code: 200, msg: '修改密码成功', success: true })
  }

  static async info (req, res) {
    const { userName, userId, roleId } = req.user
    let data = await User.findOne({
      attributes: {
        include: [[$seq.col('`UserRole`.`roleId`'), 'roleId'], [$seq.col('`UserRole->Roles`.`roleName`'), 'roleName']],
        exclude: ['password']
      },
      include: [
        {
          model: UserRole,
          attributes: [],
          include: [
            {
              model: Role,
              attributes: [],
            }
          ]
        }
      ],
      where: { id: userId },
    })
    data.setDataValue('avatar', data.avatar ? 'http://' + req.headers.host + data.avatar : null)

    //根据roleId查询按钮权限
    let roleIds = String(roleId).split(',')
    let roleMenu
    let permissions = []
    if (userName == 'admin') {
      roleMenu = ''
      permissions = ['admin']
    } else {
      roleMenu = await Role.findAll({
        attributes: ['menuIds', 'halfIds'],
        where: {
          roleId: {
            $in: roleIds
          }
        },
        raw: true
      }).then(data => {
        let str = ''
        data.forEach(item => {
          str += item.menuIds + ',' + item.halfIds + ','
        })
        str = str.substring(0, str.length - 1)
        return [...new Set(str.split(','))]
      })

      let permData = await Menu.findAll({
        attributes: ['perms'],
        where: {
          status: {
            $ne: 1
          },
          menuType: 'F',
          menuId: roleMenu ? {
            $in: roleMenu
          } : { $ne: null }
        },
        order: [
          ['parentId', 'asc']
        ],
        raw: true
      }).then(data => {
        return data.map(item => item.perms)
      })
      permissions = permData
    }
    data.setDataValue('permissions', permissions)
    res.sendSuccess({ code: 200, data })
  }

  static async userList (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    let { rows, count } = await User.findAndCountAll({
      include: [
        {
          through: { attributes: [] }, // 排除中间表
          model: Role,
          as: 'roleIds',
          attributes: [
            'roleId',
            'roleName'
          ],
        },
      ],
      attributes: {
        // include:[[$seq.fn('concat',$seq.col('roleIds.roleName')),'roleNames']],
        exclude: ['password', 'roleId'],
      },
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      distinct: true,
      // raw:true
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  // 生成验证码
  static async captcha (req, res) {
    let cap = svgCaptcha.createMathExpr({
      mathMin: 1,
      mathMax: 20,
      // color: false,              // 启用随机文字颜色
      // background: '#FFFFFF'
    });
    let uuid = nanoid()
    // 将生成的 SVG 数据中的文字颜色替换成白色
    // cap.data = cap.data.replace(/fill="[^"]*"/g, 'fill="#ffffff"');
    // console.log(req.session,'req.session');
    await setCaptcha(uuid, cap.text); // 存储验证码到 session，你也可以选择其他方式存储
    res.type('svg');
    res.send({ code: 200, uuid, data: cap.data });
  }

  static async login (req, res) {
    let { userName, password, uuid, captcha } = req.body
    if (!uuid || !captcha) return res.sendSuccess({ code: -1, msg: 'uuid和验证码不能为空' })
    let redisCaptcha = await getCaptcha(uuid)
    if (captcha != redisCaptcha) return res.sendSuccess({ code: -1, msg: '验证码不正确' })
    // 验证通过，删除保存的验证码
    await delCaptcha(uuid)

    let user = await User.findOne({ where: { userName } })
    if (!user || user.length === 0) {
      return res.sendSuccess({ code: -1, msg: '该账户不存在' })
    }

    let role = await UserRole.findOne({
      where: {
        userId: user.dataValues.id
      }
    })

    let config = await ConfigService.getInfo()
    password = md5(`${password}${PWD_SALT}`)
    if (user.dataValues.password != password)
      return res.sendSuccess({ code: -1, error: 1001, msg: '密码错误！' })
    if (config.isApproval == 1 && user.dataValues.isRegister == 1 && user.dataValues.status != 1)
      return res.sendSuccess({ code: -1, error: 1002, msg: '注册用户需管理员审批后才可登录或此用户已被停用！' })
    if (user.dataValues.status != 1)
      return res.sendSuccess({ code: -1, error: 1002, msg: '此用户已停用！' })
    if (!role) return res.sendSuccess({ code: -1, error: 1003, msg: '此用户无角色权限！' })
    let token = jwt.sign({ userName, userId: user.dataValues.id, roleId: role.dataValues.roleId }, PRIVATE_KEY, {
      expiresIn: EXPIRESD,
    })
    let refreshToken = jwt.sign({ userName, userId: user.dataValues.id, roleId: role.dataValues.roleId }, REFRESH_PRIVATE_KEY, {
      expiresIn: REFRESH_EXPIRESD,
    })
    await hmsetRedis(`user:${user.dataValues.id}`, { refreshToken, token }, REFRESH_EXPIRESD)
    res.sendSuccess({
      code: 200,
      msg: '登录成功',
      token: token,
      accessToken: token,
      refreshToken,
      data: { token, accessToken: token, refreshToken },
      user: { userName },
    })
  }

  static async logout (req, res) {
    if (req.user && req.user.userId) {
      await delRedis(`user:${req.user.userId}`)
    }
    res.sendSuccess({ code: 200, msg: '退出成功' })
  }

  //2022.3.4 czk分离了学生与用户
  static async studentList (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    let UserRoleWhere = {
      roleId: config.roleId
    }
    let { rows, count } = await User.findAndCountAll({
      include: [
        {
          model: UserRole,
          attributes: [],
          where: UserRoleWhere
        },
      ],
      attributes: {
        // include:[[$seq.fn('concat',$seq.col('roleIds.roleName')),'roleNames']],
        exclude: ['password', 'roleId'],
      },
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      distinct: true,
      // raw:true
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  // 学生Excel导入
  static async studentImport (req, res, next) {
    let file = req.file
    if (!file) return res.sendSuccess({ code: -1, msg: '文件上传异常，请联系管理员' })

    let obj = xlsx.parse(file.path)
    let xlsxData = obj[0].data // 原始表格获取数据
    let header = xlsxData.shift()
    let len = header.length

    if (!header || header.length == 0) {
      return res.sendSuccess({
        code: -1,
        error: '表头数据异常',
        msg: '此表格未检测到表头数据！',
      })
    }

    // 获取各字段在表头中的位置
    const fieldIndexes = {
      userName: header.findIndex(item => item == '学生名称'),
      nickName: header.findIndex(item => item == '学生昵称'),
      phone: header.findIndex(item => item == '手机号码'),
      email: header.findIndex(item => item == '邮箱'),
      status: header.findIndex(item => item == '状态')
    }

    // 检查必填字段
    if (fieldIndexes.userName === -1) {
      return res.sendSuccess({ code: -1, msg: '表头中缺少"学生名称"字段' })
    }

    let insertData = [] // 处理后的导入数据库数据
    let errData = [] // 处理excel文件中数据异常的项
    let userNameAll = [] // 收集excel中的学生名，后面执行数据库对比

    xlsxData.forEach((item, i) => {
      if (item.length && item.length != 0) {
        const userName = item[fieldIndexes.userName]
        const nickName = item[fieldIndexes.nickName] || ''
        const phone = item[fieldIndexes.phone] || ''
        const email = item[fieldIndexes.email] || ''
        const statusText = item[fieldIndexes.status] || '启用'
        const status = statusText === '停用' ? 0 : 1

        // 验证必填字段
        if (!userName) {
          errData.push({ row: i + 2, err: '学生名称不能为空' })
        } else {
          userNameAll.push(userName)
        }

        // 验证邮箱格式
        if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
          errData.push({ row: i + 2, err: '邮箱格式不正确' })
        }

        // 验证手机号格式
        if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
          errData.push({ row: i + 2, err: '手机号格式不正确' })
        }

        insertData.push({
          userName,
          nickName,
          phone,
          email,
          status,
          password: md5(`123456${PWD_SALT}`), // 默认密码123456
          createBy: req.user.userName
        })
      }
    })

    if (errData.length > 0) {
      let errStr = '<div style="color:red">'
      errData.forEach((item) => {
        if (item.row) {
          errStr += `<p>EXCEL表格第${item.row}行数据异常,${item.err};</p>`
        }
      })
      errStr += '</div>'
      return res.sendSuccess({
        code: -1,
        error: '导入数据有误',
        msg: errStr,
      })
    }

    // 检查用户名重复
    let repetUser = await User.findAll({
      where: {
        userName: {
          $in: userNameAll
        }
      },
      raw: true
    })

    if (repetUser && repetUser.length > 0) {
      let repetError = repetUser.map(v => v.userName).join(',')
      return res.sendSuccess({
        code: -1,
        msg: '导入失败，学生名称存在重复：' + repetError
      })
    }

    try {
      const result = await $seq.transaction(async t => {
        // 批量插入学生数据
        const createdUsers = await User.bulkCreate(insertData, { transaction: t, returning: true })

        // 为每个新创建的学生分配"学员"角色
        const userRoleData = createdUsers.map(user => ({
          userId: user.id,
          roleId: config.roleId // 学员角色ID为3
        }))

        // 批量插入用户角色关联
        await $seq.models.UserRole.bulkCreate(userRoleData, { transaction: t })

        return createdUsers
      })

      res.sendSuccess({ code: 200, msg: `成功导入${result.length}条学生数据，并自动分配学员角色` })
    } catch (error) {
      console.log(error, '\n------------------学生导入错误-----------------');
      res.sendSuccess({ code: -1, msg: '导入失败，请联系管理员' })
    }
  }

  // 导出学生Excel
  static async export (req, res) {
    const { userName, status } = req.body

    // 构建查询条件
    let where = {}
    if (userName) where.userName = { $like: `%${userName}%` }
    if (status !== undefined && status !== '') where.status = status

    try {
      // 查询学生数据
      const students = await User.findAll({
        where,
        attributes: ['userName', 'nickName', 'phone', 'email', 'status', 'createTime'],
        raw: true,
        order: [['createTime', 'DESC']]
      })

      // 构建Excel数据
      const excelData = []

      // 添加表头
      excelData.push(['学生名称', '学生昵称', '手机号码', '邮箱', '状态', '创建时间'])

      // 添加数据行
      students.forEach(student => {
        excelData.push([
          student.userName || '',
          student.nickName || '',
          student.phone || '',
          student.email || '',
          student.status === 1 ? '启用' : '停用',
          student.createTime ? new Date(student.createTime).toLocaleString('zh-CN') : ''
        ])
      })

      // 生成Excel文件
      const buffer = xlsx.build([{
        name: '学生列表',
        data: excelData
      }])

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', 'attachment; filename=student_list.xlsx')

      // 发送文件
      res.send(buffer)

    } catch (error) {
      console.error('导出学生Excel失败:', error)
      res.sendSuccess({ code: -1, msg: '导出失败，请联系管理员' })
    }
  }
}

module.exports = UserService
