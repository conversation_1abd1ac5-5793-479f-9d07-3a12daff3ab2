module.exports = {
  development: {
    username: "root",
    password: "eay123@",
    database: "zk-zkzn-gkd",
    // "database": "zk-keshi-test-2024827",
    // "database": "zk-zkzn-202482",
    host: "www.unraid.plus",
    port: "3309",
    dialect: "mysql",
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
    },
    define: {
      timestamps: false,
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
    timezone: "+08:00",
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
  production: {
   username: "root",
    password: "eay123@",
    database: "zk-zkzn-gkd",
    // "database": "zk-keshi-test-2024827",
    // "database": "zk-zkzn-202482",
    host: "*************",
    port: "3306",
    dialect: "mysql",
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
    },
    define: {
      timestamps: false,
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
    timezone: "+08:00",
    pool: {
      max: 20,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
  test: {
    username: "root",
    password: "eay123@",
    database: "hbgydx",
    host: "www.unraid.plus",
    port: "3309",
    dialect: "mysql",
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
    },
    define: {
      timestamps: false,
      createdAt: "createTime",
      updatedAt: "updateTime",
    },
    timezone: "+08:00",
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
  upload_config: {
    baseURL: "http://127.0.0.1:3010",
    simple_upload_redirect: "http://*********/reload",
  },
  student_role: {
    roleId: 3,
    roleName: "学员",
  },
};
