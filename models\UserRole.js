'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class UserRole extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      this.hasMany(models.Role, { foreignKey: 'roleId', constraints: false })

      this.hasMany(models.User, { foreignKey: 'id', constraints: false })
    }
  }
  UserRole.init(
    {
      // userId: {
      //   type: DataTypes.INTEGER,
      //   references: {
      //     model: sequelize.models.User, // 'User' 也可以使用
      //     key: 'id',
      //   },
      // },
      // roleId: {
      //   type: DataTypes.INTEGER,
      //   references: {
      //     model: sequelize.models.Role, // 'Role' 也可以使用
      //     key: 'roleId',
      //   },
      // },
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'UserRole',
      tableName: 'sys_user_role',
      comment:'用户角色关联表'
    }
  )
  return UserRole
}
