const { PathWeb, VirtualData, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(PathWeb.tableAttributes)

class PathWebService {
  static async create (req, res) {
    if (!req.body.webName) return res.sendSuccess({ code: -1, msg: '名称不能为空' })
    let checkDate = await PathWeb.findOne({
      where: { webName: req.body.webName },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此名称已存在' })
    let data = await PathWeb.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    if (!req.body.webName) return res.sendSuccess({ code: -1, msg: '名称不能为空' })
    let checkDate = await PathWeb.findOne({
      where: {
        webName: req.body.webName,
        id: {
          $ne: req.body.id
        }
      },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此名称已存在' })
    let data = await PathWeb.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await PathWeb.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async list (req, res) {
    let { page, pageSize, prefixName, attchAddr } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await PathWeb.findAndCountAll({
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        // order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      return res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }

  static async getNameAnduUpdateId () {
    let webData = await VirtualData.findAll({
      attributes: [
        [$seq.fn('DISTINCT', $seq.col('webSrc')), "webSrc"]
      ],
      where: {
        webSrc: {
          $ne: null
        },
        $and: {
          webSrc: {
            $ne: ""
          }
        }
      },
      raw: true
    })

    let insrt = webData.map(t => {
      return { webName: t.webSrc, webUrl: t.webSrc, status: 1 }
    })
    await PathWeb.bulkCreate(insrt)
    // await $seq.query(``)
  }
}

// PathWebService.getNameAnduUpdateId()

module.exports = PathWebService
