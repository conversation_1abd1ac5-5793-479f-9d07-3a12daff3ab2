const express = require("express");
const router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/DictService");

router.get('/list', tryAsyncErrors(Service.list))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

//部分字典列表
router.get('/roleOption', tryAsyncErrors(Service.roleOption))
router.get('/roleMenuOption', tryAsyncErrors(Service.roleMenuOption))
router.get('/gradeOption', tryAsyncErrors(Service.gradeOption))
router.get('/codeOption', tryAsyncErrors(Service.codeOption))
router.get('/codeTreeDataOption', tryAsyncErrors(Service.codeTreeDataOption))

router.get('/virtualTreeOption', tryAsyncErrors(Service.virtualTreeOption))
//根据字典编码从redis获取数据
router.get('/option', tryAsyncErrors(Service.option))

router.get('/getDictList', tryAsyncErrors(Service.getDictList))
router.get('/operTypeOption', tryAsyncErrors(Service.operTypeOption))
router.get('/pathPrefixOption', tryAsyncErrors(Service.pathPrefixOption))
router.get('/pathWebOption', tryAsyncErrors(Service.pathWebOption))
router.get('/virtualCateOption', tryAsyncErrors(Service.virtualCateOption))
router.get('/virtualDataListOption', tryAsyncErrors(Service.virtualDataListOption))

module.exports = router;
