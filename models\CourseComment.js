'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class CourseComment extends Model {
    static associate (models) {
      // define association here
    }
  }
  CourseComment.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },      
      commentContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "评论内容"
      },      
      courseId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        // defaultValue: null,
        comment: "虚拟课程id"
      },
      courseWareId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        // defaultValue: null,
        comment: "课程学习id"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '状态0是下架1是上架',
      },
      isDraft: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '状态1是草稿0不是',
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      modelName: 'CourseComment',
      tableName: 'zk_courseware',
      timestamps: true,
      comment: '评论表'
    }
  )
  return CourseComment
}
