import CryptoJS from "crypto-js";

const Encrypts = {
  /**
   * 使用 DES 加密字符串
   * @param {string} text - 要加密的字符串
   * @param {string} secret - 密钥（8字节）
   * @returns {string} - 返回 Base64 编码的加密字符串
   */
  encryptString(text, secret) {
    // 将密钥转换为 CryptoJS 格式（8字节）
    const key = CryptoJS.enc.Utf8.parse(secret);
    const iv = CryptoJS.enc.Utf8.parse('4&tu~IK$'); // 可选，使用相同的密钥作为 IV

    // DES 加密
    const encrypted = CryptoJS.DES.encrypt(text, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    return encrypted.toString();
  },

  /**
   * 使用 DES 解密字符串
   * @param {string} cipherText - 要解密的 Base64 编码字符串
   * @param {string} secret - 密钥（8字节）
   * @returns {string} - 返回解密后的字符串
   */
  decryptString(cipherText, secret) {
    // 将密钥转换为 CryptoJS 格式（8字节）
    const key = CryptoJS.enc.Utf8.parse(secret);
    const iv = CryptoJS.enc.Utf8.parse('4&tu~IK$'); // 可选，使用相同的密钥作为 IV

    // DES 解密
    const decrypted = CryptoJS.DES.decrypt(
      {
        ciphertext: CryptoJS.enc.Base64.parse(cipherText)
      },
      key,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    );

    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

    if (!decryptedText) {
      throw new Error("Decryption failed. Please check the cipher text or secret.");
    }

    return decryptedText;
  },
};

export default Encrypts;
