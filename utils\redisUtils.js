// 引入 redis 实例对象
const { RedisClient } = require('../redis/RedisClient')
// Promise async/await 封装
//   ->向redis中定时存储数据
async function setRedis (key, value, time = 86400) {
  // 存储
  await RedisClient.set(key, JSON.stringify(value), (err, data) => {
    // 为key 设定一个时长 单位为S
    RedisClient.expire(key, time)
    if (err) {
      console.log(err);
    }
    // console.log('redis store Success!', data, key);
    return data //成功会返回ok
  })
}
// Promise async/await 封装
//  ->查询 redis 库中是否有该Key 用于判断Token是否过期
async function queryRedis (key) {
  const result = await RedisClient.exists(key)
  //  判断该值是否为空 如果为空返回null
  if (result === 0) {
    console.log('result:<', '<' + result + '>', 'This key is null...');
    return null
  }
  console.log('Result:', '<' + result + '>', 'With this value!...');
  return result
}
// Promise async/await 封装
//  ->获取 redis 库中该 Key 的 value
async function getRedis (key) {
  const result = await RedisClient.get(key)
  if (result === null) {
    // console.log('result:', '<' + result + '>', 'This key cannot be find...')
    return null
  }
  // console.log('Result:', '<' + result + '>', 'Get key success!...');
  return JSON.parse(result)
}

// Promise async/await 封装
//  ->获取 redis 库中该 Key 的 value
async function delRedis (key) {
  const result = await RedisClient.del(key)
  if (result === null) {
    // console.log('result:', '<' + result + '>', 'This key is del')
    return null
  }
  // console.log('Result:', '<' + result + '>', 'Get key success!...');
  return JSON.parse(result)
}

// Promise async/await 封装
//  ->为 redis 库中的一个key 设定过期的时间 单位为秒(S)
async function timeSetRedis (key, time) {
  // 设定时间
  const result = await RedisClient.expire(key, time)
  if (result === 0) {
    console.log('Result', '<' + result + '>', 'Set defeated or key not find...');
    return null
  }
  // console.log(result);
  console.log('Result', '<' + result + '>', 'Set Success!');
  return result
}

async function hmsetRedis (key, obj, time = 86400) {
  // 存储
  await RedisClient.hmset(key, obj, (err, data) => {
    // 为key 设定一个时长 单位为S
    RedisClient.expire(key, time)
    if (err) {
      console.log(err);
    }
    // console.log('redis store Success!', data, key);
    return data //成功会返回ok
  })
}

async function hsetRedis (key, field, value) {
  await RedisClient.hset(key, field, value, (err, data) => {
    if (err) {
      console.log(err);
    }
    return data //成功会返回ok
  })
}

async function hqueryRedis (key, field) {
  const result = await RedisClient.hexists(key, field)
  //  判断该值是否为空 如果为空返回null
  if (result === 0) {
    return null
  }
  return result
}

async function hgetRedis (key, field) {
  const result = await RedisClient.hget(key, field)
  if (result === null) {
    return null
  }
  return result
}

async function hgetallRedis (key) {
  const result = await RedisClient.hgetall(key)
  if (result === null) {
    return null
  }
  return result
}

async function ttlRedis (key) {
  const result = await RedisClient.ttl(key)
  if (result === null) {
    return null
  }
  return result
}

module.exports = {
  setRedis,
  getRedis,
  queryRedis,
  timeSetRedis,
  delRedis,
  hsetRedis,
  hmsetRedis,
  hqueryRedis,
  hgetRedis,
  hgetallRedis,
  ttlRedis
}
