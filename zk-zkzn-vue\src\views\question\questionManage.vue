<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="题干" prop="questionContent">
        <el-input placeholder="请输入题干" v-model="formSearch.userName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="题目类型" prop="questionType">
        <el-select v-model="formSearch.questionType" placeholder="题目类型" clearable size="small">
          <el-option v-for="dict in questionTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['question/add']" plain icon="el-icon-plus" @click="$router.push({path:'/question/singleChoice'})" size="mini">新增单选题</el-button>
        <el-button type="success" v-permissions="['question/add']" plain icon="el-icon-plus" @click="$router.push({path:'/question/multipleChoice'})" size="mini">新增多选题</el-button>
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <template v-slot:questionTypeSlot="{ row }">
        <el-tag :type="row.questionType==1?'':'success'">{{row.questionType==1?'单选题':'多选题'}}</el-tag>
      </template>
      <template v-slot:questionLevelSlot="{ row }">
        <el-rate v-model="row.questionLevel" disabled></el-rate>
      </template>
      <!-- 题干 -->
      <template v-slot:questionContentSlot="{ row }">
        <div v-html="row.questionContent"></div>
      </template>
      <!-- 展开栏内容 -->
      <template #conExpand="{ row }">
        <div class="expandBox">
          <el-row style="margin-bottom:10px;">
            <el-col>
              <p v-html="row.questionContent"></p>
            </el-col>
          </el-row>
          <el-row v-for="(item, i) in row.questionAnswers" :key="i" class="questionBox">
            <el-col>
              <el-button :type="item.isRight == '1' ?'success':''" size="mini">{{item.answerNum}}</el-button>
              <div class="qs_contentBox">
                <p v-html="item.answerContent"></p>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="title">分析:</span>
              <div class="qs_contentBox">
                <p class="analyBox" v-html="row.questionAnalysis"></p>
              </div>
            </el-col>
          </el-row>
          <div>
            难度: <el-rate class="inlineRate" v-model="row.questionLevel" disabled></el-rate>
          </div>
        </div>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['question/edit']" icon="el-icon-edit" @click="$router.push({path:row.questionType==1?'/question/singleChoice':'/question/multipleChoice',query:{questionId:row.questionId}})">修改</el-button>
        <el-button type="text" v-permissions="['question/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>
    <!-- <multiple-choice ref="singleChoiceRef"></multiple-choice> -->
  </div>
</template>

<script>
export default {
  name: "QuestionManage",
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'expand', width: 60, slots: { content: 'conExpand' } },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'questionContent', title: '题干', slots: { default: 'questionContentSlot' } },
        { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
        { sortable: true, field: 'questionLevel', title: '题目难度', slots: { default: 'questionLevelSlot' } },
        { sortable: true, field: 'createBy', title: '创建人' },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      loading: false,
      formSearch: {
        questionContent: '',
        questionType: '',
        page: 1,
        pageSize: 10
      },
      questionTypeOptions: [
        { label: '单选题', value: '1' },
        { label: '多选题', value: '2' }
      ],
      isExpand: false,
    };
  },
  created () {
    this.getData();
  },
  activated () {
    this.getData();
  },
  methods: {
    // 初始化
    getData () {
      this.loading = true;
      this.$http.questionQuery(this.formSearch)
        .then(res => {
          this.tableData = res.data
          this.tablePage.total = res.total;
          //分页后判断是否展开或折叠
          this.$nextTick(() => {
            if (this.isExpand) {
              this.$refs.xGrid.setAllRowExpand(true)
            } else {
              this.$refs.xGrid.clearRowExpand()
            }
          })
          this.loading = false;
        });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllRowExpand(true)
      } else {
        this.$refs.xGrid.clearRowExpand()
      }
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.questionId;
          } else {
            list += item.questionId + ',';
          }
        });
      } else {
        list = row.questionId;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.questionDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.expandBox {
  font-size: 16px;
  // padding-left: 30px;
  .title {
    display: inline-block;
    vertical-align: top;
    margin: 3px 0;
  }
  .qs_contentBox {
    display: inline-block;
    margin-left: 10px;
  }
  .analyBox {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep p {
    margin: 3px 0;
    img {
      vertical-align: middle;
    }
  }
}
.questionBox ::v-deep {
  margin-bottom: 5px;
  .el-button {
    vertical-align: top;
  }
  p {
    margin: 3px 0;
  }
}
</style>
