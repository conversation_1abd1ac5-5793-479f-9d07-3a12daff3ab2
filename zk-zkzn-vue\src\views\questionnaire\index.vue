<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="问卷名称" prop="name">
        <el-input placeholder="请输入问卷名称" v-model="formSearch.name" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <!-- <el-form-item label="操作状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="用户状态" clearable size="small" @change="getData('formSearch')">
          <el-option v-for="dict in [{value:0,label:'打开界面'},{value:1,label:'开始'},{value:2,label:'结束'}]" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <!-- <el-button type="text" icon="el-icon-preview" @click="$router.push({path:'/formRender',query:{id:row.formKey}})">预览</el-button> -->
        <el-button type="text" icon="el-icon-preview" @click="handlePreview(row)">预览</el-button>
        <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" icon="el-icon-edit" @click="handleConfig(row)">配置问卷</el-button>
        <el-dropdown style="margin-left:10px" @command="handleCommand($event, row)" placement="bottom">
          <el-button type="text">更多<i class="el-icon-d-arrow-right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="a"><i class="el-icon-document-copy"></i>复制</el-dropdown-item>
            <el-dropdown-item command="c"><i class="el-icon-delete"></i>删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-button type="text" icon="el-icon-delete" @click="handleDel(row)">删除</el-button> -->
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.userOperRules" title-width="120" title-align="right"></vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  name: "questionnaireIndex",
  data () {
    return {
      srcList: [],
      modalTitle: '新增记录',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
      },
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'name', title: '问卷名称' },
        { sortable: true, field: 'status', title: '状态', formatter: ({ row }) => row.status == 1 ? '启用' : '停用' },
        { sortable: true, field: 'createTime', title: '操作时间' },
        { field: 'remark', title: '备注' },
        { title: '操作', width: 240, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        name: '',
        remark: '',
      },
      formItem: [
        { field: 'name', title: '问卷名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      formSearch: {
        name: '',
        status: null,
        page: 1,
        pageSize: 10
      },
    };
  },
  created () {
    this.getData();
  },
  methods: {
    getData (params) {
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.questionnaireList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData
          if (this.modalTitle == '新增问卷') {
            this.$http.questionnaireAdd(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.questionnaireEdit(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleAdd () {
      this.modalTitle = '新增问卷';
      this.modalVisible = true;
      this.formData = {
        name: '',
        status: 1,
        remark: '',
      };
    },
    handleEdit (row) {
      this.modalTitle = '编辑问卷';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    handleConfig (row) {
      this.$router.push({ path: '/formEditor', query: { id: row.formKey } })
    },
    handlePreview (row) {
      const routeData = this.$router.resolve({ path: '/formRender', query: { id: row.formKey } });
      window.open(routeData.href, '_blank')
    },
    handleCommand (command, row) {
      switch (command) {
        case 'a':
          let form = row
          form.id = null
          this.$http.questionnaireAdd(form).then(res => {
            this.getData()
          })
          break;
        case 'c':
          this.handleDel(row)
          break;
      }
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.questionnaireDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>


<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>
