<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="学生名称" prop="userName">
        <el-input placeholder="请输入学生名称" v-model="formSearch.userName" clearable @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item label="届/年级" prop="gradeId">
        <el-select v-model="formSearch.gradeId" @change="getData('formSearch')" placeholder="请先选择届/年级" clearable size="small">
          <el-option v-for="dict in gradeOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="学生状态" clearable size="small">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['student/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <el-button type="primary" v-permissions="['student/uploadImport']" plain icon="el-icon-upload" @click="handleImport" size="mini">导入excel</el-button>
        <el-button type="primary" v-permissions="['student/tempdownload']" plain icon="el-icon-download" @click="handleExportTemp" size="mini">导出excel模板</el-button>
        <el-button type="success" v-permissions="['student/export']" plain icon="el-icon-download" @click="handleExport" size="mini">导出excel</el-button>
      </template>
      <!-- 学生的角色列表 -->
      <!-- <template v-slot:userSlot="{ row }">
        <el-tag v-for="item in row.roleIds" :key="item.roleId">{{item.roleName}}</el-tag>
      </template> -->
      <!-- 学生的状态切换开关 -->
      <template v-slot:statusSlot="{ row }">
        <vxe-switch v-model="row.status" open-label="启用" :open-value="1" close-label="停用" :close-value="0" @change="statusChange(row)"></vxe-switch>
      </template>
      <!-- 学生的头像 -->
      <template v-slot:avatarSlot="{ row }">
        <el-image style="width: 48px; height: 48px" :src="row.avatar?$SOURCEURL+row.avatar:require('@/assets/img/avater.jpg')" :preview-src-list="srcList">
        </el-image>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['student/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['student/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
        <el-button type="text" v-permissions="['student/resetPwd']" icon="el-icon-refresh-right" @click="handleRecord(row)">重置密码</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.userRules" title-width="120" title-align="right"></vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
export default {
  name: "List",
  components: { ElImageViewer },
  data () {
    return {
      srcList: [],
      modalTitle: '新增学生',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'seq', width: 60, title: '序号' },
        { field: 'avatar', title: '学生头像', slots: { default: 'avatarSlot' } },
        { sortable: true, field: 'userName', title: '学生名称' },
        { sortable: true, field: 'nickName', title: '学生昵称' },
        // { sortable: true, field: 'roleName', title: '角色', formatter: ({ row }) => { return row.roleIds.map(item => item.roleName).join(',') } },
        // { sortable: true, field: 'roleName', title: '角色', slots: { default: 'userSlot' } },
        { field: 'status', title: '是否禁用', slots: { default: 'statusSlot' } },
        { sortable: true, field: 'createBy', title: '创建人' },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        gradeId: '',
        userName: '',
        nickName: '',
        password: '',
        phone: 1,
        status: 1,
        email: '',
        roleIds: ''
      },
      formItem: [
        { field: 'gradeId', title: '所属届/年级', span: 12, itemRender: { name: '$select', props: { placeholder: '请选择届/年级' }, options: [] } },
        { field: 'userName', title: '学生名称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'nickName', title: '学生昵称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入昵称' } } },
        { field: 'password', title: '学生密码', span: 12, itemRender: { name: '$input', props: { type: 'password', placeholder: '请输入学生密码' } } },
        { field: 'phone', title: '手机号码', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入手机号码' } } },
        { field: 'email', title: '邮箱', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入邮箱' } } },
        { field: 'status', title: '学生状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
      ],
      formSearch: {
        gradeId: '',
        userName: '',
        status: '',
        page: 1,
        pageSize: 10
      },
      statusOptions: [
        { value: '0', label: '正常' },
        { value: '1', label: '停用' },
      ],
      gradeOption: []
    };
  },
  created () {
    this.getData();
    this.getGradeList()
  },
  methods: {
    getData (params) {
      //学生列表
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.studentList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.srcList = res.data.map(item => {
          if (item.avatar) {
            return this.$SOURCEURL + item.avatar
          } else {
            return require('@/assets/img/avater.jpg')
          }
        })
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    getGradeList () {
      this.$http.gradeOption().then(res => {
        this.formItem[0].itemRender.options = res.data
        this.gradeOption = res.data
      })
    },
    // 导入Excel
    handleImport(opts = { types: ["xls", "xlsx"] }) {
      this.$refs.xGrid.readFile(opts).then((fileData) => {
        const { files } = fileData;
        let param = new FormData();
        // 将得到的文件流添加到FormData对象
        param.append("file", files[0], files[0].name);
        this.loading = true;
        this.$http.studentImport(param).then((res) => {
          this.loading = false;
          this.$msg(res.msg, "success");
          this.getData();
        }).catch(() => {
          this.loading = false;
        });
      });
    },
    // 导出Excel模板
    handleExportTemp() {
      this.$downloadFun.temp('学生管理导入模板.xlsx');
    },
    // 导出Excel数据
    handleExport() {
      // 构建导出参数，包含当前的搜索条件
      const exportParams = {
        gradeId: this.formSearch.gradeId,
        userName: this.formSearch.userName,
        status: this.formSearch.status
      };

      this.loading = true;
      this.$http.studentExport(exportParams).then((response) => {
        this.loading = false;
        // 创建blob对象
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 生成文件名
        const now = new Date();
        const dateStr = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0');
        link.download = `学生列表_${dateStr}.xlsx`;

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$msg('导出成功', 'success');
      }).catch(() => {
        this.loading = false;
        this.$msg('导出失败', 'error');
      });
    },
    statusChange (row) {
      this.loading = true;
      this.$http.studentEdit(row).then(res => {
        this.loading = false;
        this.$msg(res.msg, 'success')
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {

      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          if (this.modalTitle == '新增学生') {
            this.$http.studentAdd(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.studentEdit(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleAdd () {
      this.modalTitle = '新增学生';
      this.formItem = [
        { field: 'gradeId', title: '所属届/年级', span: 12, itemRender: { name: '$select', props: { placeholder: '请选择届/年级' }, options: this.gradeOption } },
        { field: 'userName', title: '学生名称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'nickName', title: '学生昵称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入昵称' } } },
        { field: 'password', title: '学生密码', span: 12, itemRender: { name: '$input', props: { type: 'password', placeholder: '请输入学生密码' } } },
        { field: 'phone', title: '手机号码', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入手机号码' } } },
        { field: 'email', title: '邮箱', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入邮箱' } } },
        // { field: 'roleIds', title: '学生角色', span: 24, itemRender: { name: '$select', props: { multiple: true, placeholder: '请选择角色' }, options: this.roleOption, optionProps: { value: 'roleId', label: 'roleName' }, } },
        { field: 'status', title: '学生状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
      ]
      this.modalVisible = true;
      this.formData = {
        gradeId: '',
        userName: '',
        nickName: '',
        password: '',
        phone: 1,
        status: 1,
        email: '',
        roleIds: ''
      };
    },
    handleEdit (row) {
      this.modalTitle = '编辑学生';
      this.formItem = [
        { field: 'gradeId', title: '所属届/年级', span: 12, itemRender: { name: '$select', props: { placeholder: '请选择届/年级' }, options: this.gradeOption } },
        // { field: 'userName', title: '学生名称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'nickName', title: '学生昵称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入昵称' } } },
        // { field: 'password', title: '学生密码', span: 12, itemRender: { name: '$input', props: { type:'password',placeholder: '请输入学生密码' } } },
        { field: 'phone', title: '手机号码', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入手机号码' } } },
        { field: 'email', title: '邮箱', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入邮箱' } } },
        // { field: 'roleIds', title: '学生角色', span: 24, itemRender: { name: '$select', props: { multiple: true, placeholder: '请选择角色' }, options: this.roleOption, optionProps: { value: 'roleId', label: 'roleName' }, } },
        { field: 'status', title: '学生状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
      ]
      this.formData = JSON.parse(JSON.stringify(row))
      // this.formData.roleIds = row.roleIds.map(item => {
      //   return item.roleId
      // })
      this.modalVisible = true;
      // this.$nextTick(() => {
      //   this.$refs.menu.setCheckedKeys(row.menuIds.split(','))
      // })
    },
    handleRecord (row) {
      this.$confirm('请确认是否重置密码为111111?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.studentResetPwd(row).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.studentDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>
