<template>
  <div class="self-popover-main">
    <div class="self-popover-content" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
      <slot />
    </div>
    <el-tooltip v-show="popText !== ''" ref="tooltip" :content="popText" :effect="effect" :placement="placement" :visible-arrow="visibleArrow" :popper-class="popperClass" />
  </div>
</template>

<script>

import { getStyle } from '@/utils/dom'
// import { debounce } from 'throttle-debounce'
import _ from "lodash";

// 兼容子节点样式, 参见 controlledClazzList;
// 当子节点样式不存在时, 使用 self-popover-content来进行判断
export default {
  name: 'SelfTooltip',
  props: {
    placement: {
      type: String,
      default: 'top'
    },
    visibleArrow: {
      type: Boolean,
      default: false
    },
    popperClass: {
      type: String,
      default: ''
    },
    effect: {
      type: String,
      default: 'dark'
    }
  },
  data () {
    return {
      popText: '',
      controlledClazzList: ['self-tooltip', 'with-popover']
    }
  },
  created () {
    this.activateTooltip = tooltip => tooltip.handleShowPopper()
  },
  methods: {
    handleMouseEnter: function (event) {
      const target = event.target
      // 判断是否text-overflow, 如果是就显示tooltip
      let child = target
      for (const clazz of this.controlledClazzList) {
        const tmp = target.querySelector(`.${clazz}`)
        if (tmp) {
          child = tmp
          break
        }
      }
      let heightFlag = false
      if (child.scrollHeight > child.offsetHeight) {
        heightFlag = true
      }
      // use range width instead of scrollWidth to determine whether the text is overflowing
      // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3
      const range = document.createRange()
      range.setStart(child, 0)
      range.setEnd(child, child.childNodes.length)
      const rangeWidth = range.getBoundingClientRect().width // 文本区域宽度
      const padding = (parseInt(getStyle(target, 'paddingLeft'), 10) || 0) +
        (parseInt(getStyle(target, 'paddingRight'), 10) || 0)
      if ((rangeWidth + padding > target.offsetWidth || child.scrollWidth > child.offsetWidth) || heightFlag && this.$refs.tooltip) {
        const tooltip = this.$refs.tooltip
        // TODO 会引起整个 Table 的重新渲染，需要优化
        this.popText = target.innerText || target.textContent
        tooltip.referenceElm = target
        tooltip.$refs.popper && (tooltip.$refs.popper.style.display = 'none')
        tooltip.doDestroy()
        tooltip.setExpectedState(true)
        this.activateTooltip(tooltip)
      }
    },
    handleMouseLeave: function (event) {
      const tooltip = this.$refs.tooltip
      if (tooltip) {
        tooltip.setExpectedState(false)
        tooltip.handleClosePopper()
        this.popText = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin overflow-hide {
  overflow: hidden;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  word-break: break-word;
  white-space: nowrap;
}
.self-popover-content {
  @include overflow-hide;
  div {
    @include overflow-hide;
  }
}
</style>

