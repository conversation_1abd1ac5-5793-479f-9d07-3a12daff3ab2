<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input placeholder="请输入用户名称" v-model="formSearch.userName" clearable
          @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item label="单位" prop="unit">
        <el-input placeholder="请输入单位" v-model="formSearch.unit" clearable
          @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <!-- <el-form-item label="角色" prop="roleId">
        <el-select v-model="formSearch.roleId" placeholder="角色" clearable size="small"
          @change="getData('formSearch')">
          <el-option label="超级管理员" value="1" />
          <el-option v-for="dict in roleOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="用户状态" clearable size="small"
          @change="getData('formSearch')">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData"
      :seq-config="{ startIndex: (formSearch.page - 1) * formSearch.pageSize }" :pager-config="Object.assign(
        { currentPage: formSearch.page, total: tablePage.total },
        formSearch
      )
        " @checkbox-all="selectChangeEvent" @checkbox-change="selectChangeEvent" @page-change="handlePageChange"
      @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{
        size: 'mini',
        custom: true,
        slots: { buttons: 'toolbar_left' },
      }" :checkbox-config="checkboxConfig">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['user/add']" plain icon="el-icon-plus" @click="handleAdd"
          size="mini">新增</el-button>
        <el-button type="primary" v-permissions="['user/edit']" plain icon="el-icon-edit"
          @click="handleEdit($refs.xGrid.getCheckboxRecords()[0])" :disabled="disEdit" size="mini">修改</el-button>
        <el-button type="primary" v-permissions="['user/del']" plain icon="el-icon-delete" @click="handleDel()"
          :disabled="disDel" size="mini">删除</el-button>
        <el-button type="primary" v-permissions="['user/uploadImport']" plain icon="el-icon-folder-opened"
          @click="handleImport" size="mini">导入excel</el-button>
        <el-button type="primary" v-permissions="['user/tempdownload']" plain icon="el-icon-folder-opened"
          @click="$downloadFun.temp('用户管理导入模板.xlsx')" size="mini">导出excel模板</el-button>
      </template>
      <!-- 用户的角色列表 -->
      <template v-slot:userSlot="{ row }">
        <!-- <el-tag v-for="item in row.roleIds" :key="item.roleId">{{item.roleName}}</el-tag> -->
        <el-tag v-show="row.roleIds[0]">{{
          row.roleIds[0] ? row.roleIds[0].roleName : null
        }}</el-tag>
      </template>
      <!-- 用户的状态切换开关 -->
      <template v-slot:statusSlot="{ row }">
        <vxe-switch v-model="row.status" v-if="row.id != 1" open-label="启用" :open-value="1" close-label="停用"
          :close-value="0" @change="statusChange(row)"></vxe-switch>
      </template>
      <!-- 用户的头像 -->
      <template v-slot:avatarSlot="{ row }">
        <!-- <span>{{ row }}</span> -->
        <el-image style="width: 48px; height: 48px" :src="row.avatar
          ? $SOURCEURL + row.avatar
          : require('@/assets/img/portrait.jpg')
          " :preview-src-list="srcList">
        </el-image>
        <!-- <avatar :user="row" /> -->
      </template>

      <template v-slot:sexSlot="{ row }">
        <el-tag v-if="row.sex != null">{{
          row.sex == 0 ? "男" : row.sex == 1 ? "女" : ""
        }}</el-tag>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['user/edit']" icon="el-icon-edit" v-if="row.id != 1"
          @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['user/del']" icon="el-icon-delete" v-if="row.id != 1"
          @click="handleDel(row)">删除</el-button>
        <el-button type="text" v-permissions="['user/resetPwd']" icon="el-icon-refresh-right" v-if="row.id != 1"
          @click="handleReset(row)">重置密码</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer
      @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.userRules"
          title-width="120" title-align="right">
          <template #avatarSlots="{ data }">
            <div style="text-align: center">
              <userAvatar :user="data" />
            </div>
          </template>
          <template #nickNameSlots="{ data }">
            <div style="text-align: center">
              <el-input type="text" v-model="data.nickName" placeholder="请输入昵称" autocomplete="off">
              </el-input>
              <el-input type="text" id="nickName" v-model="nickName" placeholder="请输入昵称" autocomplete="off"
                style="position: fixed; top: -100%; left: -100%">
              </el-input>
            </div>
          </template>
          <template #passwordSlots="{ data }">
            <div style="text-align: center">
              <el-input v-model="data.password" placeholder="请输入密码" show-password
                auto-complete="new-password"></el-input>
            </div>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import userAvatar from "./userAvatar.vue";
import avatar from "./avatar.vue";
export default {
  name: "User",
  components: { ElImageViewer, avatar, userAvatar },
  data() {
    return {
      nickName: null,
      password: null,
      srcList: [],
      modalTitle: "新增用户",
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
      },

      tableColumn: [
        { type: "checkbox", width: 60 },
        { type: "seq", width: 60, title: "序号" },
        {
          field: "avatar",
          width: 120,
          title: "用户头像",
          slots: { default: "avatarSlot" },
        },
        { sortable: true, width: 120, field: "userName", title: "登录名" },
        { sortable: true, width: 120, field: "nickName", title: "用户名" },
        { sortable: true, width: 120, field: "unit", title: "单位" },
        // { sortable: true, field: 'roleName', title: '角色', formatter: ({ row }) => { return row.roleIds.map(item => item.roleName).join(',') } },
        {
          sortable: true,
          width: 120,
          field: "roleName",
          title: "角色",
          slots: { default: "userSlot" },
        },
        {
          field: "status",
          width: 120,
          title: "是否禁用",
          slots: { default: "statusSlot" },
        },
        { field: "phone", width: 120, title: "联系方式" },
        { field: "email", width: 120, title: "邮箱" },
        {
          field: "sex",
          width: 120,
          title: "性别",
          slots: { default: "sexSlot" },
        },
        { sortable: true, width: 120, field: "createBy", title: "创建人" },
        { sortable: true, width: 160, field: "createTime", title: "创建时间" },
        { sortable: true, width: 120, field: "remark", title: "备注" },
        {
          title: "操作",
          width: 220,
          slots: { default: "operate" },
          align: "center",
          fixed: "right",
        },
      ],
      tableData: [],
      formData: {
        userName: "",
        nickName: "",
        password: "",
        phone: 1,
        status: 1,
        email: "",
        roleIds: "",
      },
      formItem: [
        {
          field: "type",
          title: "头像",

          span: 12,
          itemRender: {
            props: {},
          },
          slots: { default: "avatarSlots" },
        },
        {
          field: "userName",
          title: "登录名",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入登录名" },
          },
        },
        {
          field: "nickName",
          title: "用户名",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入用户名" },
          },
        },
        {
          field: "password",
          title: "用户密码",
          span: 12,
          itemRender: {
            name: "$input",
            props: { type: "password", placeholder: "请输入用户密码" },
          },
        },
        {
          field: "phone",
          title: "手机号码",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入手机号码" },
          },
        },
        {
          field: "email",
          title: "邮箱",
          span: 12,
          itemRender: { name: "$input", props: { placeholder: "请输入邮箱" } },
        },
        {
          field: "sex",
          title: "性别",
          span: 12,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择性别" },
            options: [
              { value: "0", label: "男" },
              { value: "1", label: "女" },
            ],
          },
        },
        {
          field: "roleIds",
          title: "用户角色",
          span: 24,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择角色" },
            options: this.roleOption
          },
        },
        {
          field: "status",
          title: "用户状态",
          span: 24,
          itemRender: {
            name: "$switch",
            props: {
              openLabel: "启用",
              closeLabel: "禁用",
              openValue: 1,
              closeValue: 0,
            },
          },
        },
        {
          field: "remark",
          title: "备注",
          span: 24,
          itemRender: { name: "$input", props: { placeholder: "请输入备注" } },
        },
      ],
      formSearch: {
        userName: "",
        status: "",
        page: 1,
        pageSize: 10,
        roleId: "",
        unit: "",
      },
      statusOptions: [
        { value: 1, label: "正常" },
        { value: 0, label: "停用" },
      ],
      disEdit: true, //表格外部编辑按钮是否禁用
      disDel: true, //表格外部删除按钮是否禁用
      roleOption: [],
      checkboxConfig: {
        checkMethod: this.checCheckboxkMethod, // 用于判断是否能勾选
        visibleMethod: this.showCheckboxkMethod  // 可选: 如果需要控制勾选框可见性
      }
    };
  },
  created() {
    this.getRoleOption();
    this.getData();
 
  },
  methods: {
    getRoleOption() {
      this.$http.roleOption().then((res) => {
        this.roleOption = res.data;
        // this.formItem[5].itemRender.options = res.data
      });
    },

    getData(params) {
      //用户列表
      if (params == "formSearch") {
        this.formSearch.page = 1;
      }
      this.loading = true;
      //2022.3.7 czk新增解决vxe-table刷新不自动清除checkbox选中状态问题
      this.disBtnReset();
      this.$http.userList(this.formSearch).then((res) => {
        this.tableData = res.data;
        this.srcList = res.data.map((item) => {
          if (item.avatar) {
            return this.$SOURCEURL + item.avatar;
          } else {
            return null;
          }
        });
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    disBtnReset() {
      this.disEdit = true; //表格外部编辑按钮是否禁用
      this.disDel = true; //表格外部删除按钮是否禁用
    },
    statusChange(row) {
      this.loading = true;
      this.$http.userStatusChange(row).then((res) => {
        this.loading = false;
        this.$msg(res.msg, "success");
      }).catch(err => {
        this.getData()
      });
    },
    //重置按钮
    resetFormSearch() {
      this.$resetForm("queryForm");
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort();
        this.formSearch.sortName = undefined;
        this.formSearch.sortOrder = undefined;
      }
      this.getData();
    },
    selectChangeEvent({ checked, records }) {
      console.log(checked, records);

      this.disEdit = records.length != 1;
      this.disDel = !records.length;
    },
    handleImport(opts = { types: ["xls", "xlsx"] }) {
      this.$refs.xGrid.readFile(opts).then((fileData) => {
        const { files } = fileData;
        let param = new FormData();
        // 将得到的文件流添加到FormData对象
        param.append("file", files[0], files[0].name);
        // param.append('params', JSON.stringify(data))
        this.$http.importUser(param).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },

    //分页
    handlePageChange({ currentPage, pageSize }) {
      this.formSearch.page = currentPage;
      this.formSearch.pageSize = pageSize;
      this.getData();
    },
    //排序
    handleSortChange({ property, order }) {
      this.formSearch.sortName = property;
      this.formSearch.sortOrder = order;
      this.getData();
    },
    save() {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          if (this.modalTitle == "新增用户") {
            this.$http.userAdd(this.formData).then((res) => {
              this.$msg(res.msg, "success");
              this.close();
              this.getData();
            });
          } else {
            this.$http.userEdit(this.formData).then((res) => {
              this.$msg(res.msg, "success");
              this.close();
              this.getData();
            });
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.menuExpand = false;
      this.menuNodeAll = false;
      this.$refs.formRef.clearValidate();
      this.modalVisible = false;
    },
    handleAdd() {
      this.modalTitle = "新增用户";
      this.formItem = [
        {
          field: "avatar",
          title: "",

          span: 24,
          itemRender: {
            props: {},
          },
          slots: { default: "avatarSlots" },
        },
        {
          field: "userName",
          title: "登录名",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入名称", autocomplete: "off" },
          },
        },
        {
          field: "nickName",
          title: "用户名",
          span: 12,
          // itemRender: { name: "$input", props: { placeholder: "请输入昵称",autocomplete:"off" } },
          slots: { default: "nickNameSlots" },
        },
        {
          field: "password",
          title: "用户密码",
          span: 12,
          // itemRender: {
          //     name: "$input",
          //     props: { type: "password", placeholder: "请输入用户密码",autocomplete:"new-password"  },
          // },
          slots: { default: "passwordSlots" },
        },
        {
          field: "phone",
          title: "手机号码",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入手机号码" },
          },
        },
        {
          field: "email",
          title: "邮箱",
          span: 12,
          itemRender: { name: "$input", props: { placeholder: "请输入邮箱" } },
        },
        {
          field: "sex",
          title: "性别",
          span: 12,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择性别" },
            options: [
              { value: "0", label: "男" },
              { value: "1", label: "女" },
            ],
          },
        },
        {
          field: "unit",
          title: "单位",
          span: 24,
          itemRender: { name: "$input", props: { placeholder: "请输入单位" } },
        },
        {
          field: "roleIds",
          title: "用户角色",
          span: 24,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择角色" },
            options: this.roleOption
          },
        },
        {
          field: "status",
          title: "用户状态",
          span: 24,
          itemRender: {
            name: "$switch",
            props: {
              openLabel: "启用",
              closeLabel: "禁用",
              openValue: 1,
              closeValue: 0,
            },
          },
        },
        {
          field: "remark",
          title: "备注",
          span: 24,
          itemRender: { name: "$input", props: { placeholder: "请输入备注" } },
        },
      ];

      this.formData = {
        userName: "",
        nickName: "",
        password: "",
        phone: 1,
        unit: '',
        status: 1,
        email: "",
        sex: "0",
        roleIds: "",
        avatar: null,
      };
      this.modalVisible = true;
    },
    handleEdit(row) {
      this.modalTitle = "编辑用户";
      this.formItem = [
        // { field: 'userName', title: '用户名称', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        {
          field: "avatar",
          title: "",

          span: 24,
          itemRender: {
            props: {},
          },
          slots: { default: "avatarSlots" },
        },
        {
          field: "nickName",
          title: "用户名",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入用户名" },
          },
        },
        // { field: 'password', title: '用户密码', span: 12, itemRender: { name: '$input', props: { type:'password',placeholder: '请输入用户密码' } } },
        {
          field: "phone",
          title: "手机号码",
          span: 12,
          itemRender: {
            name: "$input",
            props: { placeholder: "请输入手机号码" },
          },
        },
        {
          field: "email",
          title: "邮箱",
          span: 12,
          itemRender: { name: "$input", props: { placeholder: "请输入邮箱" } },
        },
        {
          field: "sex",
          title: "性别",
          span: 12,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择性别" },
            options: [
              { value: "0", label: "男" },
              { value: "1", label: "女" },
            ],
          },
        },
        {
          field: "unit",
          title: "单位",
          span: 24,
          itemRender: { name: "$input", props: { placeholder: "请输入单位" } },
        },
        {
          field: "roleIds",
          title: "用户角色",
          span: 24,
          itemRender: {
            name: "$select",
            props: { placeholder: "请选择角色" },
            options: this.roleOption
          },
        },
        {
          field: "status",
          title: "用户状态",
          span: 24,
          itemRender: {
            name: "$switch",
            props: {
              openLabel: "启用",
              closeLabel: "禁用",
              openValue: 1,
              closeValue: 0,
            },
          },
        },
        {
          field: "remark",
          title: "备注",
          span: 24,
          itemRender: { name: "$input", props: { placeholder: "请输入备注" } },
        },
      ];
      this.formData = JSON.parse(JSON.stringify(row));
      this.formData.roleIds = row.roleIds.map((item) => {
        return item.roleId;
      })[0];
      this.modalVisible = true;
      // this.$nextTick(() => {
      //   this.$refs.menu.setCheckedKeys(row.menuIds.split(','))
      // })
    },
    handleReset(row) {
      this.$confirm("请确认是否重置密码为111111?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.userResetPwd(row).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },
    handleDel(row) {
      let ids;
      if (!row) {
        if (this.$refs.xGrid.getCheckboxRecords().length == 0) {
          return this.$msg("请先选择删除项", "warning");
        }
        ids = this.$refs.xGrid
          .getCheckboxRecords()
          .map((item) => {
            return item.id;
          })
          .join(",");
      } else {
        ids = row.id;
      }
      this.$confirm("请确认是否删除此数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.userDel(ids).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },
    // 自定义勾选框是否可勾选
    checCheckboxkMethod(row) {
      return Number(row.row.id) != 1;
    },

    // 自定义勾选框是否可见
    showCheckboxkMethod(row) {
      return Number(row.row.id) != 1;
    }
  },
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>
