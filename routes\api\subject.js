var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/SubjectService");

router.get('/list', tryAsyncErrors(Service.list))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

module.exports = router;
