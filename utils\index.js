const UserService = require("../services/UserService")
const ConfigService = require("../services/ConfigService")
const multer = require("multer");
const fs = require("fs");
const path = require("path");
const uniqid = require('uniqid');
const { customAlphabet } = require('nanoid');
const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)
const IP2Region = require('ip2region').default;
const queryIp = new IP2Region();

function getDomainWithoutPort (url) {
  // 如果没有协议，添加 http:// 作为默认
  if (!/^https?:\/\//i.test(url)) {
    url = 'http://' + url;
  }

  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname; // 返回不带端口的域名或 IP
  } catch (error) {
    console.error("Invalid URL:", error);
    return null; // 返回 null 或处理错误
  }
}
//try catch封装报错
function tryAsyncErrors (handler) {
  return async function (req, res, next) {
    try {
      await handler(req, res, next)
    } catch (error) {
      next(error);
    }
  };
}

//数据树形结构转换
function handleTree (data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
      // console.log(childrenListMap,'childrenListMap');
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList (o) {
    if (childrenListMap[o[config.id]] !== null) {
      if (o.dataValues) {
        o.dataValues[config.childrenList] = childrenListMap[o[config.id]];
      } else {
        o[config.childrenList] = childrenListMap[o[config.id]];
      }
    }
    if (o.dataValues) {
      if (o.dataValues[config.childrenList]) {
        for (let c of o.dataValues[config.childrenList]) {
          adaptToChildrenList(c);
        }
      }
    } else {
      if (o[config.childrenList]) {
        for (let c of o[config.childrenList]) {
          adaptToChildrenList(c);
        }
      }
    }
  }
  return tree;
}

//日志数据处理
/* 
@params req:请求体
        data：响应数据
        flag：判断是成功还是失败请求
*/
async function handleLogInfo (req, data, flag) {
  let url = req.originalUrl.toLowerCase()

  let logInfo
  if (!url) {
    logInfo = {
      userId: 1,
      userName: 'admin',
      operType: '定时任务',
      operStatus: flag ? 1 : 0,
      resParams: JSON.stringify(data || ''),
    }
  } else {
    let userId = 0
    let getIp = req.headers['x-real-ip'] == undefined ? req.headers.host : req.headers['x-real-ip']
    let ip = getDomainWithoutPort(getIp)
    if (url.indexOf('user/login') >= 0) {
      try {
        let userData = await UserService.getUserData(req.body.userName)
        userId = userData ? userData.id : 0
      } catch (error) {
        console.error('获取用户数据失败:', error)
        userId = 0
      }
    } else {
      userId = req.user ? req.user.userId : 0
    }

    let ipAddress = queryIp.search(ip);

    logInfo = {
      userId: userId,
      userName: url.indexOf('user/login') >= 0 ? req.body.userName : (req.user ? req.user.userName : '未登录'),
      hostIp: getIp,
      // hostIp: req.headers.host,
      location: ipAddress.city == ipAddress.isp ? ipAddress.isp : ipAddress.country + ipAddress.province + ipAddress.city + ipAddress.isp,
      operType:
        url.indexOf('add') >= 0
          ? '增加'
          : url.indexOf('edit') >= 0
            ? '编辑'
            : url.indexOf('del') >= 0
              ? '删除'
              : url.indexOf('upload') >= 0
                ? '文件导入'
                : url.indexOf('tempDownload') >= 0
                  ? '模板下载'
                  : url.indexOf('switch') >= 0
                    ? '状态切换'
                    : url.indexOf('record') >= 0
                      ? '重置密码'
                      : url.indexOf('register') >= 0
                        ? '注册用户'
                        : url.indexOf('user/login') >= 0
                          ? '登录'
                          : url.indexOf('backup') >= 0
                            ? '备份/还原'
                            : '其它',
      reqMethod: req.method,
      operStatus: flag ? 1 : 0,
      operUrl: url.split('?')[0],
      operParams: JSON.stringify(req.method == 'GET' ? req.query : req.body),
      resParams: JSON.stringify(data || '')
    }
  }
  return logInfo
}
//头像上传
let uploadImg = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../public/uploads/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      if (file.originalname.indexOf('.') < 0 && file.mimetype == 'image/jpeg') {
        file.originalname += '.png'
      }
      let fileName = 'avatar_' + uniqid() + path.extname(file.originalname);
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname);
    let extArr = ['.jpg', '.jpeg', '.gif', '.png'];
    //file.originalname有可能是blob，所以新增mimetype判断
    if (!extArr.includes(ext) && file.mimetype != 'image/jpeg' && file.mimetype != 'image/png') {
      //拒绝这个文件
      //cb(null, false);
      //当然我们还可以发送一个错误
      return cb(new Error('请上传jpg,png,jpeg等格式文件'), false);
    }
    //接受这个文件
    cb(null, true);
  }
});

//视频文件上传
let uploadVideo = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../public/uploadVideo/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      let fileName = 'video_' + uniqid() + path.extname(file.originalname);
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname).toLowerCase();
    let extArr = ['.mp4', '.ogg', '.webm', '.flv', '.avi'];
    //file.originalname有可能是blob，所以新增mimetype判断
    if (!extArr.includes(ext) && file.mimetype != 'image/jpeg') {
      //拒绝这个文件
      //cb(null, false);
      //当然我们还可以发送一个错误
      return cb(new Error('请上传mp4,ogg,webm等格式文件'), false);
    }
    //接受这个文件
    cb(null, true);
  }
});

//视频文件上传
let uploadExe = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../public/uploadExe/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      let date = new Date();
      let hour = date.getHours().toString().padStart(2, "0");
      let min = date.getMinutes().toString().padStart(2, "0");
      let sec = date.getSeconds().toString().padStart(2, "0");
      let fileName = hour + min + sec + '_' + file.originalname;
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname).toLowerCase();
    let extArr = ['.exe'];
    //file.originalname有可能是blob，所以新增mimetype判断
    if (!extArr.includes(ext)) {
      //拒绝这个文件
      //cb(null, false);
      //当然我们还可以发送一个错误
      return cb(new Error('请上传exe格式文件'), false);
    }
    //接受这个文件
    cb(null, true);
  }
});

let uploadWebRar = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: async function (req, file, cb) {
      const config = await ConfigService.getInfo()
      let prefixPath = path.join(__dirname, "../public/uploadWeb") 
      // let prefixPath = "E:/zk-zkzn-web"
      if(config.webUploadUrl) {
        prefixPath = config.webUploadUrl
      }
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(prefixPath + '/' + year + month + day + '/' + nanoid());
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        try {
          fs.mkdirSync(dir, { recursive: true });
        } catch (error) {
          return cb(new Error(error), false)
        }
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      let date = new Date();
      let hour = date.getHours().toString().padStart(2, "0");
      let min = date.getMinutes().toString().padStart(2, "0");
      let sec = date.getSeconds().toString().padStart(2, "0");
      let fileName = hour + min + sec + '_' + file.originalname;
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname).toLowerCase();
    let extArr = ['.zip', '.rar'];
    //file.originalname有可能是blob，所以新增mimetype判断
    if (!extArr.includes(ext)) {
      //拒绝这个文件
      //cb(null, false);
      //当然我们还可以发送一个错误
      return cb(new Error('请上传zip,rar等格式文件'), false);
    }
    //接受这个文件
    cb(null, true);
  }
});

let uploadFile = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../public/uploadFile/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      if (file.originalname.indexOf('.') < 0 && file.mimetype == 'image/jpeg') {
        file.originalname += '.png'
      }
      let fileName = 'file_' + uniqid() + path.extname(file.originalname);
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname);
    let extArr = ['.doc', '.docx'];
    if (!extArr.includes(ext)) {
      //拒绝这个文件
      return cb(new Error('请上传doc,docx等格式文件'), false);
      //当然我们还可以发送一个错误
      // cb(new Error('请上传doc,docx等格式文件'));
    }
    //接受这个文件
    cb(null, true);
  },
});

let uploadSourceFile = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../uploadSourceFile/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      if (file.originalname.indexOf('.') < 0 && file.mimetype == 'image/jpeg') {
        file.originalname += '.png'
      }
      let fileName = 'source_' + uniqid() + path.extname(file.originalname);
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname).toLowerCase(); // 转为小写
    let extArr = ['.mp4', '.doc', '.docx', '.xls', '.xlsx', '.pdf'];
    if (!extArr.includes(ext)) {
      return cb(new Error('请上传mp4,doc,docx,xls,xlsx,pdf等格式文件'), false);
    }
    cb(null, true);
  },
});

//工艺流程初始化上传
let uploadInitVR = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let dir = path.join(__dirname, "../public/uploadInitVR");
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      let fileName = uniqid() + path.extname(file.originalname);
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
});

//文件导入
let uploadImport = multer({
  storage: multer.diskStorage({
    // 设置文件存储位置
    destination: function (req, file, cb) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      let dir = path.join(__dirname, "../public/uploadImportFile/" + year + month + day);
      // 判断目录是否存在，没有则创建
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      // dir就是上传文件存放的目录
      cb(null, dir);
    },
    // 设置文件名称
    filename: function (req, file, cb) {
      let fileName = 'file_' + uniqid() + path.extname(file.originalname);
      // let fileName = 'file_' + uniqid() + '.zip';
      // fileName就是上传文件的文件名
      cb(null, fileName);
    },
  }),
  //用于过滤文件的函数
  fileFilter: function (req, file, cb) {
    let ext = path.extname(file.originalname);
    let extArr = ['.xls', '.xlsx'];
    if (!extArr.includes(ext)) {
      //拒绝这个文件
      return cb(new Error('请上传xls,xlsx格式文件'), false);
      //当然我们还可以发送一个错误
      // cb(new Error('请上传doc,docx等格式文件'));
    }
    //接受这个文件
    cb(null, true);
  },
});
module.exports = {
  uploadImg,
  uploadVideo,
  uploadInitVR,
  uploadFile,
  uploadSourceFile,
  uploadImport,
  uploadExe,
  uploadWebRar,
  tryAsyncErrors,
  handleLogInfo,
  handleTree
};
