<template>
  <div class="expandBox">
    <el-row style="margin-bottom:10px;">
      <el-col>
        <p v-html="row.questionContent"></p>
      </el-col>
    </el-row>
    <el-row v-for="(item, i) in row.questionAnswers" :key="i" class="questionBox">
      <el-col>
        <el-button :type="item.isRight == '1' ?'success':''" size="mini">{{item.answerNum}}</el-button>
        <div class="qs_contentBox">
          <p v-html="item.answerContent"></p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <span class="title">分析:</span>
        <div class="qs_contentBox">
          <p class="analyBox" v-html="row.questionAnalysis"></p>
        </div>
      </el-col>
    </el-row>
    <div>
      难度: <el-rate class="inlineRate" v-model="row.questionLevel" disabled></el-rate>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tableData: [],
      loading: false,
      formInline: {
        questionContent: '',
        questionType: ''
      },
      dialogVisible: false,
      questionTypeSelect: [
        {
          label: '单选题',
          value: '1'
        },
        {
          label: '多选题',
          value: '2'
        }
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0
      },
      nodeData: '',
      idData: ''
    };
  },
  methods: {
    initData () {
      this.loading = true;
      this.$http
        .queryTopicApi({
          pageSize: this.tablePage.pageSize,
          page: this.tablePage.currentPage,
          questionContent: this.formInline.questionContent,
          questionType: this.formInline.questionType,
          deviceOperationId: String(this.nodeData.id)
        })
        .then(res => {
          // res.
          this.loading = false;
          this.tableData = res.data;
          // this.tablePage.currentPage = res.data.page;
          this.tablePage.totalResult = res.total;
          // this.tablePage.pageSize = res.data.pageSize;

          // console.log(res);
        });
    },
    onSubmit () {
      this.initData();
    },
    Reset () {
      (this.formInline = {
        questionContent: '',
        questionType: ''
      }),
        (this.tablePage = {
          currentPage: 1,
          pageSize: 10,
          totalResult: 0
        }),
        this.initData();
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage;
      this.tablePage.pageSize = pageSize;
      this.initData();
    },
    singleChoiceShowTrue (data, idData) {
      this.nodeData = data;
      this.idData = idData;
      this.initData();
      this.dialogVisible = true;
    },
    singleChoiceShowFalse () {
      this.dialogVisible = false;
    },
    associationOptions () {
      let data = this.$refs.xTableTwo.getCheckboxRecords();
      if (data.length == 0) {
        return this.$msg('请选择关联项', 'warning');
      }
      let form = [];
      data.forEach(item => {
        form.push({
          deviceOperationId: String(this.nodeData.id),
          questionId: String(item.questionId),
          virtualprocessId: this.idData.virtualprocessId,
          deviceId: this.idData.deviceId
        });
      });
      this.$http.addAssociation(form).then(res => {
        console.log(res);
        this.initData();
        this.$parent.initData();
        this.dialogVisible = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
