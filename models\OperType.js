'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class OperType extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.hasMany(models.VirtualData,{foreignKey:'operTypeId',constraints:false})
    }
  }
  OperType.init(
    {         
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },
      operTypeName:{
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "类型名称"
      },      
      remark:{
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "备注"
      },      
    },
    {
      sequelize,
      modelName: 'OperType',
      tableName: 'zk_oper_type',
      comment:'操作类型表',
    }
  )
  return OperType
}
