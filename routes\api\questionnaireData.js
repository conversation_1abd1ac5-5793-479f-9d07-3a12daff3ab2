var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/QuestionnaireDataService");

router.get('/list', tryAsyncErrors(Service.list))
router.get('/byId', tryAsyncErrors(Service.byId))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:questionId', tryAsyncErrors(Service.delete))

module.exports = router;
