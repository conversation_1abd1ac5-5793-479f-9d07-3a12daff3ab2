/**
 * @<NAME_EMAIL>
 * @description router全局配置，如有必要可分文件抽离
 */

import Vue from "vue";
import VueRouter from "vue-router";
import Layout from "@/layouts";
import EmptyLayout from "@/layouts/EmptyLayout";
import { routerMode } from "@/config/settings";
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter);

export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register/index"),
    hidden: true,
  },
  // {
  //   path: "/user/pwd",
  //   hidden: true,
  //   component: () => import("@/views/user/pwd"),
  //   meta: {
  //     title: "密码修改",
  //   },
  // },
  // {
  //   path: "/",
  //   component: Layout,
  //   hidden: true,
  //   redirect: "/personalCenter",
  //   children: [
  //     {
  //       path: "/system/user",
  //       name: "user",
  //       component: () => import("@/views/system/user"),
  //       meta: {
  //         title: "用户管理",
  //         icon: "icon-shouye",
  //         affix: true,
  //         noKeepAlive: true,
  //       },
  //     },
  //   ],
  // },
  {
    path: "/",
    component: Layout,
    hidden: false,
    // redirect: "personalCenter",
    redirect: "/virtual/course",
    children: [
      {
        path: "personalCenter",
        name: "PersonalCenter",
        component: () => import("@/views/personalCenter/index"),
        meta: {
          icon: "icon-shouye",
          title: "个人中心",
          affix: true,
          noKeepAlive: true,
        },
      },
      {
        path: "dictData",
        name: "dictData",
        hidden: true,
        component: () => import("@/views/system/dictData"),
        meta: {
          title: "字典数据",
          // affix: true,
          noKeepAlive: true,
          activeMenu: '/system/dict'
        },
      },
    ],
  },  
  {
    path: "/formEditor",
    component: () => import("@/views/questionnaire/editor"),
    hidden: true,
  },
  {
    path: "/formRender",
    component: () => import("@/views/questionnaire/render"),
    hidden: true,
  },
  {
    path: "/401",
    name: "401",
    component: () => import("@/views/401"),
    hidden: true,
  },
  {
    path: "/developing",
    name: "developing",
    component: () => import("@/views/developing"),
    hidden: true,
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/views/404"),
    hidden: true,
  },
];

/*当settings.js里authentication配置的是intelligence时，views引入交给前端配置*/
export const asyncRoutes = [
  // {
  //   path: "/",
  //   component: Layout,
  //   redirect: "/index",
  //   children: [
  //     {
  //       path: "/index",
  //       name: "Index",
  //       component: () => import("@/views/index/index"),
  //       meta: {
  //         title: "首页",
  //         icon: "home",
  //         affix: true,
  //         noKeepAlive: true,
  //       },
  //     },
  //   ],
  // }, 
  {
    path: "/common",
    component: Layout,
    redirect: "noRedirect",
  },
  {
    path: "/personalCenter",
    component: Layout,
    hidden: false,
    redirect: "personalCenter",
    children: [
      {
        path: "personalCenter",
        name: "PersonalCenter",
        component: () => import("@/views/personalCenter/index"),
        meta: {
          title: "个人中心",
        },
      },
    ],
  },
  {
    path: "*",
    redirect: "/404",
    hidden: true,
  },
];

const router = new VueRouter({
  mode: routerMode,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes,
});
/* const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
}; */

export function resetRouter () {
  router.matcher = new VueRouter({
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  }).matcher;
}

export default router;
