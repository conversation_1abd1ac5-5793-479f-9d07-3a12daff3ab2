var createError = require("http-errors");
var compression = require('compression')
var express = require("express");
var path = require("path");
var cookieParser = require("cookie-parser");
var logger = require("morgan");

//跨域处理
// const cors = require("cors");
// var indexRouter = require("./routes/index");
// var usersRouter = require("./routes/user");
// var sysRouter = require("./routes/system");
// 路由加载
const mount = require('mount-routes')

var app = express();

// view engine setup
app.set("views", path.join(__dirname, "views"));
app.set("view engine", "jade");
//跨域所有
// app.use(cors());
app.all('*', function (req, res, next) {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Headers', 'X-Custom-Header,Origin,Accept, X-Requested-With, Content-Type,Authorization,token,accessToken')
  res.header('Access-Control-Allow-Methods', 'PUT,POST,GET,DELETE,OPTIONS')
  res.header('Access-Control-Allow-Credentials', true)
  // res.header(
  //   'Access-Control-Allow-Headers',
  //   'Access-Control-Allow-Origin,Access-Control-Allow-Headers,X-Custom-Header,Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers,Authorization,token'
  // )
  res.header('Access-Control-Max-Age', 86400)
  if (req.method == 'OPTIONS') {
    res.sendStatus(200)
  } else {
    next()
  }
})

// 初始化统一响应机制
const resextra = require('./utils/resextra')
app.use(resextra)

app.use(logger("dev"));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(compression())
app.use(cookieParser());
app.use(express.static(path.join(__dirname, "public")));
app.use('/uploadFile', express.static('public/uploadFile'))
app.use('/uploadVideo', express.static('public/uploadVideo'))
app.use('/uploadSourceFile', express.static('./uploadSourceFile'))
app.use('/virtualWeb', express.static('public/uploadWeb'))

//效验token
const checkAuth = require('./utils/checkAuth')
app.use(checkAuth);

//权限处理
const Permission = require("./utils/permission");
app.use(Permission);

//处理分页参数为数字类型以及自动添加请求用户信息
const pagingAndReqUser = require('./utils/pagingAndReqUser')
app.use(pagingAndReqUser)

const UserService = require('./services/UserService')
const { tryAsyncErrors } = require(path.join(process.cwd(), "/utils/index"))
app.use('/api/login', tryAsyncErrors(UserService.login))

// 添加API路由
const apiRoutes = require('./routes/api/index')
app.use('/api', apiRoutes)

// 带路径的用法并且可以打印出路有表
mount(app, path.join(process.cwd(), '/routes'), false)

// catch 404 and forward to error handler
app.use(function (req, res, next) {
  next(createError(404));
});

// error handler
const { Log, sequelize: $seq } = require('./models')
const { handleLogInfo } = require('./utils/index')
app.use(async function (err, req, res, next) {
  let url = req.originalUrl.toLowerCase();
  if (url.indexOf("/upload") == -1) {
    console.log('\x1b[40;31m ---------------------Error---------------------\x1b[0m');
    console.log('\x1b[41;30m [ERROR:] \x1b[40;31m ', err, '\x1b[0m\n');
    console.log('\x1b[41;30m', '[ERROR-NAME:] \x1b[40;31m ' + err.name, '\x1b[0m\n');
    console.log('\x1b[41;30m', '[ERROR-MESSAGE:] \x1b[40;31m ' + err.message, '\x1b[0m');
    console.log('\x1b[40;31m ---------------------Error---------------------\x1b[0m');
  }

  //err请求记录到数据库  
  try {
    const notLogUrl = ["/api/system/getRoutes", "/api/user/info"];
    if (
      url.indexOf("list") == -1 &&
      url.indexOf("/dict/") == -1 &&
      !notLogUrl.includes(url.split("?")[0]) &&
      url.indexOf("/upload") == -1
    ) {
      let logInfo = await handleLogInfo(req, err, false)
      await Log.create(logInfo)
    }
  } catch (error) {
    return res.send({ code: -1, msg: error.message || '未知异常' })
  }

  //效验token
  if (err.name === "UnauthorizedError") {
    if (err.message === "jwt expired") {
      res.send({ code: 402, msg: "登录令牌已过期，请重新登录" });
    } else {
      res.status(201).send({ code: -1, msg: err.message });
    }
  } else if (err.name === "SequelizeValidationError") {
    res.status(201).send({ code: -1, msg: err.message });
  } else {
    res.locals.message = err.message;
    res.locals.error = req.app.get("env") === "development" ? err : {};
    // render the error page
    res.status(err.status || 404)
    res.send({ code: -1, msg: err.message || '未知异常' });
    //下面注释了渲染错误页面
    // res.render("error");
  }
});

module.exports = app;
