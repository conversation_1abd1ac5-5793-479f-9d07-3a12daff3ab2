const { Log, sequelize: $seq } = require('../models')
const { handleLogInfo } = require('./index')
const notLogUrl = ['/api/system/getRoutes', '/api/user/info', '/api/questionnaire/add', '/api/questionnaire/edit', '/api/questionnaire/list']

module.exports = function (req, res, next) {
  //通常成功请求调用
  res.sendSuccess = async function (data) {
    try {
      let url = req.originalUrl.toLowerCase()
      //通常的查询请求不记录
      if (url.indexOf('list') == -1 && url.indexOf('/dict/') == -1 && !notLogUrl.includes(url.split('?')[0]) && url.toLowerCase().indexOf('upload') == -1) {
        let logInfo = await handleLogInfo(req, data, true)

        await Log.create(logInfo)
      }
      // if(data.code != 200) {
      //   let logInfo = handleLogInfo(req, data, false)
      //   await Log.create(logInfo)
      // }
      res.send(data)
    } catch (error) {
      return next(error)
    }
  }
  next()
}