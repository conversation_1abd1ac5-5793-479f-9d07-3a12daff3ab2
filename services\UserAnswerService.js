const { <PERSON>r<PERSON><PERSON><PERSON>, User, User<PERSON><PERSON><PERSON>, VirtualProcessDeviceOper, Question, QuestionAnswer, sequelize: $seq } = require('../models')
class UserAnswerService {
  static async create (req, res) {
    let userId = req.user.userId
    let deviceOperationId = req.body[0].deviceOperationId
    let tableData = req.body.map(item => {
      //关于此处的checkboxData加了sort()方法的原因是，可能传递的数组顺序不一致，导致对比出错
      return { userId, deviceOperationId: item.deviceOperationId, answerIds: (item.questionType == 1 ? item.radioData : item.checkboxData.sort().join()), questionId: item.questionId }
    })
    let insertData = {
      userId,
      deviceOperationId,
      userAnswers: tableData
    }
    let data = await UserExam.create(insertData, {
      include: {
        model: UserAnswer,
        as: 'userAnswers'
      }
    })
    // await $seq.query(`UPDATE zk_user_exam ue JOIN (SELECT SUM(e.answerIds = t.trueAnswer) trueCount,SUM(e.answerIds != t.trueAnswer) falseCount FROM zk_user_answer e INNER JOIN v_question_true t ON e.questionId = t.questionId WHERE e.examId = ${data.examId}) qt SET ue.isTrueNum = qt.trueCount,ue.isFalseNum = qt.falseCount WHERE ue.examId = ${data.examId}`)
    await $seq.query(`UPDATE zk_user_exam ue JOIN (SELECT SUM(e.answerIds = t.trueAnswer) trueCount,SUM(e.answerIds != t.trueAnswer) falseCount FROM zk_user_answer e INNER JOIN zk_question t ON e.questionId = t.questionId WHERE e.examId = ${data.examId}) qt SET ue.isTrueNum = qt.trueCount,ue.isFalseNum = qt.falseCount WHERE ue.examId = ${data.examId}`)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })


  }
  static async update (req, res) {
    let data = await UserAnswer.update(req.body, {
      where: {
        commentId: req.body.commentId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { commentId } = req.params
    let data = await UserAnswer.destroy({
      where: {
        commentId
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { articleId, page, pageSize = 10 } = req.body
    if (page && pageSize) {
      let { rows, count } = await UserAnswer.findAndCountAll({
        include: [
          {
            model: User,
            attributes: []
          },
        ],
        attributes: {
          include: ['User.userName', 'User.nickName', 'User.avater']
        },
        where: {
          articleId
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
        raw: true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let { rows, count } = await UserAnswer.findAndCountAll({
        include: [
          {
            model: User,
            attributes: []
          },
        ],
        attributes: {
          include: ['User.userName', 'User.nickName', 'User.avatar']
        },
        where: {
          articleId
        },
        distinct: true,
        raw: true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    }
  }
  static async queryAnswer (req, res) {
    let data = await UserExam.findAll({
      include: [
        { model: User, attributes: [], required: true },
        { model: VirtualProcessDeviceOper, attributes: [], required: true },
        { model: UserAnswer, as: 'userAnswers', attributes: [] },
      ],
      attributes: {
        include: ['User.userName', 'User.nickName', 'User.avatar', 'VirtualProcessDeviceOper.operationName',
          [$seq.fn('COUNT', 0), 'answerCount']
        ]
      },
      group: 'UserExam.examId',
      distinct: true,
      raw: true
    })
    let total = await UserExam.count()
    res.sendSuccess({ code: 200, data, total, msg: '成功' })
  }
  static async userAnswerList (req, res) {
    let { examId, userId } = req.body
    let data = await UserAnswer.findAll({
      include: [
        {
          model: Question,
          attributes: [],
        },
        {
          model: QuestionAnswer,
          as: 'questionAnswers',
        }
      ],
      attributes: {
        include: [
          [$seq.col('Question.questionContent'), 'questionContent'],
          [$seq.col('Question.questionType'), 'questionType'],
          [$seq.col('Question.classType'), 'classType'],
          [$seq.col('Question.questionLevel'), 'questionLevel'],
          [$seq.col('Question.questionLevel'), 'level'],
          [$seq.col('Question.questionAnalysis'), 'analysis'],
          [$seq.col('Question.questionAnalysis'), 'questionAnalysis'],
          // [$seq.col('Question.questionAnalysis'), 'checkboxData']
        ]
      },
      where: {
        examId, userId
      },
      distinct: true,
      // raw: true
    })
    data.map(item => {
      if (item.dataValues.questionType == '1') {
        // item.dataValues.radioData = item.dataValues.answerIds
        item.setDataValue('radioData', item.dataValues.answerIds)
      } else if (item.dataValues.questionType == '2') {
        // item.dataValues.checkboxData = item.dataValues.answerIds.split(',')
        item.setDataValue('checkboxData', item.dataValues.answerIds.split(',').map(item => { return Number(item) }))
      }
    })
    res.sendSuccess({ code: 200, data, msg: '成功' })
  }
}

module.exports = UserAnswerService
