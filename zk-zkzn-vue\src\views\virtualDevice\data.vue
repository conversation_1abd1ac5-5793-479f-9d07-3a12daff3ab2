<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="编号" prop="id">
        <el-input v-model="formSearch.id" placeholder="请输入编号" clearable size="small" @change="getData()"></el-input>
      </el-form-item>
      <el-form-item label="名称" prop="virtualName">
        <el-input v-model="formSearch.virtualName" placeholder="请输入名称" clearable size="small" @keyup.enter.native="() => { isExpand = false; getData(); }
          "></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="菜单状态" clearable size="small" @change="getData()">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="同级树节点只展开一个" prop="accordionTree">
        <el-select v-model="formSearch.accordionTree" placeholder="" size="small" @change="accordionTreeChange">
          <el-option v-for="dict in [
            { value: true, label: '是' },
            { value: false, label: '否' },
          ]" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData()">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="() => { this.$resetForm('queryForm'), getData(); }">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid id="virtual_tab" :custom-config="{ storage: true }" ref="xTree" :height="tableHeight" :id="'code'" keep-source :columns="tableColumn" :data="tableData" v-loading="loading" :scroll-y="{ enabled: true, mode: 'wheel' }" :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }" :row-config="{ keyField: 'code' }" :tree-config="{
        transform: true,
        rowField: 'id',
        parentField: 'pId',
        reserve: formSearch.reserveTree,
        accordion: formSearch.accordionTree,
        iconOpen: 'vxe-icon--arrow-bottom',
        iconClose: 'vxe-icon--arrow-right',
        iconLoaded: 'vxe-icon--refresh roll',
      }" :toolbarConfig="{
        size: 'mini',
        custom: true,
        slots: { buttons: 'toolbar_left' },
      }">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['virtualData/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <el-button type="info" plain icon="el-icon-sort" :disabled="formSearch.accordionTree" @click="toggleExpandAll" size="mini">展开/折叠</el-button>
        <el-button type="primary" v-permissions="['virtualData/edit']" plain icon="el-icon-success" @click="saveUpdate" size="mini">保存</el-button>
        <input ref="uploadRef" id="upload" type="file" style="display: none" @change="importFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
      </template>
      <template #idSlot="{ row }">
        <el-tag :type="row.level == 1 ? '' : row.level == 2 ? 'warning' : 'success'">{{ row.id }}</el-tag>
      </template>

      <template v-slot:statusSlot="{ row }">
        <vxe-switch v-model="row.status" open-label="启用" open-value="1" close-label="停用" close-value="2" @change="statusChange(row)"></vxe-switch>
      </template>
      <template #imgSlot="{ row }">
        <el-image style="height: 48px; vertical-align: bottom" :fit="'contain'" :src="row.pic ? $SOURCEURL + row.pic : ''" :preview-src-list="srcList">
          <div slot="error" class="el-image__error">
            <i class="el-icon-picture-outline" style="min-width: 50px; font-size: 32px"></i>
          </div>
        </el-image>
      </template>
      <template #operate="{ row }">
        <el-button type="text" icon="el-icon-cpu" @click="handleProgramLoad(row)">载入程序</el-button>
        <el-button type="text" v-permissions="['virtualData/add']" icon="el-icon-plus" @click="handleChildAdd(row)">新增下级</el-button>
        <el-button type="text" v-permissions="['virtualData/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-dropdown style="margin-left: 10px" @command="handleCommand($event, row)" placement="bottom">
          <el-button type="text">更多<i class="el-icon-d-arrow-right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="h"><i class="el-icon-link"></i>访问web地址</el-dropdown-item>
            <el-dropdown-item command="g" v-permissions="['virtualData/editBatch']"><i class="el-icon-edit"></i>批量修改数据</el-dropdown-item>
            <el-dropdown-item command="f" v-permissions="['virtualData/add']"><i class="el-icon-document-copy"></i>复制</el-dropdown-item>
            <el-dropdown-item command="a" v-permissions="['virtualData/uploadImport']"><i class="el-icon-upload"></i>导入子数据</el-dropdown-item>
            <el-dropdown-item command="d" v-permissions="['virtualData/sort']"><i class="el-icon-refresh"></i>更新子数据排序号</el-dropdown-item>
            <el-dropdown-item command="e" v-permissions="['virtualData/sortValid']"><i class="el-icon-warning"></i>效验排序字段是否重复</el-dropdown-item>
            <el-dropdown-item command="b" v-permissions="['virtualData/childDel']"><i class="el-icon-delete"></i>删除子数据</el-dropdown-item>
            <el-dropdown-item command="c" v-permissions="['virtualData/del']"><i class="el-icon-delete"></i>删除自身及子数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <el-button type="text" icon="el-icon-delete" @click="handleDel(row)">删除</el-button> -->
      </template>
    </vxe-grid>

    <vxe-modal ref="xModal" width="800" height="80vh" :title="modalTitle" v-model="modalVisible" @show="() => { this.$xModalOpen('xModal'); }" show-footer resize remember @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.virtualDataRules" title-width="120" title-align="right">
          <template #treeSelect="{ data }">
            <tree-select :options="menuOptions" v-model="data.pId" @select="treeSelectNode" :show-count="true" :normalizer="normalizer" placeholder="选择上级" />
          </template>
          <template #cateSelect="{ data }">
            <tree-select :options="virtualCateOption" v-model="data.virtualCateId" @select="cateSelectNode" @input="cateInputNode" :show-count="true" :normalizer="cateNormalizer" placeholder="选择分类" />
          </template>
          <template #uploadImg="{ data }">
            <el-upload class="avatar-uploader" :headers="{ token: $store.getters['user/accessToken'] }" accept=".jpg,.png,.jpeg" :action="$BASEURL + '/file/upload'" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <div class="avaterBox">
                <img v-if="data.pic" :src="$SOURCEURL + data.pic" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div v-if="data.pic" class="avaterMask" @click.stop="delUploadImg">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div>
            </el-upload>
          </template>

          <!-- 视频上传 -->
          <template #uploadVideo="{ data }">
            <el-upload class="avatar-uploader" :headers="{ token: $store.getters['user/accessToken'] }" :accept="uploadVideoAccept" :action="$BASEURL + '/file/uploadVideo'" :show-file-list="false" :on-success="handleAvatarVideoSuccess" :before-upload="beforeAvatarVideoUpload" :on-progress="uploadVideoProcess">
              <div class="avaterBox">
                <video v-if="data.videoSrc" :src="$SOURCEURL + data.videoSrc" controls="controls" class="avatar">
                  您的浏览器不支持视频播放
                </video>
                <i v-if="!data.videoSrc && !videoFlag" class="el-icon-plus avatar-uploader-icon"></i>
                <el-progress v-if="videoFlag == true" type="circle" :percentage="videoUploadPercent" style="margin-top: 7px"></el-progress>
                <div v-if="data.videoSrc" class="avaterMask" @click.stop="delUploadVideo">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div>
            </el-upload>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>

    <vxe-modal ref="xModal2" width="800" height="70vh" title="批量修改" v-model="modalBatchVisible" @show="() => { this.$xModalOpen('xModal2'); }" show-footer resize remember transfer @close="closeBatch">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef2" :data="formDataBatch" :items="formItemBatch" :rules="$rules.virtualClassRules" title-width="120" title-align="right">
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="saveBatch">保存</el-button>
          <el-button @click="closeBatch">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import protocolCheck from "@/protocolCheck/index.js";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import eventBus from "@/utils/eventBus";

export default {
  name: "Data",
  components: { ElImageViewer },
  data () {
    return {
      normalizer (node) {
        return {
          id: node.id,
          label: node.virtualName,
          children: node.children,
        };
      },
      cateNormalizer (node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children,
        };
      },
      loading: false,
      isExpand: false,
      showViewer: false,
      srcList: [],
      uploadVideoAccept: ".mp4,.webm,.ogg",
      videoFlag: false,
      //是否显示进度条
      videoUploadPercent: "",
      menuOptions: [],
      tableColumn: [
        { field: "id", title: "编号", treeNode: true, align: "left", width: 160, slots: { default: "idSlot" }, editRender: { name: "$input", props: { placeholder: "请输入" } } },
        { field: "virtualName", title: "名称", width: 180, editRender: { name: "$input", props: { placeholder: "请输入" } }, },
        { field: "description", title: "简介", width: 200, editRender: { name: "$input", props: { placeholder: "请输入" } }, },
        { field: "pic", title: "图片", showOverflow: true, slots: { default: "imgSlot" }, minWidth: 80, },
        { field: "assetName", title: "资源名称", width: 120, editRender: { name: "$input", props: { placeholder: "请输入" } }, },
        { field: "webUrlId", title: "web地址", width: 120, editRender: { name: "$select", props: { placeholder: "请选择" }, options: [], }, formatter: ({ row }) => { return this.webUrlConvertName(row.webUrlId) } },
        { field: "prefixId", title: "程序协议", width: 120, editRender: { name: "$select", props: { placeholder: "请选择" }, options: [], }, formatter: ({ row }) => { return this.prefixConvertName(row.prefixId) } },
        { field: "operName", title: "操作名称", width: 120, editRender: { name: "$input", props: { placeholder: "请输入" } }, },
        { field: "operTypeId", title: "操作类型", width: 120, editRender: { name: "$select", props: { placeholder: "请选择" }, options: [], }, formatter: ({ row }) => { return this.convertName(row.operTypeId); }, },
        { field: "sortBy", title: "排序", width: 80, editRender: { name: "$input", props: { type: "integer", placeholder: "请输入" }, }, },
        { field: "status", title: "状态", width: 100, slots: { default: "statusSlot" }, },
        { title: "操作", minWidth: 280, fixed: "right", slots: { default: "operate" }, },
      ],
      tableData: [],
      modalTitle: "",
      modalVisible: false,
      formSearch: {
        id: null,
        virtualName: "",
        status: "",
        accordionTree: false,
        reserveTree: true,
      },
      statusOptions: [
        { value: 1, label: "正常" },
        { value: 2, label: "停用" },
      ],
      formData: {},
      formItem: [
        { field: "pId", title: "上级", span: 24, slots: { default: "treeSelect" }, },
        { field: "virtualName", title: "名称", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入名称" } }, },
        // { field: "virtualId", title: "进程id", span: 12, itemRender: { name: "$input", props: { type: "integer", placeholder: "请输入进程id" } }, },
        // { field: "virtualParentId", title: "父进程id", span: 12, itemRender: { name: "$input", props: { type: "integer", placeholder: "请输入父进程id" } }, },
        { field: "assetName", title: "资源名称", span: 12, itemRender: { name: "$input", props: { placeholder: "请输入资源名称" } }, },
        // { field: 'id', title: '编号', span: 24, itemRender: { name: '$input', props: { placeholder: '自动生成编号', disabled: true } } },
        { field: "operName", title: "操作名称", span: 12, itemRender: { name: "$input", props: { placeholder: "请输入操作名称" }, }, },
        { field: "virtualCateId", title: "学科分类", span: 12, slots: { default: "cateSelect" } },
        { field: "sortBy", title: "排序字段", span: 12, itemRender: { name: "$input", props: { placeholder: "请输入排序字段" }, }, },
        { field: "description", title: "简介", span: 24, itemRender: { name: "$textarea", props: { placeholder: "请输入介绍信息" }, }, },
        { field: "isExecutable", title: "是否为课件", span: 12, itemRender: { name: "$radio", options: [{ label: "是", value: 1 }, { label: "否", value: 0 },], events: { change: this.isExecutableChange } }, },
        { field: "isHostShow", title: "强制主目录显示", span: 12, itemRender: { name: "$radio", options: [{ label: "是", value: 1 }, { label: "否", value: 0 },], }, },
        { field: "isPlatform", title: "平台", span: 12, visibleMethod: ({ data }) => { return data.isExecutable == 1 }, itemRender: { name: "$checkbox", options: [{ label: "web", value: '1' }, { label: "window", value: '2' }], events: { change: this.isPlatformChange } }, },
        { field: "status", title: "状态", span: 12, itemRender: { name: "$radio", options: [{ label: "正常", value: "1" }, { label: "停用", value: "2" },], }, },
        // { field: "webSrc", title: "web地址", span: 24, visibleMethod: ({ data }) => { return data.isPlatform.includes('1'); }, itemRender: { name: "$input", props: { placeholder: "请输入web地址" }, }, },
        { field: "webUrlId", title: "web地址", span: 24, visibleMethod: ({ data }) => { return data.isExecutable == 1 && data.isPlatform.includes('1'); }, itemRender: { name: "$select", props: { placeholder: "请选择web地址" }, options: [] }, },
        { field: "webParams", title: "web参数", span: 24, visibleMethod: ({ data }) => { return data.isExecutable == 1 && data.isPlatform.includes('1'); }, itemRender: { name: "$input", props: { placeholder: "请输入web参数" }, }, },
        { field: "prefixId", title: "外链路径", span: 24, visibleMethod: ({ data }) => { return data.isExecutable == 1 && data.isPlatform.includes('2'); }, itemRender: { name: "$select", props: { placeholder: "请输入外链路径" }, options: [] }, },
        { field: "linkSrc", title: "外链参数", span: 24, visibleMethod: ({ data }) => { return data.isExecutable == 1 && data.isPlatform.includes('2'); }, itemRender: { name: "$input", props: { placeholder: "请输入外链参数" }, }, },
        { field: "pic", title: "图片", span: 24, slots: { default: "uploadImg" }, },
        { field: "videoSrc", title: "视频", span: 24, slots: { default: "uploadVideo" }, },
        { field: "remark", title: "备注", span: 24, itemRender: { name: "$textarea", props: { placeholder: "请输入备注" }, }, },
        // { field: 'status', title: '状态', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
      ],
      modalBatchVisible: false,
      formDataBatch: {},
      formItemBatch: [
        { field: "virtualName", title: "名称", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入名称", disabled: true } } },
        { field: "isExecutable", title: "是否可执行", span: 24, itemRender: { name: "$radio", options: [{ label: "是", value: 1 }, { label: "否", value: 0 },], }, },
        { field: "isPlatform", title: "平台", span: 12, itemRender: { name: "$checkbox", options: [{ label: "web", value: '1' }, { label: "window", value: '2' }], events: { change: this.isPlatformChange } }, },
        // { field: "webSrc", title: "web地址", span: 24, visibleMethod: ({ data }) => { return data.isPlatform.includes('1'); }, itemRender: { name: "$input", props: { placeholder: "请输入web地址" }, }, },
        { field: "webUrlId", title: "web地址", span: 24, visibleMethod: ({ data }) => { return data.isPlatform.includes('1'); }, itemRender: { name: "$select", props: { placeholder: "请选择web地址" }, options: [] }, },
        { field: "prefixId", title: "外链路径", span: 24, visibleMethod: ({ data }) => { return data.isPlatform.includes('2'); }, itemRender: { name: "$select", props: { placeholder: "请输入外链路径" }, options: [] }, },
        // { field: "linkSrc", title: "外链参数", span: 24, visibleMethod: ({ data }) => { return data.isPlatform.includes('2'); }, itemRender: { name: "$input", props: { placeholder: "请输入外链参数" }, }, },
        { field: "batchEditMode", title: "批量修改模式", span: 24, itemRender: { name: "$radio", options: [{ label: "自身及子数据", value: "1" }, { label: "子数据", value: "2" }] } },
        { field: "isCover", title: "是否全覆盖", span: 24, itemRender: { name: "$radio", options: [{ label: "全覆盖", value: "1" }, { label: "平台字段为空的才修改", value: "2" }] } },
      ],
      prefixOption: [],
      uploadId: "",
      uploadLevel: "",
      operTypeOption: [],
      pathWebOption: [],
      virtualCateOption: [],
    };
  },
  created () {
    this.getPathPrefixOption();
    this.getPathWebOption();
    this.getVirtualCateOption();
    this.getOperTypeOption();
    this.getData();
  },
  computed: {
    tableHeight () {
      return document.body.clientHeight - 210;
    },
  },
  methods: {
    getData (params) {
      this.loading = true;
      this.$http.virtualDataList(this.formSearch).then((res) => {
        this.tableData = res.data;
        //单独写的判断是否展开树形表格
        if (this.formSearch.virtualName) {
          this.isExpand = true;
          setTimeout(() => {
            this.$refs.xTree.setAllTreeExpand(true);
            this.loading = false;
          }, 500);
        } else {
          this.loading = false;
        }
        this.srcList = [];
        this.deepDataPic(res.data, this.srcList);
        // this.loading = false;
      });
    },
    getPathPrefixOption () {
      this.$http.pathPrefixOption().then((res) => {
        this.prefixOption = res.data
        let index = this.formItem.findIndex((item) => item.field == "prefixId");
        this.formItem[index].itemRender.options = res.data;

        let index2 = this.formItemBatch.findIndex((item) => item.field == "prefixId");
        this.formItemBatch[index2].itemRender.options = res.data;

        let preIndex = this.tableColumn.findIndex(
          (item) => item.field == "prefixId"
        );
        if (preIndex != -1) {
          this.tableColumn[preIndex].editRender.options = this.prefixOption;
        }
      });
    },
    getPathWebOption () {
      this.$http.pathWebOption().then((res) => {
        this.pathWebOption = res.data
        let index = this.formItem.findIndex((item) => item.field == "webUrlId");
        this.formItem[index].itemRender.options = res.data;

        let index2 = this.formItemBatch.findIndex((item) => item.field == "webUrlId");
        this.formItemBatch[index2].itemRender.options = res.data;

        let preIndex = this.tableColumn.findIndex(
          (item) => item.field == "webUrlId"
        );
        if (preIndex != -1) {
          this.tableColumn[preIndex].editRender.options = this.pathWebOption;
        }
      });
    },
    getVirtualCateOption () {
      this.$http.virtualCateOption().then((res) => {
        this.virtualCateOption = res.data
      });
    },
    getOperTypeOption () {
      this.$http.operTypeOption().then((res) => {
        this.operTypeOption = res.data;
        let index = this.tableColumn.findIndex(
          (item) => item.field == "operTypeId"
        );
        if (index != -1) {
          this.tableColumn[index].editRender.options = this.operTypeOption;
        }
      });
    },
    convertName (id) {
      let obj = this.operTypeOption.find((item) => item.value == id);
      return obj ? obj.label : "";
    },
    webUrlConvertName (id) {
      let obj = this.pathWebOption.find((item) => item.value == id);
      return obj ? obj.label : "";
    },
    prefixConvertName (id) {
      let obj = this.prefixOption.find((item) => item.value == id);
      return obj ? obj.label : "";
    },
    //获取表格更新数据
    async saveUpdate () {
      let record = await this.$refs.xTree.getUpdateRecords();
      record.forEach((item) => {
        if (item.children) {
          delete item.children;
        }
        if (item._X_ROW_CHILD) {
          delete item._X_ROW_CHILD;
        }
      });
      await this.$http.virtualDataEdit(record);
      this.$msg("操作成功", "success");
      this.getData();
    },
    accordionTreeChange (val) {
      this.formSearch.reverseTree = !val;
    },
    handleCommand (command, row) {
      switch (command) {
        case "a":
          if (!row.id || !row.level) {
            return this.$msg("上传所需参数缺失，请刷新后再试", "warning");
          }
          this.uploadId = row.id;
          this.uploadLevel = row.level;
          this.$refs.uploadRef.click();
          break;
        case "b":
          if (!row.id) return this.$msg("id参数缺失，请刷新后再试", "warning");
          this.handleChildDel(row.id);
          break;
        case "c":
          this.handleDel(row);
          break;
        case "d":
          this.$http.virtualDataSort({ id: row.id }).then((res) => {
            this.$msg("更新排序成功");
            this.getData();
          });
          break;
        case "e":
          this.$http.virtualDataSortValid({ id: row.id }).then((res) => {
            if (res.data && res.data.length > 0) {
              let str = "编号" + row.id + "下级数据排序号";
              for (let i = 0; i < res.data.length; i++) {
                str += "【" + res.data[i].sortBy + "】";
              }
              str += "存在重复";
              this.$msg(str, "warning");
            } else {
              this.$msg("编号" + row.id + "下级数据无重复排序号", "success");
            }
          });
          break;
        case "f":
          let form = JSON.parse(JSON.stringify(row));
          form.id = null;
          if (form.children) {
            delete form.children;
          }
          if (form._X_ROW_CHILD) {
            delete form._X_ROW_CHILD;
          }
          this.$http.virtualDataAdd(form).then((res) => {
            this.getData();
          });
          break;
        case "g":
          this.formDataBatch = JSON.parse(JSON.stringify(row));
          this.formDataBatch.isPlatform = this.formDataBatch.isPlatform ? this.formDataBatch.isPlatform.split(',') : []
          this.$set(this.formDataBatch, 'batchEditMode', '1')
          this.$set(this.formDataBatch, 'isCover', '2')
          this.modalBatchVisible = true;
          break;
        case "h":
          let mode = 4
          let process = 1
          if (row.virtualParentId == 0) {
            mode = 4
            process = row.virtualId
          } else {
            mode = 1
            process = row.virtualParentId
          }

          window.open(
            row.webUrl +
            `?access_token=${this.$store.getters["user/accessToken"]
            }&asset=${row.assetName
            }&mode=${mode
            }&process=${process
            }&device=${row.virtualId
            }`,
            "_blank"
          );
          break;
      }
    },
    importFile (e) {
      let file = e.currentTarget.files[0];
      let param = new FormData();
      // 将得到的文件流添加到FormData对象
      param.append("file", file);
      param.append("uploadId", this.uploadId);
      param.append("uploadLevel", this.uploadLevel);
      e.target.value = "";
      this.loading = true;
      this.$http
        .virtualDataImport(param)
        .then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleAvatarSuccess (res, file) {
      this.formData.pic = res.url;
      this.$message.success("上传成功！");
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过5MB!");
      }
      return isLt2M;
    },
    delUploadImg (e) {
      this.formData.pic = "";
    },
    // 视频上传
    handleAvatarVideoSuccess (res, file) {
      if (res.url) {
        this.formData.videoSrc = res.url;
        this.$message.success("上传成功！");
      } else {
        if (res.msg) {
          this.$msg(res.msg, "error");
        }
      }
      this.videoFlag = false;
      this.videoUploadPercent = 0;
    },
    //进度条
    uploadVideoProcess (event, file, fileList) {
      this.videoFlag = true;
      this.videoUploadPercent = file.percentage.toFixed(0) * 1;
    },
    beforeAvatarVideoUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 1500;
      if (!isLt2M) {
        this.$message.error("上传视频大小不能超过1500MB!");
        return false;
      }
      if (["video/mp4", "video/ogg", "video/webm"].indexOf(file.type) == -1) {
        this.$msg("请上传mp4，ogg，webm格式视频！", "warning");
        return false;
      }
      return isLt2M;
    },
    delUploadVideo (e) {
      this.formData.videoSrc = "";
    },
    statusChange (row) {
      let form = {
        id: row.id,
        pId: row.pId + "",
        status: row.status,
      };
      this.loading = true
      this.$http.virtualDataStatus(form).then((res) => {
        // const regex = new RegExp(`^${row.id}`);
        // if (row.status == "1") {
        //   this.tableData.forEach((item) => {
        //     if (regex.test(item.pId)) {
        //       item.status = "1";
        //     }
        //   });
        // } else if (row.status == "2") {
        //   this.tableData.forEach((item) => {
        //     if (regex.test(item.pId)) {
        //       item.status = "2";
        //     }
        //   });
        // }
        this.$msg("操作成功", "success");
        this.getData()
      }).catch((err) => {
        row.status = row.status == "1" ? "2" : "1";
      });
    },
    //递归获取树形结构图片信息
    deepDataPic (data, arr = []) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].pic) {
          arr.push(this.$SOURCEURL + data[i].pic);
        }
        if (data[i].children && data[i].children.length > 0) {
          this.deepDataPic(data[i].children, arr);
        }
      }
    },
    toggleExpandAll () {
      this.loading = true;
      this.isExpand = !this.isExpand;
      setTimeout(() => {
        if (this.isExpand) {
          this.$refs.xTree.setAllTreeExpand(true);
        } else {
          this.$refs.xTree.clearTreeExpand();
        }
        this.loading = false;
      }, 500);
    },
    treeSelectNode (node, id) {
      this.formData.level = node.level + 1;
    },
    cateSelectNode (node, id) {
      // console.log(node, id, 'node');
    },
    cateInputNode (value, id) {
      if(value == undefined) {
        this.formData.virtualCateId = null
      } else {
        this.formData.virtualCateId = value
      }
    },
    save () {
      this.$refs.formRef.validate((errMap) => {
        // if (!errMap || Object.keys(errMap).length == 0) {
        if (!errMap) {
          let form = JSON.parse(JSON.stringify(this.formData));
          if (form._X_ROW_CHILD) {
            delete form._X_ROW_CHILD;
          }
          form.isPlatform = form.isPlatform ? form.isPlatform.join(',') : ''
          if (this.modalTitle == "新增数据") {
            this.$http.virtualDataAdd(form).then((res) => {
              this.$msg("操作成功", "success");
              this.close()
              this.getData();
            });
          } else {
            this.$http.virtualDataEdit(form).then((res) => {
              this.$msg("操作成功", "success");
              this.close()
              this.getData();
            });
          }
          // this.$refs.xTree.setAllTreeExpand(true);
        } else {
          return false;
        }
      });
    },
    isExecutableChange ({ data }) {
      if (data.isExecutable != 1) {
        data.isPlatform = []
      }
    },
    isPlatformChange ({ data }) {
      if (data.isPlatform && data.isPlatform.length == 1) {
        if (data.isPlatform[0] == '1') {
          data.prefixId = null
          data.linkSrc = ""
        } else {
          data.webUrlId = null
          data.webParams = ""
          data.webSrc = ""
        }
      } else if (data.isPlatform && data.isPlatform.length == 0 || !data.isPlatform) {
        data.webUrlId = null
        data.prefixId = null
        data.linkSrc = ""
        data.webParams = ""
        data.webSrc = ""
      }
    },
    close () {
      this.$refs.formRef.clearValidate();
      this.modalVisible = false;
    },
    handleAdd () {
      this.getTreeselect();
      this.modalTitle = "新增数据";
      this.formData = {
        pId: 0,
        virtualId: null, // 确保这个字段有值
        virtualParentId: 0,
        virtualCateId: null,
        virtualName: "",
        assetName: "",
        operName: "",
        sortBy: 1,
        description: "",
        attchAddr: "",
        isExecutable: 1,
        isHostShow: 0,
        webUrlId: null,
        prefixId: null,
        status: '1',
        pic: "",
        webParams: "",
        linkSrc: "",
        videoSrc: "",
        isPlatform: ['1'],
        webSrc: "",
        remark: "",
        level: 1,
      };
      this.modalVisible = true;
    },
    handleChildAdd (row) {
      this.getTreeselect();
      this.modalTitle = "新增数据";
      this.formData = {
        pId: row.id,
        virtualId: null,
        virtualParentId: row.virtualId,
        virtualCateId: null,
        virtualName: "",
        assetName: "",
        operName: "",
        description: "",
        attchAddr: "",
        isExecutable: 1,
        isHostShow: 0,
        webUrlId: null,
        prefixId: null,
        status: '1',
        pic: "",
        webParams: "",
        linkSrc: "",
        videoSrc: "",
        isPlatform: ['1'],
        webSrc: "",
        remark: "",
        sortBy: 1,
        isExecutable: 1,
        level: row.level + 1,
      };
      this.modalVisible = true;
    },
    handleEdit (row) {
      this.getTreeselect();
      this.modalTitle = "编辑数据";
      this.formData = JSON.parse(JSON.stringify(row));
      this.formData.isPlatform = this.formData.isPlatform ? this.formData.isPlatform.split(',') : []
      if (this.formData.children) {
        this.formData.children = undefined;
      }
      this.modalVisible = true;
    },
    handleChildDel (id) {
      this.$confirm("请确认是否此数据的所有子节点数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.virtualDataChildDel({ id }).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },
    handleDel (row) {
      this.$confirm("请确认是否删除此数据及其所有子节点数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.virtualDataDel(row.id).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },
    handleProgramLoad (row) {
      if (!row.prefixName) {
        return this.$msg("请先填写程序路径", "warning");
      }
      // 更正
      let url = row.prefixName;
      if (row.virtualParentId === 0) {
        url += row.virtualId + "|" + 3 + "|1|" + this.$store.getters["user/accessToken"];
      } else {
        url += row.virtualParentId + "|" + 1 + "|" + row.virtualId + "|" + this.$store.getters["user/accessToken"];
      }
      // 在原有 URL 中添加新的 API 地址并进行 Base64 编码 // 需要加入的额外网址
      const encodedAdditionalUrl = this.$Encrypts.encryptString(window.client_ip, '8N*vq#gPgp');  // 使用 Base64 编码
      url += "|" + encodedAdditionalUrl; // 用“|”分割// 将最终构造的 URL 作为自定义协议的内容
      protocolCheck(
        url,
        () => {
          this.$msg("此协议" + row.prefixName + "未注册", "error");
        },
        () => { },
        () => {
          this.$msg(
            "此浏览器不支持此功能，请更换谷歌，火狐等浏览器",
            "warning"
          );
        }
      );
    },
    // /** 查询下拉树结构 */
    getTreeselect () {
      this.$http.virtualTreeOption().then((res) => {
        this.menuOptions = [];
        let menu = { id: 0, pId: 0, level: 0, virtualName: "主类目", children: [] };
        menu.children = res.data;
        this.menuOptions.push(menu);
      });
    },
    saveBatch () {
      this.$refs.formRef2.validate((errMap) => {
        if (!errMap) {
          let form = JSON.parse(JSON.stringify(this.formDataBatch));
          if (form._X_ROW_CHILD) {
            delete form._X_ROW_CHILD;
          }
          if (form.children) {
            delete form.children;
          }
          form.isPlatform = form.isPlatform ? form.isPlatform.join(',') : ''
          this.$http.virtualDataEditBatch(form).then((res) => {
            this.$msg("操作成功", "success");
            this.closeBatch()
            this.getData();
          });
        } else {
          return false;
        }
      });
    },
    closeBatch () {
      this.$refs.formRef2.clearValidate();
      this.modalBatchVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
/* 修复模态框按钮超出问题 */
::v-deep .vxe-modal--wrapper {
  .vxe-modal--content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .vxe-modal--header {
    flex-shrink: 0;
  }
  
  .vxe-modal--body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    min-height: 0;
  }
  
  .vxe-modal--footer {
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}

/* 确保表单内容不会超出 */
::v-deep .form-k {
  .vxe-form--wrapper {
    max-height: none;
  }
  
  .vxe-form--item {
    margin-bottom: 15px;
  }
}

/* 修复上传组件样式 */
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 100%;
  max-height: 200px; /* 限制视频高度 */
  display: block;
}

/* 视频容器样式优化 */
.avaterBox {
  position: relative;
  max-width: 300px;
  
  video {
    max-height: 200px;
    width: 100%;
    object-fit: contain;
  }
}

.avaterBox:hover .avaterMask {
  background-color: #f56c6c;
}

.avaterBox:hover .avaterMask .success {
  display: none;
}

.avaterBox:hover .avaterMask .delete {
  display: block;
}

.avaterMask {
  width: 80px;
  height: 80px;
  line-height: 40px;
  border-radius: 8px;
  transform: rotate(45deg);
  position: absolute;
  right: -40px;
  top: -40px;
  font-size: 20px;
  background-color: #67c23a;
  transition: all 0.4s;

  span {
    position: absolute;
    top: 55px;
    left: 30px;
    color: #fff;

    .delete {
      display: none;
    }

    .success {
      display: block;
      transform: rotate(-45deg);
    }
  }
}

/* 模态框尺寸调整 */
::v-deep .vxe-modal.is--resize {
  min-height: 400px;
  max-height: 90vh;
}

/* 批量修改模态框样式 */
::v-deep .vxe-modal[aria-describedby*="xModal2"] {
  .vxe-modal--body {
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}
</style>






