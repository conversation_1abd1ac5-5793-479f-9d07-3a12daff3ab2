const { PathPrefix, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(PathPrefix.tableAttributes)

class PathPrefixService {
  static async create (req, res) {
    let data = await PathPrefix.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    let data = await PathPrefix.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await PathPrefix.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async list (req, res) {
    let { page, pageSize, prefixName, attchAddr } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await PathPrefix.findAndCountAll({
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        // order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      return res.sendSuccess({ code: -1, msg:'分页参数缺失' })
    }
  }
}

module.exports = PathPrefixService
