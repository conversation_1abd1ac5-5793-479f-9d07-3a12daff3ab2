<template>
  <el-tooltip ref="tooltip" :disabled='disabled' :open-delay='500' :content="content" :effect="effect" :placement="placement">
    <div ref="contentRef" class="contentBox" :text="content" @mouseenter="handleMouseEnter">{{popText}}</div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'TableCellTooltip',
  props: {
    placement: {
      type: String,
      default: 'top'
    },
    rowNum: {
      type: Number,
      default: 3
    },
    // popperClass: {
    //   type: String,
    //   default: ''
    // },
    effect: {
      type: String,
      default: 'dark'
    },
    content: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      popText: this.content,
      disabled: false,
      timer: null,
    }
  },
  watch:{
    content(val) {
      // console.log(val,'val');
      this.popText = val
      this.initData(this.$refs.contentRef)
    }
  },
  mounted () {
    this.initData(this.$refs.contentRef)
    // window.onresize = () => {
    //   this.testFun()  
    // }
  },
  methods: {
    handleMouseEnter (e) {
      let width = e.target.offsetWidth
      this.$refs.tooltip.$refs.popper.style.maxWidth = width + 'px'
    },
    initData (ele) {
      // console.log(this.$refs,'this.$refs');
      // let ele = this.$refs.contentRef
      let lineHeight = document.defaultView.getComputedStyle(ele).lineHeight
      let top = this.rowNum * parseInt(lineHeight)
      let words = this.content;
      let i = 0
      if (ele.offsetHeight > top) {
        this.disabled = false
        let tempStr = ""
        ele.innerText = tempStr
        while (ele.offsetHeight <= top) {
          if (i > 500) {
            return
          }
          tempStr = words.substring(0, i + 1)
          i++;
          ele.innerText = tempStr
        }
        // ele.innerText = tempStr.substring(0, tempStr.length - 3) + '...';
        this.popText = tempStr.substring(0, tempStr.length - 3) + '...';
        // this.popText = ele.innerText
      } else {
        this.disabled = true
      }
    },
    testFun () {
      if (this.timer != null) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        let boxlist = document.querySelectorAll('.contentBox')
        boxlist.forEach((value) => {
          let ele = value
          let lineHeight = document.defaultView.getComputedStyle(ele).lineHeight
          let top = this.rowNum * parseInt(lineHeight)
          let words = ele.getAttribute('text');
          let i = 0
          if (ele.offsetHeight > top) {
            this.disabled = false
            let tempStr = ""
            ele.innerText = tempStr
            while (ele.offsetHeight <= top) {
              if (i > 500) {
                return
              }
              tempStr = words.substring(0, i + 1)
              i++;
              ele.innerText = tempStr
            }
            ele.innerText = tempStr.substring(0, tempStr.length - 3) + '...';
          } else {
            this.disabled = true
          }
        })
      }, 500);
    },
  }
}
</script>

<style lang="scss" scoped>
.contentBox {
  overflow: hidden;
}
</style>

