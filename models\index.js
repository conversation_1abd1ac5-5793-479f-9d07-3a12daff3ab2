'use strict'

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const Op = Sequelize.Op
const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const config = require(__dirname + '/../config/config.js')[env];
const { logFun } = require('../utils/logColor');
const db = {};

let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], config);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, {
    ...config,
    //日志配置
    logging: logFun,
    operatorsAliases: {
      $eq: Op.eq,
      $ne: Op.ne,
      $gte: Op.gte,
      $gt: Op.gt,
      $lte: Op.lte,
      $lt: Op.lt,
      $not: Op.not,
      $in: Op.in,
      $notIn: Op.notIn,
      $is: Op.is,
      $like: Op.like,
      $notLike: Op.notLike,
      $iLike: Op.iLike,
      $notILike: Op.notILike,
      $regexp: Op.regexp,
      $notRegexp: Op.notRegexp,
      $iRegexp: Op.iRegexp,
      $notIRegexp: Op.notIRegexp,
      $between: Op.between,
      $notBetween: Op.notBetween,
      $overlap: Op.overlap,
      $contains: Op.contains,
      $contained: Op.contained,
      $adjacent: Op.adjacent,
      $strictLeft: Op.strictLeft,
      $strictRight: Op.strictRight,
      $noExtendRight: Op.noExtendRight,
      $noExtendLeft: Op.noExtendLeft,
      $and: Op.and,
      $or: Op.or,
      $any: Op.any,
      $all: Op.all,
      $values: Op.values,
      $col: Op.col
    }
  });
}

fs
  .readdirSync(__dirname)
  .filter(file => {
    return (file.indexOf('.') !== 0) && (file !== basename) && (file.slice(-3) === '.js');
  })
  .forEach(file => {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

//通用like查询条件生成sequelize的where参数
/* 
@params req:请求体
        res:响应体
        modelFields：模型的attr数组
        customConditions:自定义添加的条件，czk与2022.1.19添加。
                         参数类型为Object，必须是key和value，key表示表字段名，value为对象条件，示例{key:'id',value:{$eq:1}}
*/
db.$LikeWhere = function (req, res, modelFields, customConditions) {
  if (req.method != 'GET' && req.method != 'POST') return res.send({ code: -1, msg: '请求方式不符合，请使用get或post请求' })
  let whereParams = {} //返回的where参数对象
  let params = req.method == 'GET' ? req.query : req.body //请求参数对象
  // console.log(,params,'params');
  let searchParams = Object.keys(params) //请求参数的键数组
  if (!searchParams || (searchParams.length && searchParams.length == 0)) return {}
  searchParams.forEach(item => {
    if (modelFields.includes(item) && params[item]) {
      if (item.toLocaleLowerCase().indexOf('id') > -1 || item.toLocaleLowerCase().indexOf('num') > -1) {
        whereParams[item] = {
          $eq: params[item]
        }
      } else {
        whereParams[item] = {
          $like: `%${params[item]}%`
        }
      }
    }
  })
  //新增自定义想添加的条件
  if (customConditions && customConditions.key && customConditions.value) {
    whereParams[customConditions.key] = customConditions.value
  }
  return whereParams
}

//通用排序条件生成sequelize的order参数
/* 
@params req:请求体
        res:响应体
        modelFields：模型的attr数组
*/
db.$Order = function (req, res, modelFields) {
  if (req.method != 'GET' && req.method != 'POST') return res.send({ code: -1, msg: '请求方式不符合，请使用get或post请求' })
  let params = req.method == 'GET' ? req.query : req.body //请求参数对象
  let { sortName, sortOrder } = params //请求参数的键
  if (!sortName || !sortOrder) return null
  if (!modelFields.includes(sortName)) return res.send({ code: -1, msg: '排序参数不存在与此表格' })
  return [[sequelize.literal(`convert(${sortName} using gbk)`), sortOrder]]
}

db.$utils = {

}

module.exports = db;
