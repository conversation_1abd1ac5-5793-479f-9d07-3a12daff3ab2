<!-- <AUTHOR> -->
<template>
  <div class="icon-body">
    <el-input v-model="name" style="position: relative;" clearable placeholder="请输入图标名称" @clear="filterIcons" @change.native="filterIcons">
      <i slot="suffix" class="el-icon-search el-input__icon" />
    </el-input>
    <div class="icon-list">
      <div class="icon-item" v-for="(item, index) in iconList" :key="index" @click="selectedIcon('icon-'+item.font_class)">
        <i :class="'iconfont icon-'+item.font_class"></i>
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import icons from './icon'
export default {
  name: 'IconSelect',
  data() {
    return {
      name: '',
      iconList: icons
    }
  },
  created() {
  },
  methods: {
    filterIcons() {
      this.iconList = icons
      if (this.name) {
        this.iconList = this.iconList.filter(item => item.name.includes(this.name) || item.font_class.includes(this.name))
      }
    },
    selectedIcon(name) {
      this.$emit('selected', name)
      document.body.click()
    },
    reset() {
      this.name = ''
      this.iconList = icons
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .icon-body {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    .icon-list {
      margin-top: 10px;
      height: 200px;
      margin-right: -15px;
      overflow: hidden;
      overflow-y: auto;
      .icon-item {
        width: 60px;
        height: 60px;
        margin-right: 7px;
        margin-bottom: 7px;
        border-radius: 4px;
        text-align: center;
        float: left;
        border: 1px solid #ccc;
        overflow: hidden;
        transition: all .5s;
        &:nth-child(5n) {
          margin-right: 0px;
        }
        &:hover {
          color: #409eff;
          border-color: #409eff;
        }
        i {
          display: inline-block;
          margin-top: 3px;
          width: 100%;
          font-size: 26px;
        }
      }
      span {
        display: inline-block;
        vertical-align: -0.15em;
        fill: currentColor;
        overflow: hidden;
      }
    }
  }
</style>
