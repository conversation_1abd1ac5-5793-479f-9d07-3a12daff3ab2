var formatDate = function (serial, format) {
  //判断是否为数字
  var isNum = checkTimeNumber(serial)
  var utc_days
  var utc_value
  var date_info
  var fractional_day
  var total_seconds
  var seconds
  var hours
  var minutes
  var daytime
  var returnDate
  // return 'NaN'
  //如果表格读取为数字型
  if (isNum) {
    utc_days = Math.floor(serial - 25569);
    utc_value = utc_days * 86400;
    date_info = new Date(utc_value * 1000);
    fractional_day = serial - Math.floor(serial) + 0.0000001;
    total_seconds = Math.floor(86400 * fractional_day);
    seconds = total_seconds % 60;
    total_seconds -= seconds;
    hours = Math.floor(total_seconds / (60 * 60));
    minutes = Math.floor(total_seconds / 60) % 60;

    daytime = new Date(date_info.getFullYear(), date_info.getMonth(), date_info.getDate(), hours, minutes, seconds);
    returnDate = date_info.getFullYear();
  } else {
    //不为数字为字符串且转换后不为NaN
    if (Date.parse(serial) != 'NaN') {
      daytime = new Date(Date.parse(serial))
      date_info = new Date(Date.parse(serial));
      returnDate = new Date(Date.parse(serial)).getFullYear();
    } else {
      return 'NaN'
    }
  }

  returnDate += format + (date_info.getMonth() < 9 ? '0' + (date_info.getMonth() + 1) : (date_info.getMonth() + 1));
  returnDate += format + (date_info.getDate() < 10 ? '0' + date_info.getDate() : date_info.getDate());
  returnDate += " " + (daytime.getHours() < 10 ? '0' + daytime.getHours() : daytime.getHours());
  returnDate += ":" + (daytime.getMinutes() < 10 ? '0' + daytime.getMinutes() : daytime.getMinutes());
  return returnDate;
}

//excel导入时发现新bug，关于时间填错了
var checkTimeNumber = function (val) {
  if (val !== '' && typeof val == 'string') {
    return false;
  } else {
    return true;
  }
}

module.exports = {
  formatDate
}