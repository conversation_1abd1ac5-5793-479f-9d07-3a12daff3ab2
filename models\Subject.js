'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Subject extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // this.belongsTo(models.Question, {foreignKey:'questionId',constraints:false })
      this.belongsTo(models.Grade,{foreignKey:'gradeId',constraints:false})
      this.hasMany(models.Question,{foreignKey:'questionId',constraints:false})
    }
  }
  Subject.init(
    {         
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },
      subjectName:{
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "学科名称"
      },
      gradeId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "年级id"
      },     
      createBy: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },      
    },
    {
      sequelize,
      // timestamps: false,
      modelName: 'Subject',
      tableName: 'zk_basic_subject',
      comment:'学科表'
    }
  )
  return Subject
}
