var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const { transactionTryAsyncErrors } = require("../../utils/transaction");
const Service = require("../../services/VirtualCateService");

router.get('/list', tryAsyncErrors(Service.list))
router.get("/treeList", tryAsyncErrors(Service.treeList));
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', transactionTryAsyncErrors(Service.update))
router.delete('/delete/:id', transactionTryAsyncErrors(Service.delete))
router.post('/relevance', transactionTryAsyncErrors(Service.relevance))
router.post('/notRelevance', tryAsyncErrors(Service.notRelevance))

module.exports = router;
