const dayjs = require('dayjs');
module.exports = {
  //Sequelize控制台输出修改
  logFun: function (data, info) {
    let arr = data.split(':')
    let params = (data.indexOf('INSERT INTO') != -1 && data.indexOf('sys_log') == -1) ? info.bind : ''
    console.log('\x1b[46;30m\x1b[4m', arr[0], dayjs().format('YYYY/MM/DD HH:mm:ss'), '\x1b[0m\x1b[40;36m', arr[1], '\x1b[0m');
    if (params) {
      console.log('\x1B[36m%s\x1B[0m', '[' + dayjs().format('YYYY/MM/DD HH:mm:ss') + '] ' + '[PARAMS:]-  ' + params, '\n')
    }
  }
}