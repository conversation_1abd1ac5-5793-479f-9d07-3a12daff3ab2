'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class PathWeb extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsTo(models.VirtualProcess, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcessDevice, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcessDeviceOper, { foreignKey:'id',targetKey: 'prefixId' })
      // this.belongsTo(models.VirtualProcess)
      // this.belongsTo(models.VirtualProcessDevice)
      // this.belongsTo(models.VirtualProcessDeviceOper)
    }
  }
  PathWeb.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      webName: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: '',
        comment: "web地址名称"
      },
      webUrl: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "",
        comment: "web地址"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '状态1是启用0是禁用',
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      }, 
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
    },
    {
      sequelize,
      modelName: 'PathWeb',
      tableName: 'zk_path_web',
      comment: 'web地址表'
    }
  )
  return PathWeb
}
