var express = require("express");
var router = express.Router();
const fs = require("fs");
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/DdService");

router.get('/version', tryAsyncErrors(Service.version))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))
router.get('/download', function (req, res) {
  let fileName = '__UNI__DDJN.apk'
  let tarpath = './public/appVersion/__UNI__6318D07__20220509175122.apk'

  if (fs.existsSync(tarpath)) {
    var stream = fs.createReadStream(tarpath)
    var userAgent = (req.headers['user-agent'] || '').toLowerCase()
    let size = fs.statSync(tarpath).size
    res.setHeader("Content-Length", size + "")

    //跨域中需要加上下面这段代码，前端才能获取到自定义头字段
    // res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition')
    if (userAgent.indexOf('msie') >= 0 || userAgent.indexOf('chrome') >= 0) {
      /*谷歌和IE浏览器下载*/
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          'attachment; filename=' + encodeURIComponent(fileName),
      })
    } else if (userAgent.indexOf('firefox') >= 0) {
      /*火狐浏览器下载*/
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          "attachment; filename*=\"utf8''" + encodeURIComponent(fileName) + '"',
      })
    } else {
      /* safari等其他非主流浏览器只能自求多福了 */
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          'attachment; filename=' + new Buffer(fileName).toString('binary'),
      })
    }
    stream.pipe(res)
  } else {
    res.json({ code: 403, success: false, error: 'file not exit' })
  }
})

module.exports = router;