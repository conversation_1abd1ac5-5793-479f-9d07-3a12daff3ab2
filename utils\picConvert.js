// const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputDir = './input';
const outputDir = './output';
const targetSize = 1024; // 压缩后的目标大小，单位为字节
const overlayDir = './public/logo.png'
const valid = ['jpeg', 'png', 'webp', 'tiff']
const quality = 80 //图片质量，整数1-100(可选，默认80)
const prefix = '' //转化后文件后缀，为空则覆盖原有图片，不为空则保留原图，生成带前缀的图片
// 获取指定目录下的所有文件路径
function getFilePaths (dirPath) {
  const fileNames = fs.readdirSync(dirPath);
  return fileNames.map((fileName) => path.join(dirPath, fileName));
}

// 压缩单个图片文件
function compressImage (inputPath, outputPath) {
  //composite方法添加水印
  // return sharp(inputPath).composite([{ input: overlayDir, top: 20, left: 20 }]).toFile(outputPath)
  // let funStr = path.extname(inputPath).substring(1)
  // const imageBuffer = fs.readFileSync(inputPath);
  // if (funStr != '.' && valid.includes(funStr)) {
  //   return sharp(imageBuffer)[funStr]({ quality }).toFile(outputPath)
  // } else {
  //   return sharp(imageBuffer).toFile(outputPath)
  // }
}

// 压缩指定目录下的所有图片文件
function compressImagesInDir (inputDir, outputDir) {  
  const inputPaths = getFilePaths(inputDir);
  const promises = inputPaths.map((inputPath) => {
    const fileName = path.basename(inputPath);
    const outputPath = path.join(outputDir, prefix + fileName);
    return compressImage(inputPath, outputPath);
  });
  // let promises = []
  // for (let i = 0; i < inputPaths.length; i++) {
  //   let inputPath = inputPaths[i]
  //   let fileName = path.basename(inputPath);
  //   let outputPath = path.join(outputDir, 'lp_' + fileName);
  //   if (i == (inputPaths.length - 1)) {
  //     promises.push(compressImage(inputPath + '123', outputPath))
  //   } else {
  //     promises.push(compressImage(inputPath, outputPath))
  //   }
  // }
  return Promise.allSettled(promises);
}

module.exports = { compressImage, compressImagesInDir }
