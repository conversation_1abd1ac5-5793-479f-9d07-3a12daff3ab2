<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="web名称" prop="webName">
        <el-input placeholder="请输入名称" v-model="formSearch.webName" clearable @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" keep-source :edit-config="{trigger: 'click' ,mode: 'row',showStatus:true}" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['web/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <el-button type="primary" v-permissions="['web/edit']" plain icon="el-icon-success" @click="bulkSave" size="mini">保存</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" icon="el-icon-link" @click="openWebUrl(row.webUrl)">web预览</el-button>
        <el-button type="text" v-permissions="['web/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['web/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.classRules" title-width="120" title-align="right">
          <template #uploadExe="{ data }">
            <el-upload class="upload-demo" ref="uploadRef" :headers="{ token: $store.getters['user/accessToken'] }" :action="$BASEURL + '/file/uploadWebRar'" accept=".zip,.rar" :limit="1" :show-file-list="true" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :on-error="handleAvatarError" :before-remove="beforeRemove">
              <el-button size="small" type="primary">上传zip,rar文件</el-button>
              <!-- <div slot="tip" class="el-upload__tip">只能上传doc/docx文件，且不超过500kb</div> -->

              <!-- <div class="avaterBox">
                <img v-if="data.pic" :src="$SOURCEURL + data.pic" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div v-if="data.pic" class="avaterMask" @click.stop="delUploadImg">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div> -->
            </el-upload>
          </template>

          <template #linkUrl="{ data }">
            <el-button type="text" icon="el-icon-link" @click="openWebUrl(data.webUrl)">访问web地址</el-button>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
let FullScreenLoading = null
export default {
  name: "webPath",
  data () {
    return {
      srcList: [],
      modalTitle: '新增信息',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'webName', title: 'web名称', editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { sortable: true, field: "webUrl", title: "web地址", editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'remark', title: '备注', editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        webName: '',
        remark: '',
      },
      formItem: [
        { field: 'webName', title: 'web名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入web' } } },
        {
          field: "exe",
          title: "文件上传", className: 'formTreeTitle',
          span: 24,
          slots: { default: "uploadExe" },
        },
        { field: "webUrl", title: "web地址", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入web地址", }, }, },
        { field: "preview", title: " ", span: 24, slots: { default: "linkUrl" } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      formSearch: {
        webName: undefined,
        page: 1,
        pageSize: 10
      },
    };
  },
  created () {
    this.getData();
  },
  methods: {
    getData (params) {
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.pathWebList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    bulkSave () {
      let record = this.$refs.xGrid.getUpdateRecords()
      this.$http.pathWebEdit(record).then(res => {
        this.$msg('操作成功', 'success');
        this.getData();
      });
    },
    isValidURL (url) {
      if (url.indexOf("http") > -1) {
        return true
      } else {
        return false
      }
    },
    openWebUrl (url) {
      if (!url) {
        return this.$msg("请先填写web地址或上传相关文件", 'warning')
      }
      if (!this.isValidURL(url)) {
        return this.$msg("此url地址不合法", 'warning')
      }
      return window.open(url, '_blank')
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData
          if (!form.id) {
            this.$http.pathWebAdd(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.pathWebEdit(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.$refs.uploadRef.clearFiles()
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleAdd () {
      this.modalTitle = '新增信息';
      this.modalVisible = true;
      this.formData = {
        webName: '',
        webUrl: '',
        remark: '',
      };
    },
    handleEdit (row) {
      this.modalTitle = '编辑信息';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.pathWebDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
    handleAvatarSuccess (res, file) {
      if(res.code != 200) return this.$message.error(res.msg || "未知错误");
      this.formData.webUrl = res.url;
      this.$message.success("上传成功！");
      FullScreenLoading.close();
    },
    beforeRemove (file, fileList) {
      this.formData.webUrl = null
    },
    // 上传失败时也隐藏 loading
    handleAvatarError (err, file, fileList) {
      FullScreenLoading.close();
      this.$message.error(err.message)
    },
    beforeAvatarUpload (file) {
      FullScreenLoading = this.$loading({
        lock: true,
        text: '上传中，请稍等。。。',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      // const isLt2M = file.size / 1024 / 1024 < 5;
      // if (!isLt2M) {
      //   this.$message.error("上传文件大小不能超过5MB!");
      // }
      // return isLt2M;
    },
    delUploadImg (e) {
      this.formData.webUrl = "";
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
::v-deep .formTreeTitle {
  .vxe-form--item-inner {
    align-items: start;

    .vxe-form--item-title {
      vertical-align: top;
    }
  }
}
</style>
