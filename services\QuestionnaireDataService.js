const { QuestionnaireData,Questionnaire, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(QuestionnaireData.tableAttributes)

class QuestionnaireDataService {
  static async create (req, res) {    
    if (!req.body.formKey) return res.sendSuccess({ code: -1, msg: '问卷id不存在' })
    if (!req.body.dataJson) return res.sendSuccess({ code: -1, msg: '请填写问卷' })
    let data = await QuestionnaireData.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    if (!req.body.id) {
      return res.sendSuccess({ code: -1, msg: '数据id缺失' })
    }
    let data = await QuestionnaireData.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await QuestionnaireData.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async getOne (req, res) {
    const { questionnaireId } = req.params
    let data = await QuestionnaireData.findByPk(questionnaireId, {
      include: [
        {
          model: QuestionnaireDataAnswer,
          as: 'questionAnswers',
        },
      ]
    })
    res.sendSuccess({ code: 200, data, msg: '获取成功' })
  }

  static async list (req, res) {
    if(!req.query.formKey) return res.sendSuccess({ code: -1, msg: 'formKey参数缺失' })
    if (!req.query.page && !req.query.pageSize) return res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    let where = $LikeWhere(req, res, fields)
    let column = await Questionnaire.findOne({
      where:{
        formKey:req.query.formKey
      },
      raw: true
    })
    let { rows, count } = await QuestionnaireData.findAndCountAll({
      where,
      offset: (req.query.page - 1) * req.query.pageSize,
      limit: req.query.pageSize,
      order: [
        ['createTime', 'DESC'],
      ],
      raw: true
    })
    res.sendSuccess({ code: 200, data: rows,column, total: count })
  }

  static async byId (req, res) {
    if (!req.query.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await QuestionnaireData.findByPk(req.query.id, { raw: true })
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = QuestionnaireDataService
