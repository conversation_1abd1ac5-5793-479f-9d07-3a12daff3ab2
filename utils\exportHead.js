const { sequelize: $seq } = require('../models')

module.exports = {
  workmateCupHead: ['项目名称', '所属地区', '所属赛区', '报名时间', '创始人身份', '注册年限', '营业收入', '所属工会', '上级工会', '项目简介', '所属行业', '来源', '组别', '大赛获奖情况', '单位', '联系人', '联系方式', '备注'],
  workmateCupField: ['projectName', [$seq.col('Region.regionName'), 'regionName'], [$seq.col('Division.name'), 'divisionName'], 'applyTime', 'identityName', 'foundTime', 'operationRevenue', 'unioner', 'unionerParent', 'projectDescription', 'industry', 'source', 'groupType', 'competitionAwards', 'unit', 'contactPerson', 'contactInformation', 'remark'],
  questionnaireHead: ["公司名称", "所在地区", "联系人", "联系电话", "职务", "所属行业", "创办时间", "所有制性质", "营业收入", "业务覆盖面", "所属工会", "公司是否成立工会组织", "双创项目个数", "核心创始人身份", "核心创始人学历", "中高级职称人数", "专利技术数量", "资金来源", "直接带动就业人数", "间接带动就业人数", "重大发展或突破", "双创项目发展问题", "金融需求", "人才需求", "管理需求", "科创需求", "合作需求", "推广需求", "其它需求", "需求重要程度排序", "期望解决周期", "期望得到工会的帮助和支持", "答疑收件邮箱", "导师问诊答疑", "备注"],
  questionnaireField: [[$seq.col('EnterPrise.enterPriseName'), "enterPriseName"], "area", "contactPerson", "contactInformation", "duty", [$seq.col('Industry.name'), "industry"], "foundTime", "ownership", "operationRevenue", "businessCoverage", "unioner", "isCreateUnion", "scProNumber", "scProFounder", "founderEducation", "teamPersonNumber", "teamPatentNumber", "capitalSource", "directDriveNumber", "indirectDriveNumber", "quantumJump", "scDevelopIssue", "jrDemand", "rcDemand", "glDemand", "kcDemand", "hzDemand", "tgDemand", "qtDemand", "demandDegreeSort", "solveCycle", "expectHelp", "answerEmail", "specialTutor", "remark"],
  
}