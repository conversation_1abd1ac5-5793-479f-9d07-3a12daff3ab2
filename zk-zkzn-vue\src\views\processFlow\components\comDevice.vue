<template>
  <div>
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <!-- <el-form-item label="关联题型" prop="type">
        <el-select v-model="formSearch.type" placeholder="关联题型" size="small" @change="changeColumn">
          <el-option v-for="dict in typeOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <!-- 选择题筛选框 -->
      <!-- <el-form-item v-if="formSearch.type == 2" label="题干" prop="questionContent">
        <el-input placeholder="请输入题干" v-model="formSearch.questionContent" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item v-if="formSearch.type == 2" label="题目类型" prop="questionType">
        <el-select v-model="formSearch.questionType" placeholder="题目类型" clearable size="small">
          <el-option v-for="dict in questionTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->

      <!-- 议题筛选框 -->
      <el-form-item label="议题名称" prop="issueName">
        <el-input placeholder="请输入议题" v-model="formSearch.issueName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <!-- <el-form-item label="题目类型" prop="questionType">
        <el-select v-model="formSearch.questionType" placeholder="题目类型" clearable size="small">
          <el-option v-for="dict in questionTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" plain icon="el-icon-plus" @click="associatedTopic" size="mini">关联题目</el-button>
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </template>
      <!-- <template v-slot:questionTypeSlot="{ row }">
        <el-tag :type="row.questionType==1?'':'success'">{{row.questionType==1?'单选题':'多选题'}}</el-tag>
      </template> -->
      <template #conExpand="{ row }">
        <div class="expandBox">
          <el-row style="margin-bottom:10px;">
            <el-col>
              <p v-html="row.issueContent"></p>
            </el-col>
          </el-row>
        </div>
      </template>

      <template v-slot:questionLevelSlot="{ row }">
        <el-rate v-model="row.questionLevel" disabled> </el-rate>
      </template>

      <template v-slot:questionTypeSlot="{ row }">
        <el-tag :type="row.questionType==1?'':'success'">{{row.questionType==1?'单选题':'多选题'}}</el-tag>
      </template>

      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" icon="el-icon-edit" @click="disassociateQuestion(row)">解除关联</el-button>
        <!-- <el-button type="text" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
        <el-button type="text" icon="el-icon-refresh-right" @click="handleRecord(row)">重置密码</el-button> -->
      </template>
    </vxe-grid>
    <com-associated-modal ref="comAssociatedRef"></com-associated-modal>

  </div>
</template>

<script>
import comAssociatedModal from './comAssociatedModal.vue'
export default {
  components: {
    comAssociatedModal
  },
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'expand', width: 60, slots: { content: 'conExpand' } },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'issueName', title: '议题名称' },
        { field: 'issueContent', title: '议题内容', type: "html" },
        // { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      loading: false,
      formSearch: {
        type: '1',
        issueName: '',
        questionContent: '',
        questionType: '',
        page: 1,
        pageSize: 10,
        code: null,
        recordId: undefined,
        virtualId: undefined,
        virtualprocessId: undefined,
        deviceId: undefined,
        deviceOperationId: undefined,
      },
      selectNodeTable: false,
      typeOption: [
        { label: '议题', value: '1' },
        { label: '选择题', value: '2' }
      ],
      questionTypeOptions: [
        { label: '单选题', value: '1' },
        { label: '多选题', value: '2' }
      ],
      isExpand: false
    };
  },
  methods: {
    open (val) {
      //2022.3.4 czk新增此字段，关联记录id
      this.formSearch.code = val.code
      this.formSearch.recordId = val.recordId
      this.formSearch.virtualId = val.virtualId
      this.formSearch.virtualprocessId = val.virtualprocessId
      this.formSearch.deviceId = val.deviceId
      this.formSearch.deviceOperationId = val.deviceOperationId
      //重置表格和表单的page为1，不然会导致某些bug产生
      this.tablePage.currentPage = 1
      this.formSearch.page = 1
      this.changeColumn(this.formSearch.type)
      this.getData()
    },
    getData () {
      this.loading = true;
      if (!this.formSearch.recordId || !this.formSearch.deviceId) {
        return this.$msg('参数缺失，请刷新界面后再试', 'warning')
      }
      this.$http.issueAssociation(this.formSearch).then(res => {
        this.tableData = res.data
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    changeColumn (val) {
      if (val == '1') {
        this.tableColumn = [
          { type: 'checkbox', width: 60 },
          { type: 'expand', width: 60, slots: { content: 'conExpand' } },
          { type: 'seq', width: 60, title: '序号' },
          { sortable: true, field: 'issueName', title: '议题名称' },
          { field: 'issueContent', title: '议题内容', type: "html" },
          // { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
          { sortable: true, field: 'createTime', title: '创建时间' },
          { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
        ]
      } else if (val == '2') {
        this.tableColumn = [
          { type: 'checkbox', width: 60 },
          { type: 'expand', width: 60, slots: { content: 'conExpand' } },
          { type: 'seq', width: 60, title: '序号' },
          { sortable: true, field: 'questionContent', title: '题干', type: "html" },
          { field: 'questionType', title: '题目类型', slots: { default: 'questionTypeSlot' } },
          { sortable: true, field: 'questionLevel', title: '题目难度', slots: { default: 'questionLevelSlot' } },
          { sortable: true, field: 'createBy', title: '创建人' },
          { sortable: true, field: 'createTime', title: '创建时间' },
          { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
        ]
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllRowExpand(true)
      } else {
        this.$refs.xGrid.clearRowExpand()
      }
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    associatedTopic () {
      this.$refs.comAssociatedRef.open({
        code: this.formSearch.code,
        recordId: this.formSearch.recordId,
        virtualId: this.formSearch.virtualId,
        virtualprocessId: this.formSearch.virtualprocessId,
        deviceId: this.formSearch.deviceId,
        deviceOperationId: this.formSearch.deviceOperationId
      }, Number(this.formSearch.type));
    },
    disassociateQuestion (row) {
      this.$http.delAssociation(row.processRecordId).then(res => {
        this.$msg(res.msg, 'success');
        this.getData();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.expandBox {
  font-size: 16px;
  // padding-left: 30px;
  .title {
    display: inline-block;
    vertical-align: top;
    margin: 3px 0;
  }
  .qs_contentBox {
    display: inline-block;
    margin-left: 10px;
  }
  .analyBox {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep p {
    margin: 3px 0;
    img {
      vertical-align: middle;
    }
  }
}
</style>
