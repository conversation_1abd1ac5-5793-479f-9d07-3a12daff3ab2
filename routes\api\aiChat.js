var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const AiChatController = require("../../controllers/AiChatController");

// 获取用户剩余聊天次数
router.get('/remaining', tryAsyncErrors(AiChatController.getRemainingChats))
// 获取聊天历史记录
router.get('/history', tryAsyncErrors(AiChatController.getChatHistory))
// 发送聊天消息
router.post('/send', tryAsyncErrors(AiChatController.sendChatMessage))

module.exports = router; 