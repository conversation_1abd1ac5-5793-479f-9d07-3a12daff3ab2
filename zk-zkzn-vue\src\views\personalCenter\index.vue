<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="8" :xs="24">
        <el-card class="box-card">
          <!-- <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div> -->
          <div>
            <div class="text-center">
              <userAvatar :user="user" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <i class="el-icon-user"></i>登录名
                <div class="pull-right">{{ user.userName }}</div>
              </li>
              <li class="list-group-item">
                <i class="el-icon-user"></i>用户名
                <div class="pull-right">{{ user.nickName }}</div>
              </li>
              <li class="list-group-item">
                <i class="el-icon-user"></i>单位
                <div class="pull-right">{{ user.unit }}</div>
              </li>
              <li class="list-group-item">
                <i class="el-icon-user"></i>手机号码
                <div class="pull-right">{{ user.phone }}</div>
              </li>
              <li class="list-group-item">
                <i class="el-icon-user"></i>用户邮箱
                <div class="pull-right">{{ user.email }}</div>
              </li>

              <li class="list-group-item">
                <i class="el-icon-user"></i>所属角色
                <div class="pull-right">{{ user.roleName }}</div>
              </li>
              <li class="list-group-item">
                <i class="el-icon-user"></i>创建日期
                <div class="pull-right">{{ user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="16" :xs="24">
        <el-card>
          <!-- <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div> -->
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";

export default {
  name: "PersonalCenter",
  components: { userAvatar, userInfo, resetPwd },
  data() {
    return {
      user: {},
      activeTab: "userinfo",
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getUser() {
      this.$http.userInfo().then((res) => {
        this.user = res.data;
      });
    },
  },
};
</script>
