const { Subject, Grade, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(Subject.tableAttributes)
class SubjectService {
  static async create (req, res) {
    let data = await Subject.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await Subject.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await Subject.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { page, pageSize, subjectName } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await Subject.findAndCountAll({
        attributes: {
          include: [[$seq.col('Grade.gradeName'), 'gradeName']]
        },
        include: {
          model: Grade,
          attributes: []
        },
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let data = await Subject.findAll({
        attributes: {
          include: [[$seq.col('Grade.gradeName'), 'gradeName']]
        },
        include: {
          model: Grade,
          attributes: []
        },
        where
      })
      res.sendSuccess({ code: 200, data })
    }
  }
}

module.exports = SubjectService
