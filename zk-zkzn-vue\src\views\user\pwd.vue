<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card>
          <!-- <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div> -->
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="user" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd :user="user" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userInfo from "../personalCenter/userInfo";
import resetPwd from "../personalCenter/resetPwd";

export default {
  name: "Pwd",
  components: { userInfo, resetPwd },
  data () {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "userinfo"
    };
  },
  created () {
  },
  methods: {
    getUser () {
      this.$http.userInfo().then(res => {
        this.user = res.data;
        this.roleGroup = res.roleGroup;
        this.postGroup = res.postGroup;
      });
    }
  }
};
</script>
