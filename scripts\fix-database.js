/**
 * 数据库修复脚本
 * 该脚本用于修复数据库外键约束错误
 * 执行方式：node scripts/fix-database.js
 */

const models = require('../models');
const fs = require('fs');
const path = require('path');
const { QueryTypes } = require('sequelize');

async function main() {
  try {
    // 获取数据库连接
    const sequelize = models.sequelize;
    
    // 删除可能存在的表（按依赖关系倒序）
    console.log('删除现有表...');
    try {
      await sequelize.query('DROP TABLE IF EXISTS `ai_chat_message`;', { type: QueryTypes.RAW });
      await sequelize.query('DROP TABLE IF EXISTS `ai_chat_quota`;', { type: QueryTypes.RAW });
      console.log('表删除成功');
    } catch (error) {
      console.error('删除表时出错:', error);
    }
    
    // 创建表，但不带外键约束
    console.log('创建表结构...');
    
    // 创建AI聊天配额表
    const createAiChatQuotaSQL = `
    CREATE TABLE IF NOT EXISTS \`ai_chat_quota\` (
      \`id\` INTEGER auto_increment COMMENT '配额记录ID',
      \`userId\` INTEGER COMMENT '用户ID，可以为null表示未登录用户',
      \`tempUserId\` VARCHAR(100) COMMENT '临时用户ID，用于未登录用户',
      \`courseId\` INTEGER NOT NULL COMMENT '课程ID',
      \`remainingChats\` INTEGER NOT NULL DEFAULT 5 COMMENT '剩余聊天次数，默认值在运行时由配置文件的maxQuestionsPerCourse决定',
      \`totalChats\` INTEGER NOT NULL DEFAULT 5 COMMENT '总聊天次数，默认值在运行时由配置文件的maxQuestionsPerCourse决定',
      \`lastResetDate\` DATE NOT NULL COMMENT '上次重置日期',
      \`dailyLimit\` TINYINT(1) NOT NULL DEFAULT false COMMENT '是否启用每日重置，默认为false(不重置)',
      \`createTime\` DATETIME NOT NULL COMMENT '创建时间',
      \`updateTime\` DATETIME NOT NULL COMMENT '更新时间',
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`uk_user_course\` (\`userId\`, \`courseId\`),
      UNIQUE KEY \`uk_temp_user_course\` (\`tempUserId\`, \`courseId\`)
    ) ENGINE=InnoDB COMMENT='AI聊天剩余次数表';
    `;
    
    // 创建AI聊天消息表
    const createAiChatMessageSQL = `
    CREATE TABLE IF NOT EXISTS \`ai_chat_message\` (
      \`id\` INTEGER auto_increment COMMENT '聊天消息ID',
      \`userId\` INTEGER COMMENT '用户ID，可以为null表示未登录用户',
      \`tempUserId\` VARCHAR(100) COMMENT '临时用户ID，用于未登录用户',
      \`courseId\` INTEGER NOT NULL COMMENT '课程ID',
      \`courseName\` VARCHAR(255) COMMENT '课程名称',
      \`content\` TEXT NOT NULL COMMENT '消息内容',
      \`isUser\` TINYINT(1) NOT NULL DEFAULT true COMMENT '是否用户发送的消息，true为用户，false为AI',
      \`messageTime\` DATETIME NOT NULL COMMENT '消息发送时间',
      \`deleted\` TINYINT(1) NOT NULL DEFAULT false COMMENT '是否已删除',
      \`createTime\` DATETIME NOT NULL COMMENT '创建时间',
      \`updateTime\` DATETIME NOT NULL COMMENT '更新时间',
      PRIMARY KEY (\`id\`)
    ) ENGINE=InnoDB COMMENT='AI聊天消息记录表';
    `;
    
    try {
      await sequelize.query(createAiChatQuotaSQL, { type: QueryTypes.RAW });
      await sequelize.query(createAiChatMessageSQL, { type: QueryTypes.RAW });
      console.log('表创建成功！');
    } catch (error) {
      console.error('创建表时出错:', error);
    }
    
    console.log('数据库修复完成！');
  } catch (error) {
    console.error('执行脚本时出错:', error);
  } finally {
    // 关闭数据库连接
    await models.sequelize.close();
  }
}

main(); 