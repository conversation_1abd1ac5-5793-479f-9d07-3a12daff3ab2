/**
 * AI服务工具类
 * 用于与AI提供商交互，发送请求并获取回复
 */

const axios = require('axios');
const aiConfig = require('../config/aiConfig');

class AiService {
  constructor() {
    this.config = aiConfig;
    // 根据配置决定使用哪个AI提供商
    this.provider = aiConfig.defaultProvider;
  }

  /**
   * 过滤AI回复，移除思考过程
   * @param {String} response AI回复内容
   * @returns {String} 过滤后的回复
   */
  filterAIResponse(response) {
    if (!response) return '';
    
    // 移除<think>...</think>标签内的内容
    let filtered = response.replace(/<think>[\s\S]*?<\/think>/g, '');
    
    // 移除可能的其他思考格式，如markdown代码块中的思考过程
    filtered = filtered.replace(/```思考[\s\S]*?```/g, '');
    filtered = filtered.replace(/```think[\s\S]*?```/g, '');
    filtered = filtered.replace(/```thinking[\s\S]*?```/g, '');
    
    // 移除以"思考："或"思考过程："开头的段落
    filtered = filtered.replace(/^(思考[：:]|思考过程[：:]|[Tt]hinking[：:]).*?(?=\n\n|\n$|$)/gms, '');
    
    // 移除可能嵌入在括号内的思考过程
    filtered = filtered.replace(/\(思考:.*?\)/g, '');
    filtered = filtered.replace(/（思考:.*?）/g, '');
    
    // 清理可能产生的多余空行
    filtered = filtered.replace(/\n{3,}/g, '\n\n');
    
    return filtered.trim();
  }

  /**
   * 获取AI回复
   * @param {String} message 用户消息
   * @param {String} courseId 课程ID
   * @returns {Promise<String>} AI回复
   */
  async getAiResponse(message, courseId) {
    // 如果禁用真实AI，则使用模拟回复
    if (!this.config.enableRealAI) {
      return this.simulateAIResponse(message, courseId);
    }

    // 根据提供商选择不同的处理方法
    let response;
    switch (this.provider) {
      case 'openai':
        response = await this.getOpenAIResponse(message, courseId);
        break;
      case 'baidu':
        response = await this.getBaiduResponse(message, courseId);
        break;
      default:
        response = await this.simulateAIResponse(message, courseId);
        break;
    }
    
    // 过滤回复内容，移除思考过程
    return this.filterAIResponse(response);
  }

  /**
   * 获取OpenAI回复
   * @param {String} message 用户消息
   * @param {String} courseInfo 课程ID或名称
   * @returns {Promise<String>} AI回复
   */
  async getOpenAIResponse(message, courseInfo) {
    try {
      const openaiConfig = this.config.openai;
      
      console.log('发送OpenAI请求，API地址:', openaiConfig.baseURL);
      const requestData = {
        model: openaiConfig.model,
        messages: [
          {
            role: 'system',
            content: `你是一个课程助手，正在帮助用户学习"${courseInfo}"课程。请提供专业、简洁的回答，使用中文回复。请直接回答问题，不要输出你的思考过程，不要使用<think>标签或任何形式的"思考："前缀。`
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: openaiConfig.maxTokens,
        temperature: openaiConfig.temperature,
      };
      
      console.log('OpenAI请求数据:', JSON.stringify(requestData));
      
      const response = await axios.post(
        `${openaiConfig.baseURL}/chat/completions`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${openaiConfig.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: openaiConfig.timeout,
        }
      );
      
      console.log('OpenAI响应状态码:', response.status);
      console.log('OpenAI响应头:', JSON.stringify(response.headers));
      console.log('OpenAI响应数据:', JSON.stringify(response.data));
      
      if (response.data && response.data.choices && response.data.choices.length > 0) {
        return response.data.choices[0].message.content.trim();
      } else {
        console.error('OpenAI响应格式异常:', response.data);
        return this.simulateAIResponse(message, courseInfo);
      }
    } catch (error) {
      console.error('OpenAI API调用失败:', error.message);
      console.error('调用失败的完整错误信息:', error);
      
      if (error.response) {
        console.error('错误响应数据:', error.response.data);
        console.error('错误状态码:', error.response.status);
        console.error('错误响应头:', error.response.headers);
      } else if (error.request) {
        console.error('未收到响应的请求:', error.request);
      }
      
      // 如果API调用失败，回退到模拟响应
      return this.simulateAIResponse(message, courseInfo);
    }
  }

  /**
   * 获取百度文心回复
   * @param {String} message 用户消息
   * @param {String} courseInfo 课程ID或名称
   * @returns {Promise<String>} AI回复
   */
  async getBaiduResponse(message, courseInfo) {
    // 这里实现调用百度文心API的代码
    // 由于百度文心接口调用较复杂，这里仅提供一个示例框架
    try {
      // 获取access token的代码...
      // 调用百度文心API的代码...
      
      // 示例返回，实际项目中需要替换为真实实现
      return `这是百度文心对于课程"${courseInfo}"问题的回复: ${message}`;
    } catch (error) {
      console.error('百度文心API调用失败:', error);
      // 如果API调用失败，回退到模拟响应
      return this.simulateAIResponse(message, courseInfo);
    }
  }

  /**
   * 模拟AI回复（当真实AI服务不可用时使用）
   * @param {String} message 用户消息
   * @param {String} courseInfo 课程ID或名称
   * @returns {Promise<String>} 模拟的AI回复
   */
  async simulateAIResponse(message, courseInfo) {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 简单的关键词回复规则
    if (message.includes('你好') || message.includes('嗨') || message.includes('hi')) {
      return `你好！我是"${courseInfo}"课程的AI助手，有什么可以帮助您的吗？`;
    }
    
    if (message.includes('课程') || message.includes('学习')) {
      return `这门"${courseInfo}"课程包含了很多有用的知识点，建议您仔细阅读课程概述和学习资料部分。有具体问题可以随时向我提问！`;
    }
    
    if (message.includes('谢谢') || message.includes('感谢')) {
      return '不用谢！如果还有其他问题，随时可以问我。';
    }
    
    // 默认回复
    return `您的问题我已收到，这是关于"${courseInfo}"课程的问题。作为AI助手，我会尽力解答您的疑问。您可以更具体地描述您的问题，这样我能提供更准确的帮助。`;
  }

  /**
   * 获取每门课程的最大提问次数
   * @returns {Number} 最大提问次数
   */
  getMaxQuestionsPerCourse() {
    return this.config.maxQuestionsPerCourse;
  }
}

// 创建单例实例
const aiService = new AiService();

module.exports = aiService; 