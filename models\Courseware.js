'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Courseware extends Model {
    static associate (models) {
      // define association here
      this.hasMany(models.CourseSource, { as: 'courseSources', foreignKey: 'coursewareId', constraints: false })
      this.belongsTo(models.VirtualProcess, { foreignKey: 'processTypeId', constraints: false })
      this.hasOne(models.VirtualData, { sourceKey: 'processTypeId', foreignKey: 'code', constraints: false })
    }
  }
  Courseware.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      coursewareName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "课程名称"
      },
      coursewareContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "课程概述"
      },
      coursewarePic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "封面图片"
      },
      processTypeId: {
        type: DataTypes.STRING,
        allowNull: true,
        // defaultValue: null,
        comment: "所属工艺流程id"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '状态0是下架1是上架',
      },
      isDraft: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '状态1是草稿0不是',
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      modelName: 'Courseware',
      tableName: 'zk_courseware',
      timestamps: true,
      comment: '课程信息表'
    }
  )
  return Courseware
}
