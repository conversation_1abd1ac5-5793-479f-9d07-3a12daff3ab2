import { storage, tokenTableName } from "@/config/settings";
import cookie from "js-cookie";

export function getRefreshToken () {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.getItem(tokenTableName + "_refresh");
    } else if ("sessionStorage" === storage) {
      return sessionStorage.getItem(tokenTableName + "_refresh");
    } else if ("cookie" === storage) {
      return cookie.get(tokenTableName + "_refresh");
    } else {
      return localStorage.getItem(tokenTableName + "_refresh");
    }
  } else {
    return localStorage.getItem(tokenTableName + "_refresh");
  }
}

export function setRefreshToken (accessToken) {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.setItem(tokenTableName + "_refresh", accessToken);
    } else if ("sessionStorage" === storage) {
      return sessionStorage.setItem(tokenTableName + "_refresh", accessToken);
    } else if ("cookie" === storage) {
      return cookie.set(tokenTableName + "_refresh", accessToken);
    } else {
      return localStorage.setItem(tokenTableName + "_refresh", accessToken);
    }
  } else {
    return localStorage.setItem(tokenTableName + "_refresh", accessToken);
  }
}

export function removeRefreshToken () {
  if (storage) {
    if ("localStorage" === storage) {
      return localStorage.removeItem(tokenTableName + "_refresh");
    } else if ("sessionStorage" === storage) {
      return sessionStorage.clear();
    } else if ("cookie" === storage) {
      return cookie.remove(tokenTableName + "_refresh");
    } else {
      return localStorage.removeItem(tokenTableName + "_refresh");
    }
  } else {
    return localStorage.removeItem(tokenTableName + "_refresh");
  }
}
