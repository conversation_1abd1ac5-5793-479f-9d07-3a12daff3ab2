<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="课程名称" prop="coursewareName">
        <el-input placeholder="请输入课程名称" v-model="formSearch.coursewareName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="所属工艺" prop="processTypeId">
        <el-select v-model="formSearch.processTypeId" placeholder="请选择所属工艺" clearable size="small">
          <el-option v-for="dict in coursewareTypeSelect" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in statusOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['courseware/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增课程</el-button>
        <!-- <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button> -->
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <template #imgSlot="{row}">
        <el-image style="height:70px;" :fit="'contain'" :src="row.coursewarePic?$SOURCEURL+row.coursewarePic:''" :preview-src-list="srcList">
          <div slot="error" class="el-image__error">
            <i class="el-icon-picture-outline" style="min-width:70px;font-size:32px"></i>
          </div>
        </el-image>
      </template>
      <template v-slot:processTypeSlot="{ row }">
        <el-tag>{{row.processTypeName}}</el-tag>
      </template>
      <template v-slot:sourceSlot="{ row }">
        <div>
          <div class="sourceInfo" v-for="item,i in row.courseSources" :key="i" :title="item.sourceName">
            <el-link type="primary" :href="item.sourceUrl?$SOURCEURL+item.sourceUrl:''" :title="item.sourceName" target="_blank" icon="el-icon-link"> {{item.sourceName}}</el-link>
          </div>
        </div>
      </template>
      <template v-slot:statusSlot="{ row }">
        <el-tag :type="row.status!=1?'danger':'success'">
          <icon-dot :class="row.status!=1?'error':'success'" style="margin-top:-2px;" />{{row.status!=1?'待上架':'上架中'}}
        </el-tag>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <!-- <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button> -->
        <el-button type="text" v-permissions="['courseware/edit']" :icon="row.status!=1?'el-icon-upload2':'el-icon-download'" :style="row.status!=1?'color:#67C23A;':'color:#F56C6C;'" @click="handleStatus(row)">{{row.status!=1?'上架':'下架'}}</el-button>
        <el-button type="text" v-permissions="['courseware/edit']" icon="el-icon-edit" v-if="row.status!=1" @click="$router.push({path:'/courseware/coursewareOperator',query:{coursewareId:row.id}})">修改</el-button>
        <el-button type="text" v-permissions="['courseware/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import myMixin from './mixin.js';
import ElImageViewer from 'element-ui/packages/image/src/image-viewer';
export default {
  name: "CoursewareManage",
  mixins: [myMixin],
  components: { ElImageViewer },
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        // { type: 'checkbox', width: 60 },
        // { type: 'expand', width: 60, slots: { content: 'conExpand' } },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'coursewareName', title: '课程名称' },
        { field: 'coursewarePic', title: '封面图片', showOverflow: false, slots: { default: 'imgSlot' }, minWidth: 80 },
        { field: 'processTypeName', title: '所属工艺', slots: { default: 'processTypeSlot' }, width: 100 },
        { field: 'courseSources', title: '资源列表', showOverflow: false, align: 'left', headerAlign: 'center', slots: { default: 'sourceSlot' }, minWidth: 130 },
        { field: 'status', title: '状态', minWidth: 80, slots: { default: 'statusSlot' } },
        // { sortable: true, field: 'createBy', title: '创建人' },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      srcList: [],
      loading: false,
      formSearch: {
        coursewareName: undefined,
        processTypeId: undefined,
        status: undefined,
        page: 1,
        pageSize: 10
      },
      statusOption: [
        { value: 0, label: '待上架' },
        { value: 1, label: '上架中' },
      ],
      isExpand: false,
    };
  },
  created () {
    this.getData();
  },
  activated () {
    this.getData();
  },
  methods: {
    // 初始化
    getData () {
      this.loading = true;
      this.$http.coursewareList(this.formSearch)
        .then(res => {
          this.tableData = res.data
          this.tablePage.total = res.total;
          this.srcList = []
          res.data.forEach(item => {
            if (item.coursewarePic) {
              this.srcList.push(this.$SOURCEURL + item.coursewarePic)
            }
          })
          //分页后判断是否展开或折叠
          this.$nextTick(() => {
            if (this.isExpand) {
              this.$refs.xGrid.setAllRowExpand(true)
            } else {
              this.$refs.xGrid.clearRowExpand()
            }
          })
          this.loading = false;
        })
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllRowExpand(true)
      } else {
        this.$refs.xGrid.clearRowExpand()
      }
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {

      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    handleStatus (row) {
      if (row.status == 1) {
        row.status = 0
      } else {
        row.status = 1
      }
      this.$http.coursewareEdit(row).then(res => {
        this.$msg('操作成功', 'success');
        this.getData()
      });
    },
    handleAdd () {
      this.$router.push({ path: '/courseware/coursewareOperator' })
    },
    handleEdit (row) {
      this.$router.push({ path: '/courseware/coursewareOperator', query: { coursewareId: row.id } })
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.coursewareDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.sourceInfo {
  width: 100%;
  margin-left: 20px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
