<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="路径名称" prop="prefixName">
        <el-input placeholder="请输入名称" v-model="formSearch.prefixName" clearable
          @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData"
      :seq-config="{ startIndex: (formSearch.page - 1) * formSearch.pageSize }" :pager-config="Object.assign(
        { currentPage: formSearch.page, total: tablePage.total },
        formSearch
      )
        " @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{
          size: 'mini',
          custom: true,
          slots: { buttons: 'toolbar_left' },
        }">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['path/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['path/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['path/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer
      @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.classRules"
          title-width="120" title-align="right">
          <template #uploadExe="{ data }">
            <el-upload class="upload-demo" ref="uploadRef" :headers="{ token: $store.getters['user/accessToken'] }"
              :action="$BASEURL + '/file/uploadExe'" accept=".exe" :limit="1" :show-file-list="!loadings"
              :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :on-error="handleAvatarError"   :before-remove="beforeRemove"  >
              <el-button size="small" type="primary">上传exe文件</el-button>
              <!-- <div slot="tip" class="el-upload__tip">只能上传doc/docx文件，且不超过500kb</div> -->

              <!-- <div class="avaterBox">
                <img v-if="data.pic" :src="$SOURCEURL + data.pic" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div v-if="data.pic" class="avaterMask" @click.stop="delUploadImg">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div> -->
            </el-upload>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
    <!-- 全屏 loading 层 -->
    <div v-if="loadings" class="fullscreen-loading">
      <el-spinner></el-spinner>
      <p>上传中,请稍后...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "virtualAppPath",
  data() {
    return {
      srcList: [],
      modalTitle: "新增信息",
      modalVisible: false,
      loading: false,
      loadings: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: "seq", width: 60, title: "序号" },
        { sortable: true, field: "prefixName", title: "路径名称" },
        { sortable: true, field: "attchName", title: "课件名称" },
        { sortable: true, field: "attchAddr", title: "课件下载地址" },
        { sortable: true, field: "createTime", title: "创建时间" },
        { field: "remark", title: "备注" },
        { title: "操作", width: 220, slots: { default: "operate" }, align: "center", fixed: "right", },
      ],
      tableData: [],
      formData: {
        prefixName: "",
        attchName:'',
        remark: "",
      },
      formItem: [
        { field: "prefixName", title: "路径名称", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入路径" } }, },
        { field: "attchName", title: "课件名称", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入课件名称" } }, },
        {
          field: "exe",
          title: "文件上传", className: 'formTreeTitle',
          span: 24,
          slots: { default: "uploadExe" },
        },
        // disabled:true
        { field: "attchAddr", title: "课件下载地址", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入下载地址", }, }, },
        { field: "remark", title: "备注", span: 24, itemRender: { name: "$input", props: { placeholder: "请输入备注" } }, },
      ],
      formSearch: {
        prefixName: undefined,
        page: 1,
        pageSize: 10,
      },

    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData(params) {
      if (params == "formSearch") {
        this.formSearch.page = 1;
      }
      this.loading = true;
      this.$http.pathList(this.formSearch).then((res) => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    //重置按钮
    resetFormSearch() {
      this.$resetForm("queryForm");
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort();
        this.formSearch.sortName = undefined;
        this.formSearch.sortOrder = undefined;
      }
      this.getData();
    },
    //分页
    handlePageChange({ currentPage, pageSize }) {
      this.formSearch.page = currentPage;
      this.formSearch.pageSize = pageSize;
      this.getData();
    },
    //排序
    handleSortChange({ property, order }) {
      this.formSearch.sortName = property;
      this.formSearch.sortOrder = order;
      this.getData();
    },
    save() {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData;
          if (!form.id) {
            this.$http.pathAdd(form).then((res) => {
              this.$msg(res.msg, "success");
              this.close();
              this.getData();
            });
          } else {
            this.$http.pathEdit(form).then((res) => {
              this.$msg(res.msg, "success");
              this.close();
              this.getData();
            });
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.$refs.uploadRef.clearFiles()
      this.$refs.formRef.clearValidate();
      this.modalVisible = false;
    },
    handleAdd() {
      this.modalTitle = "新增信息";
      this.modalVisible = true;
      this.formData = {
        prefixName: "",
        attchName:"",
        attchAddr: "",
        remark: "",
      };
    },
    handleEdit(row) {
      this.modalTitle = "编辑信息";
      this.formData = JSON.parse(JSON.stringify(row));
      this.modalVisible = true;
    },
    handleDel(row) {
      let list = "";
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg("请选择删除项", "warning");
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ",";
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm("请确认是否删除此数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.pathDel(list).then((res) => {
          this.$msg(res.msg, "success");
          this.getData();
        });
      });
    },
    handleAvatarSuccess(res, file) {
      this.formData.attchAddr = res.url;
      this.$message.success("上传成功！");
      this.loadings = false;
    },
    beforeRemove(file, fileList) {
        this.formData.attchAddr=null
      },
    // 上传失败时也隐藏 loading
    handleAvatarError(err, file, fileList) {
      this.$message.error(err.message)
      this.loadings = false;
    },
    beforeAvatarUpload(file) {
      this.loadings = true;
      // const isLt2M = file.size / 1024 / 1024 < 5;
      // if (!isLt2M) {
      //   this.$message.error("上传文件大小不能超过5MB!");
      // }
      // return isLt2M;
    },
    delUploadImg(e) {
      this.formData.attchAddr = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
/* 隐藏上传文件列表 */
.el-upload__file-list {
  display: none;
}

/* 隐藏进度条（如果你也希望隐藏进度条） */
.el-upload .el-progress {
  display: none;
}

::v-deep .formTreeTitle {
  .vxe-form--item-inner {
    align-items: start;

    .vxe-form--item-title {
      vertical-align: top;
    }
  }
}

.fullscreen-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 89999999;
}
</style>
