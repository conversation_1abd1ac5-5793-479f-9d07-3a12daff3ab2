import "core-js/stable";
import "regenerator-runtime/runtime";
import Vue from "vue";
import App from "./App";
import store from "./store";
import router from "./router";
import "./plugins";

import { download } from '@/utils/request'
import downloadFun from '@/utils/download'
import rules from '@/utils/rules';
import http from "./api/http_czk"
import {resetForm,xModalOpen,msg,searchChildren,getBrowserKernelVersion} from "@/utils/index"
import { baseURL } from "@/config/settings";
import Encrypts from '@/utils/encrypts';

 
//通用文件导出接口
Vue.prototype.$Encrypts = Encrypts;
Vue.prototype.$download = download;
//通用模板下载等其它封装方法
Vue.prototype.$downloadFun = downloadFun;
// 效验
Vue.prototype.$rules = rules;
//全局api
Vue.prototype.$http = http

// 判断浏览器内核版本
Vue.prototype.$getBrowserKernelVersion = getBrowserKernelVersion
//form表单重置,vxe模态框打开,提示信息
Vue.prototype.$resetForm = resetForm
Vue.prototype.$xModalOpen = xModalOpen
Vue.prototype.$msg = msg
Vue.prototype.$searchChildren = searchChildren
//全局baseurl
Vue.prototype.$BASEURL = process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL : baseURL
Vue.prototype.$SOURCEURL = process.env.NODE_ENV === 'production' ? VUE_APP_BASEURL.split('/api')[0] : baseURL.split('/api')[0]

if (process.env.NODE_ENV === "preview") {
  const { mockXHR } = require("../mock/static");
  mockXHR();
}

Vue.config.productionTip = false;

new Vue({
  el: "#vue-admin-beautiful",
  router,
  store,
  render: (h) => h(App),
});
