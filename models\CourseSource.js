'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class CourseSource extends Model {
    static associate (models) {
      // define association here
      this.belongsTo(models.Courseware, { foreignKey: 'coursewareId', constraints: false })
    }
  }
  CourseSource.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      coursewareId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "课程id"
      },
      sourceName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "资源名称"
      },
      sourceUrl: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: '',
        comment: "课程资源地址"
      },
      previewUrl: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: '',
        comment: "预览地址"
      }
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'CourseSource',
      tableName: 'zk_courseware_source',
      comment: '课程资源关联表'
    }
  )
  return CourseSource
}
