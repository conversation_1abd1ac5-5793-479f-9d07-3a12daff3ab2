<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="字典编码" prop="dictCode">
        <!-- <el-select v-model="formSearch.dictCode" :disabled="disabled" placeholder="请选择字典编码" clearable size="small"
          @change="getData('formSearch')">
          <el-option v-for="dict in dictCodeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select> -->
        <el-input v-model="formSearch.dictCode" :disabled="disabled" placeholder="请输入字典编码" clearable size="small" @change="getData('formSearch')">
        </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="状态" clearable size="small" @change="getData('formSearch')">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini"
          @click="() => { this.$resetForm('queryForm'); getData() }">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid :height="$store.getters['table/srcTableHeight']" ref="xGrid" row-id="id"
      :tree-config="{ children: 'children', iconOpen: 'vxe-icon--arrow-bottom', iconClose: 'vxe-icon--arrow-right', iconLoaded: 'vxe-icon--refresh roll' }"
      :toolbarConfig="{ size: 'mini', custom: true, slots: { buttons: 'toolbar_left' } }" v-loading="loading"
      :columns="tableColumn" :data="tableData">
      <!-- v-hasPermi="['system:menu:add']" -->
      <template #toolbar_left>
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status == 1 ? 'default' : 'danger'">{{ row.status == 1 ? '正常' : '停用' }}</el-tag>
      </template>
      <template #operate="{ row }">
        <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal ref="xModal" v-model="modalVisible" width="800" height="600" :title="modalTitle"
      @show="$xModalOpen('xModal')" :show-zoom="$store.state.settings.formSpan == 12 ? true : false" mask-closable
      esc-closable show-footer resize transfer>
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :span="$store.state.settings.formSpan" :data="formData" :items="formItem"
          :rules="$rules.userRules" title-width="100" title-align="right" title-overflow='tooltip'>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  name: "DictData",
  data () {
    return {
      tableColumn: [
        { field: 'dictValue', title: '字典value' },
        { field: 'dictLabel', title: '字典label' },
        { field: 'listClass', title: '回显样式' },
        { field: 'isDefault', title: '是否默认值' },
        { field: 'status', title: '状态', slots: { default: 'status' } },
        { field: 'sortBy', title: '排序' },
        { field: 'createTime', title: '创建时间' },
        { field: 'remark', title: '备注', minWidth: 120 },
        { title: '操作', width: 200, slots: { default: 'operate' } }
      ],
      tableData: [],
      formSearch: {
        dictCode: '',
        status: '',
      },
      statusOptions: [
        { value: 1, label: '正常' },
        { value: 2, label: '停用' },
      ],
      menuOptions: [],
      formData: {},
      formItem: [
        { field: 'dictValue', title: '字典value', itemRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'dictLabel', title: '字典label', itemRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'listClass', title: '回显样式', itemRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'isDefault', title: '是否默认值', itemRender: { name: '$select', props: { placeholder: '请选择', options: [{ value: 'Y', label: 'Y' }, { value: 'N', label: 'N' }] } } },
        { field: 'sortBy', title: '排序', itemRender: { name: '$input', props: { type: 'integer', placeholder: '请输入排序号' } } },
        { field: 'status', title: '状态', itemRender: { name: '$radio', options: [{ label: '正常', value: 1 }, { label: '停用', value: 2 }] } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      modalTitle: '新增信息',
      modalVisible: false,
      disabled: false,
    };
  },
  created () {
    if (this.$route.query.dictCode) {
      this.disabled = true
      this.formSearch.dictCode = this.$route.query.dictCode

    }
    this.getData();
  },
  methods: {
    getData (params) {
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.dictDataList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.loading = false;
        this.$refs.xGrid.setAllTreeExpand(true);
      });
    },
    handleAdd () {
      this.modalTitle = '新增信息';
      this.formData = {
        dictCode: this.formSearch.dictCode,
        dictValue: '',
        dictLabel: '',
        listClass: '',
        isDefault: 'N',
        sortBy: 1,
        status: 1,
        remark: '',
      }
      this.modalVisible = true;
    },
    handleEdit (row) {
      this.modalTitle = '修改信息';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    // 删除菜单
    handleDel (row) {
      this.$confirm('请确认是否删除此数据及其所有子节点数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.dictDataDel(row.id).then(() => {
          this.$msg('删除成功', 'success');
          this.getData();
        });
      })
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData
          if (!this.formData.id) {
            this.$http.dictDataAdd(form).then(res => {
              this.$msg('新增成功', 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.dictDataEdit(form).then(res => {
              this.$msg('修改成功', 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
      this.modalVisible = false
    },
  }
};
</script>

<style scoped></style>
