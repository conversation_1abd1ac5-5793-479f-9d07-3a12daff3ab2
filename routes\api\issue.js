var express = require("express");
var router = express.Router();
const { nanoid } = require('nanoid')
const fs = require('fs')
const path = require('path')
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/IssueService");

router.get('/list', tryAsyncErrors(Service.list))
router.get('/list/:id', tryAsyncErrors(Service.getOne))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

module.exports = router;
