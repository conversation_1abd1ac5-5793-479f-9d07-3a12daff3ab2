var express = require('express')
var router = express.Router()
const path = require('path')
const { unzipZipFile } = require('../../utils/zip')
const mammoth = require('mammoth')
const { exec, spawn } = require('child_process');
const fs = require('fs')
const { uploadImg, uploadVideo, uploadFile, uploadExe, uploadWebRar, uploadSourceFile, uploadInitVR, tryAsyncErrors } = require('../../utils/index')

const Service = require("../../services/FileService");
const ConfigService = require('../../services/ConfigService')

//上传文件中间件，处理异常问题
function uploadMiddleware (req, res, next) {
  try {
    if (req.url == '/upload') {
      uploadImg.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    } else if (req.url == '/uploadVideo') {
      uploadVideo.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    } else if (req.url == '/uploadFile' || req.url == '/uploadFileGetContent') {
      uploadFile.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    } else if (req.url == '/uploadExe') {
      uploadExe.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    } else if (req.url == '/uploadWebRar') {
      uploadWebRar.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    } else if (req.url == '/uploadSourceFile') {
      uploadSourceFile.single('file')(req, res, (err) => {
        if (err) {
          next(err)
        } else {
          next()
        }
      })
    }
  } catch (error) {
    next(error)
  }
}

//文件转换函数---通过调用libreOffice
function fileConversion (destination, path, fileType, oldPath, newPath) {
  let cmdstr = `cd ${destination} && soffice --headless --convert-to ${fileType} ${path} --outdir ${destination}`
  
  exec(cmdstr, { timeout: 600000 }, (error, stdout, stderr) => {
    if (error) {
      console.log('LibreOffice转换失败:', JSON.stringify(error));
      console.log('命令:', cmdstr);
      console.log('stderr:', stderr);
      return;
    }
    
    console.log("转换成功");
    console.log(`stdout: ${stdout}`);
    
    // 检查转换后的文件是否存在
    if (fs.existsSync(oldPath)) {
      fs.rename(oldPath, newPath, function (err) {
        if (err) {
          console.log('文件重命名失败:', err);
          return;
        }
        console.log('预览文件生成成功');
      });
    } else {
      console.log('转换后的文件不存在:', oldPath);
    }
  });
}


async function getWordHtmlContent (filePath, req, res, next) {
  let url = path.resolve('./public/' + filePath)
  let fileObj = req.file
  let destination = fileObj.destination
  let fileName = fileObj.filename.split('.')[0]
  let dir = destination + '/' + fileName;
  // 判断目录是否存在，没有则创建
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  try {
    let options = {
      convertImage: mammoth.images.imgElement(function (image) {
        var fileType = image.contentType.toLowerCase();
        fileType = fileType.substring(fileType.indexOf('/') + 1);
        const imageName = Date.now() + '' + Number.parseInt(Math.random() * 10000) + "." + fileType;
        const imageFullName = path.join(dir, imageName);
        return image.read("base64").then(async function (imageBuffer) {
          // console.log(imageBuffer);
          var dataBuffer = Buffer.from(imageBuffer, 'base64');
          fs.writeFileSync(imageFullName, dataBuffer, "binary");
          // let resultPath = `/uploadFile/images/${dayStr}/${imageName}`;
          // let uploadResult = await ctx.helper.reqJsonData('upload/filePath', {
          //   imgPath: resultPath,
          //   localImgPath: imageFullName,
          //   filename: imageName
          // }, "post");
          // // console.log('---uploadResult--', uploadResult);
          return {
            src: 'http://' + req.headers.host + imageFullName.split('public')[1]
          };
        });
      })
    };
    let result = await mammoth.convertToHtml({
      path: url
    }, options)
    return result
  } catch (error) {
    console.log(error, 'error');
    next(error)
  }
}

//图片上传接口
router.post('/upload', uploadMiddleware, async (req, res, next) => {
  let imgPath = req.file.path.split('public')[1]
  let imgUrl = imgPath
  res.send({ code: 200, msg: '上传成功', data: imgUrl, url: imgUrl })
})

//视频上传接口
router.post('/uploadVideo', uploadMiddleware, async (req, res, next) => {
  let videoPath = req.file.path.split('public')[1]
  let videoUrl = videoPath
  res.send({ code: 200, msg: '上传成功', data: videoUrl, url: videoUrl, videoName: req.file.originalname })
})

//exe文件上传接口
router.post('/uploadExe', uploadMiddleware, async (req, res, next) => {
  let filePath = req.file.path.split('public')[1]
  let fileUrl = filePath
  // let result = await getWordHtmlContent(filePath, req)
  // console.log(html,'html');
  res.send({ code: 200, msg: '上传成功', data: fileUrl, url: fileUrl })
})

//rar,zip文件上传接口
router.post('/uploadWebRar', uploadMiddleware, async (req, res, next) => {
  const config = await ConfigService.getInfo()
  let webUrl = 'http://' + req.headers.host
  if (config.webPrefixUrl) {
    webUrl = config.webPrefixUrl
  }
  try {
    unzipZipFile(req.file.path, req.file.destination)

    //获取index.html
    let getIndexHtmlUrl = function (dirPath) {
      // 读取目录中所有文件
      let result = [];
      let filenames = fs.readdirSync(dirPath);
      for (let i = 0; i < filenames.length; i++) {
        let filename = filenames[i];
        let filePath = path.join(dirPath, filename);
        let stat = fs.statSync(filePath);
        if (filename == "index.html") {
          result.push(filePath)
        }
        if (stat.isDirectory()) {
          result = result.concat(getIndexHtmlUrl(filePath));
        }
      }
      return result;
    }
    let indexUrlArr = getIndexHtmlUrl(req.file.destination)
    let indexUrl = indexUrlArr[0]

    if (!indexUrl) return res.send({ code: -1, msg: '未查找到相关文件' })
    let splitArr = []
    let finalUrl = ""
    if (config.webUploadUrl) {
      splitArr = indexUrl.split(config.webUploadUrl)
      finalUrl = splitArr[splitArr.length - 1]
      finalUrl = finalUrl.replace(/\\/g, "/")
    } else {
      splitArr = indexUrl.split("uploadWeb")
      finalUrl = "/virtualWeb" + splitArr[splitArr.length - 1]
      finalUrl = finalUrl.replace(/\\/g, "/")
    }
    webUrl += finalUrl
  } catch (error) {
    next(error)
  }
  res.send({ code: 200, msg: '上传成功', data: webUrl, url: webUrl })
})

//文件上传接口
router.post('/uploadFile', uploadMiddleware, async (req, res, next) => {
  let filePath = req.file.path.split('public')[1]
  let fileUrl = filePath
  res.send({ code: 200, msg: '上传成功', data: fileUrl, html, url: fileUrl })
})

//文件上传接口并获取docx内容
router.post('/uploadFileGetContent', uploadMiddleware, async (req, res, next) => {
  let filePath = req.file.path.split('public')[1]
  let fileUrl = filePath
  let result = await getWordHtmlContent(filePath, req, res, next)
  res.send({ code: 200, msg: '上传成功', data: fileUrl, html: result.value, url: fileUrl })
})

//资源文件上传接口
router.post('/uploadSourceFile', uploadMiddleware, async (req, res, next) => {
  try {
    let filePath = req.file.path
    let name = req.file.originalname
    let fileUrl = '\\uploadSourceFile' + req.file.path.split('uploadSourceFile')[1]
    let fileArr = ('\\uploadSourceFile' + filePath.split('uploadSourceFile')[1]).split('.')
    let prefix = fileArr[0]
    let suffix = fileArr[1].toLowerCase() // 转为小写
    let array = ['doc', 'docx', 'xls', 'xlsx']
    let fileType = (suffix == 'xls' || suffix == 'xlsx') ? 'html' : 'pdf'
    let oldPath = prefix + '.' + fileType
    let newPath = prefix + '_preview.' + fileType
    
    if (array.includes(suffix)) {
      fileConversion(req.file.destination, req.file.path, fileType, oldPath, newPath)
    }
    
    res.send({ 
      code: 200, 
      msg: '上传成功', 
      name, 
      url: fileUrl, 
      previewUrl: newPath 
    })
  } catch (error) {
    console.error('上传处理失败:', error);
    res.send({ 
      code: -1, 
      msg: '文件处理失败: ' + error.message 
    });
  }
})

//进程信息初始化上传接口
router.post('/vrProcessUpload', uploadInitVR.single('file'), tryAsyncErrors(Service.vrProcessUpload))
//设备信息初始化上传接口
router.post('/vrDeviceUpload', uploadInitVR.single('file'), tryAsyncErrors(Service.vrDeviceUpload))
//操作信息初始化上传接口
router.post('/vrOperationUpload', uploadInitVR.single('file'), tryAsyncErrors(Service.vrOperationUpload))

//excel模板接口
router.get('/tempDownload', function (req, res) {
  let { fileName } = req.query
  fileName = fileName.replace(/\.\.\//g, '');
  let tarpath = './public/exportTemp/' + fileName
  // var size = fs.statSync(path).size
  if (fs.existsSync(tarpath)) {
    var stream = fs.createReadStream(tarpath)
    var userAgent = (req.headers['user-agent'] || '').toLowerCase()
    //跨域中需要加上下面这段代码，前端才能获取到自定义头字段
    // res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition')

    if (userAgent.indexOf('msie') >= 0 || userAgent.indexOf('chrome') >= 0) {
      /*谷歌和IE浏览器下载*/
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          'attachment; filename=' + encodeURIComponent(fileName),
      })
    } else if (userAgent.indexOf('firefox') >= 0) {
      /*火狐浏览器下载*/
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          "attachment; filename*=\"utf8''" + encodeURIComponent(fileName) + '"',
      })
    } else {
      /* safari等其他非主流浏览器只能自求多福了 */
      res.writeHead(200, {
        'Content-Type': 'application/force-download;charset=utf-8',
        'Content-Disposition':
          'attachment; filename=' + new Buffer(fileName).toString('binary'),
      })
    }
    stream.pipe(res)
  } else {
    res.json({ code: 403, success: false, error: 'file not exit' })
  }
})

module.exports = router



