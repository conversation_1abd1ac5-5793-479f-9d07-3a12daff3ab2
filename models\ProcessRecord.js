'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class ProcessRecord extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsTo(models.TipArticle,{foreignKey:'articleId',constraints:false})
      // this.belongsTo(models.User,{foreignKey:'userId',constraints:false})
      this.belongsTo(models.Question, { foreignKey: 'questionId', constraints: false })
      this.hasMany(models.QuestionAnswer, { as: 'questionAnswers', foreignKey: 'questionId', sourceKey: 'questionId', constraints: false })
      this.belongsTo(models.Record, { foreignKey: 'recordId', constraints: false })
      this.belongsTo(models.Issue, { foreignKey: 'issueId', constraints: false })
    }
  }
  ProcessRecord.init(
    {
      // processRecord: {
      //   type: DataTypes.VIRTUAL,
      //   get () {
      //     return {
      //       deviceOperationId: this.deviceOperationId,
      //       virtualprocessId: this.virtualprocessId,
      //       deviceId: this.deviceId,
      //       questionId: this.questionId
      //     }
      //   },
      // },
      radioData: {
        type: DataTypes.VIRTUAL,
        get () {
          return ''
        },
      },
      checkboxData: {
        type: DataTypes.VIRTUAL,
        get () {
          return []
        },
      },
      processRecordId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "评论唯一ID"
      },
      recordId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue: null,
        comment: "记录ID"
      },
      virtualId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue: null,
        comment: "仿真数据ID（改版后只需这一个id，之前3个id不需要）"
      },
      code: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: null,
        comment: "仿真数据code,唯一标识"
      },
      virtualprocessId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "工艺进程id"
      },
      deviceId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备id"
      },
      deviceOperationId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },
      issueId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "议题id"
      },
      questionId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "题目id"
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建者id"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'ProcessRecord',
      tableName: 'zk_process_record',
      comment:'流程记录与题目关联表'
    }
  )
  return ProcessRecord
}
