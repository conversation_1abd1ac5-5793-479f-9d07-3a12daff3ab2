<template>
  <div>
    <el-row :gutter="10" class="singleChoice">
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="cardTitle">题目编辑区域：</span>
          </div>
          <el-form :model="formData" :rules="$rules.questionRules" ref="formRef" label-width="120px"
            class="demo-formData">
            <el-form-item label="所属工艺流程" prop="classType">
              <el-select v-model="formData.classType" placeholder="请选择所属工艺流程">
                <el-option v-for="item in questionTypeSelect" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="题干" prop="questionContent">
              <el-input v-model="formData.questionContent" placeholder="请输入题干">
                <template slot="suffix">
                  <i class="el-icon-edit-outline suffixIcon"
                    @click="openEdit('questionContent', formData.questionContent)"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="题目选项" required prop="questionAnswers">
              <vxe-grid ref="xGrid" :columns="tableColumn" :data="formData.questionAnswers" @radio-change="radioChange"
                :edit-config="{ trigger: 'manual', mode: 'row' }"
                :toolbarConfig="{ size: 'mini', slots: { buttons: 'toolbar_left' } }">
                <template #toolbar_left>
                  <el-button type="primary" plain icon="el-icon-plus" @click="answerOptionAdd"
                    size="mini">增加选项</el-button>
                </template>
                <!-- input的内容 -->
                <template v-slot:answerNumSlot="{ row }">
                  <el-input v-model="row.answerNum"></el-input>
                </template>
                <!-- input的内容 -->
                <template v-slot:answerContentSlot="{ row, rowIndex }">
                  <el-input v-model="row.answerContent">
                    <template slot="suffix">
                      <i class="el-icon-edit-outline suffixIcon"
                        @click="openEdit('answerContent', row.answerContent, rowIndex)"></i>
                    </template>
                  </el-input>
                </template>
                <!-- 表格的侧边操作栏按钮 -->
                <template v-slot:operate="{ row, rowIndex }">
                  <el-button type="danger" size="mini" icon="el-icon-delete"
                    @click="answerOptionDel(row, rowIndex)">删除</el-button>
                </template>
              </vxe-grid>
            </el-form-item>
            <el-form-item label="分析" prop="questionAnalysis">
              <el-input v-model="formData.questionAnalysis">
                <template slot="suffix">
                  <i class="el-icon-edit-outline suffixIcon"
                    @click="openEdit('questionAnalysis', formData.questionAnalysis)"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="难度" prop="questionLevel" class="questionLevel">
              <el-rate v-model="formData.questionLevel"></el-rate>
            </el-form-item>
            <el-form-item style="text-align:center">
              <el-button type="primary" v-permissions="['question/add','question/edit']" @click="save">保存</el-button>
              <el-button type="primary" v-permissions="['question/add','question/edit']" @click="save('continue')">保存并继续</el-button>
              <el-button @click="close">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="cardTitle">题目预览:</span>
          </div>
          <div v-html="formData.questionContent" style="margin-bottom: 15px"></div>

          <el-row :gutter="10" v-for="(item, i) in formData.questionAnswers" :key="i" class="questionBox">
            <el-col :span="3">
              <el-button :type="item.isRight == '1' ? 'success' : ''" size="mini">{{ item.answerNum }}</el-button>
            </el-col>
            <el-col :span="21">
              <p v-html="item.answerContent"></p>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="margin-bottom: 10px">
            <el-col :span="4"> 分析:</el-col>
            <el-col :span="18">
              <div v-html="formData.questionAnalysis"></div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="4">难度:</el-col>
            <el-col :span="18">
              <el-rate v-model="formData.questionLevel" disabled> </el-rate>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <DialogEditor ref="editorRef" @getContent="contentSave"></DialogEditor>
  </div>
</template>

<script>
import DialogEditor from "@/components/DialogEditor"
export default {
  name: "SingleChoice",
  components: {
    DialogEditor
  },
  data() {
    return {
      tableColumn: [
        { title: '序号', width: 60, slots: { default: ({ row }) => { return row.answerNum } } },
        { field: 'answerNum', title: '选项', width: 80, slots: { default: 'answerNumSlot' } },
        { field: 'answerContent', title: '选项内容', slots: { default: 'answerContentSlot' } },
        { type: 'radio', title: '答案', field: 'isRight', width: 100 },
        { title: '操作', width: 100, slots: { default: 'operate' }, align: 'center' },
      ],
      questionTypeSelect: [],
      formData: {
        classType: undefined,
        questionType: 1,//单选还是多选
        questionContent: '',
        questionAnswers: this.$store.getters['settings/questionOptions'],
        questionAnalysis: '',
        questionLevel: 0,
      },
      editorParams: '',//富文本保存时，判断是哪个参数
      editorTableIndex: 0,//表格中哪一选项的判断参数
    };
  },
  created() {
    if (this.$route.query.questionId) {
      this.updateFormData()
    } else {
      this.close()
    }
    this.getProcessFlow()
  },
  activated() {
    console.log(this.$route, 'activated');
    if (this.$route.query.questionId) {
      this.updateFormData()
    } else {
      this.close()
    }
  },
  // watch: {
  //   $route (val) {
  //     console.log(val, this.$route, 'val');
  //   },
  // },
  methods: {
    getProcessFlow() {
      this.$http.getDictList({ dictField: 'processFlow' }).then(res => {
        this.questionTypeSelect = res.data;
      });
    },
    updateFormData() {
      this.$http.questionOne(this.$route.query.questionId)
        .then(res => {
          this.formData = res.data
          if (res.data.questionAnswers && res.data.questionAnswers.length == 0) {
            this.formData.questionAnswers = this.$store.getters['settings/questionOptions']
          }
          this.$refs.xGrid.setRadioRow(res.data.questionAnswers.filter((item) => {
            return item.isRight == '1'
          })[0], true)
        });
    },
    /* 
      @params str 区分参数名
      @params value 参数值
      @params index 区分表格中的参数值
     */
    openEdit(str, value, index) {
      console.log(str, value, index);

      if (!str) return this.$msg('编辑参数缺失，请联系开发人员', 'warning')
      this.editorParams = str
      if (index) {
        this.editorTableIndex = index
      } else {
        this.editorTableIndex = 0
      }
      this.$refs.editorRef.content = value
      this.$refs.editorRef.modalVisible = true
    },
    answerOptionAdd() {
      let str = this.formData.questionAnswers[this.formData.questionAnswers.length - 1].answerNum
      let num = str.charAt(str.length - 1).charCodeAt()


      let newStr = String.fromCharCode(num + 1)
      this.formData.questionAnswers.push({ answerNum: newStr, answerContent: '', isRight: 2 })
    },
    answerOptionDel(row, rowIndex) {
      this.formData.questionAnswers.splice(rowIndex, 1)
    },
    radioChange({ row, rowIndex }) {
      this.formData.questionAnswers.map(item => {
        item.isRight = 2
      })
      this.formData.questionAnswers[rowIndex].isRight = 1
    },
    save(params) {
      //vxe-table的form与elementui的form效验不一样，vxe-table的valid要为false，elementui要为true
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          //效验是否有选正确答案选项并赋值
          let trueIndex = this.$refs.xGrid.getRadioRecord()
          if (!trueIndex) return this.$msg('请在题目选项中选择正确答案！', 'warning')
          this.formData.questionAnswers.forEach((item) => {
            if (item.answerNum == trueIndex.answerNum && item.answerContent == trueIndex.answerContent) {
              item.isRight = 1
            }
          })
          //效验选项是否填写完整
          let arr = this.formData.questionAnswers.filter(item => {
            return !item.answerNum || !item.answerContent
          })
          if (arr.length > 0) return this.$msg('请将题目选项填写完整！', 'warning')
          //区分新增还是修改
          if (!this.formData.questionId) {
            this.$http.questionAdd(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              if (params != 'continue') {
                this.$router.push({ path: '/question/questionManage' })
              }
            });
          } else {
            this.$http.questionEdit(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              if (params != 'continue') {
                this.$router.push({ path: '/question/questionManage' })
              }
            });
          }
        } else {
          return false
        }
      })
    },
    close() {
      this.$resetForm('formRef');
      this.$nextTick(() => {
        this.formData.questionAnswers = null
        this.formData.questionAnswers = this.$store.getters['settings/questionOptions']
        this.$refs.formRef.clearValidate()
      })
    },
    contentSave(value) {
      if (this.editorParams != 'answerContent') {
        this.formData[this.editorParams] = value
      } else {
        this.formData['questionAnswers'][this.editorTableIndex][this.editorParams] = value
        // this.formData['questionAnswers'] = this.tableData
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.singleChoice {
  .cardTitle {
    font-size: 16px;
    color: $base-color-default;
  }

  .suffixIcon {
    font-size: 22px;
    margin-top: 6px;
    cursor: pointer;
  }

  .questionLevel ::v-deep {
    .el-rate {
      line-height: 45px;
    }
  }

  .questionBox ::v-deep {
    margin-bottom: 5px;

    p {
      margin: 3px;
    }
  }

  // .vxe-modal--wrapper.type--alert .vxe-modal--body, .vxe-modal--wrapper.type--alert .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--confirm .vxe-modal--body, .vxe-modal--wrapper.type--confirm .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--modal .vxe-modal--body, .vxe-modal--wrapper.type--modal .vxe-modal--body .vxe-modal--content {
  //   overflow: hidden!important;
  // }
}
</style>
