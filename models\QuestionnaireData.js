'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class QuestionnaireData extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsTo(models.Questionnaire, { foreignKey: 'formKey', constraints: false })
    }
  }
  QuestionnaireData.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },
      quesId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "问卷id"
      },
      formKey: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "问卷key"
      },
      userId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "用户id"
      },
      dataJson:{
        type: DataTypes.JSON,
        allowNull: false,
        comment: '数据json',
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'QuestionnaireData',
      tableName: 'zk_questionnaire_data',
      comment:'问卷数据表'
    }
  )
  return QuestionnaireData
}
