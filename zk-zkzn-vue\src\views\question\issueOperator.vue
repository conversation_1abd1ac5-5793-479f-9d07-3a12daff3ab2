<template>
  <el-row :gutter="10" class="singleChoice">
    <el-col>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="cardTitle">议题创编：</span>
        </div>
        <el-form :model="formData" :rules="$rules.issueRules" ref="formRef" label-width="120px" class="demo-formData">
          <el-form-item label="所属工艺流程" prop="processTypeId">
            <el-select v-model="formData.processTypeId" placeholder="请选择所属工艺流程">
              <el-option v-for="item in issueTypeSelect" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="议题名称" prop="issueName">
            <el-input v-model="formData.issueName" placeholder="请输入议题名称"></el-input>
          </el-form-item>
          <el-form-item label="议题内容" prop="issueContent" class="issueContent">
            <el-upload class="upload-demo" :headers="{ token: $store.getters['user/accessToken'] }" accept=".doc,.docx" :action="$BASEURL+'/file/uploadFile'" :before-upload="beforeUpload" :on-error="uploadErr" :on-success="uploadSuccess">
              <el-button size="small" type="primary">导入Word文档</el-button>
              <div slot="tip" class="el-upload__tip">只能上传doc/docx文件，且不超过500kb</div>
            </el-upload>
            <WangEditor ref="wangEditorRef" :text="formData.issueContent"></WangEditor>
          </el-form-item>
          <el-form-item style="text-align:center">
            <el-button type="primary" @click="save">保存</el-button>
            <el-button type="primary" @click="save('continue')">保存并继续</el-button>
            <el-button @click="close">取消</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import WangEditor from '@/components/WangEditor'
export default {
  name: "issueOperator",
  components: {
    WangEditor
  },
  data () {
    return {
      issueTypeSelect: [],
      formData: {
        processTypeId: 1,
        issueName: '',//单选还是多选
        issueContent: '',
        issueAnalysis: '',
        issueLevel: 0,
      },
    };
  },
  created () {
    if (this.$route.query.issueId) {
      this.updateFormData()
    } else {
      this.close()
    }
    this.getProcessFlow()
  },
  activated () {
    if (this.$route.query.issueId) {
      this.updateFormData()
    } else {
      this.close()
    }
  },
  methods: {
    getProcessFlow () {
      this.$http.getDictList({ dictField: 'processFlow' }).then(res => {
        this.issueTypeSelect = res.data;
      });
    },
    updateFormData () {
      this.$http.issueOne(this.$route.query.issueId)
        .then(res => {
          this.formData = res.data
        });
    },
    beforeUpload (file) {
      let prefix = file.name.split('.')[1].toLowerCase()
      let extension = (prefix === 'doc' || prefix === 'docx')
      const isLt2M = file.size / 1024 / 1024 < 5     //这里做文件大小限制
      if (!extension) {
        this.$message({
          message: '上传文件只能是 doc、docx格式!',
          type: 'warning'
        });
      }
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 5MB!',
          type: 'warning'
        });
      }
      return extension && isLt2M
    },
    uploadSuccess (res, file, fileList) {
      if (res.code != -1) {
        this.formData.issueContent += res.html;
        this.$msg('文件上传成功', 'success')
      } else {
        this.$msg(res.msg, 'error')
      }
    },
    uploadErr (err, file, fileList) {
      this.$msg(err.toString(), 'error')
    },
    save (params) {
      //vxe-table的form与elementui的form效验不一样，vxe-table的valid要为false，elementui要为true
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.formData.issueContent = this.$refs.wangEditorRef.getContent()
          let form = this.formData
          if (!form.id) {
            this.$http.issueAdd(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              if (params != 'continue') {
                this.$router.push({ path: '/question/issueManage' })
              }
            });
          } else {
            this.$http.issueEdit(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              if (params != 'continue') {
                this.$router.push({ path: '/question/issueManage' })
              }
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.$resetForm('formRef');
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.singleChoice {
  .cardTitle {
    font-size: 16px;
    color: $base-color-default;
  }
  .suffixIcon {
    font-size: 22px;
    margin-top: 6px;
    cursor: pointer;
  }
  .issueLevel ::v-deep {
    .el-rate {
      line-height: 45px;
    }
  }
  .issueBox ::v-deep {
    margin-bottom: 5px;
    p {
      margin: 3px;
    }
  }
  .issueContent ::v-deep {
    line-height: 20px;
    .el-upload__tip {
      margin-top: 0px;
    }
    .el-upload-list__item {
      margin-top: 0px;
    }
    .el-upload-list__item:first-child {
      margin-top: 0px;
    }
  }
  // .vxe-modal--wrapper.type--alert .vxe-modal--body, .vxe-modal--wrapper.type--alert .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--confirm .vxe-modal--body, .vxe-modal--wrapper.type--confirm .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--modal .vxe-modal--body, .vxe-modal--wrapper.type--modal .vxe-modal--body .vxe-modal--content {
  //   overflow: hidden!important;
  // }
}
</style>
