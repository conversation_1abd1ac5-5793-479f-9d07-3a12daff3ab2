<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="议题名称" prop="issueName">
        <el-input placeholder="请输入议题名称" v-model="formSearch.issueName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
      </el-form-item>
      <el-form-item label="所属工艺" prop="processTypeId">
        <el-select v-model="formSearch.processTypeId" placeholder="所属工艺" clearable size="small">
          <el-option v-for="dict in processTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增议题</el-button>
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>
      <template v-slot:processTypeSlot="{ row }">
        <el-tag>{{row.processTypeName}}</el-tag>
      </template>
      <!-- 展开栏内容 -->
      <template #conExpand="{ row }">
        <div class="expandBox">
          <div v-html="row.issueContent"></div>
        </div>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <!-- <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button> -->
        <el-button type="text" icon="el-icon-edit" @click="$router.push({path:'/question/issueOperator',query:{issueId:row.id}})">修改</el-button>
        <el-button type="text" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>
    <!-- <multiple-choice ref="singleChoiceRef"></multiple-choice> -->
  </div>
</template>

<script>
export default {
  name: "IssueManage",
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'expand', width: 60, slots: { content: 'conExpand' } },
        { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'issueName', title: '议题名称' },
        { field: 'processTypeId', title: '所属工艺', slots: { default: 'processTypeSlot' } },
        { field: 'status', title: '状态' },
        { sortable: true, field: 'createBy', title: '创建人' },
        { sortable: true, field: 'createTime', title: '创建时间' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      loading: false,
      formSearch: {
        issueName: undefined,
        processTypeId: undefined,
        page: 1,
        pageSize: 10
      },
      processTypeOptions: [],
      isExpand: false,
    };
  },
  created () {
    this.getProcessFlow()
    this.getData();
  },
  activated () {
    this.getData();
  },
  methods: {
    // 初始化
    getData () {
      this.loading = true;
      this.$http.issueList(this.formSearch)
        .then(res => {
          this.tableData = res.data
          this.tablePage.total = res.total;
          //分页后判断是否展开或折叠
          this.$nextTick(() => {
            if (this.isExpand) {
              this.$refs.xGrid.setAllRowExpand(true)
            } else {
              this.$refs.xGrid.clearRowExpand()
            }
          })
          this.loading = false;
        });
    },
    getProcessFlow () {
      this.$http.getDictList({ dictField: 'processFlow' }).then(res => {
        this.processTypeOptions = res.data;
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    toggleExpandAll () {
      this.isExpand = !this.isExpand
      if (this.isExpand) {
        this.$refs.xGrid.setAllRowExpand(true)
      } else {
        this.$refs.xGrid.clearRowExpand()
      }
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {

      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    handleAdd () {
      this.$router.push({ path: '/question/issueOperator' })
    },
    handleEdit (row) {
      this.$router.push({ path: '/question/issueOperator', query: { issueId: row.id } })
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.issueDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.expandBox {
  font-size: 16px;
  // padding-left: 30px;
  .title {
    display: inline-block;
    vertical-align: top;
    margin: 3px 0;
  }
  .qs_contentBox {
    display: inline-block;
    margin-left: 10px;
  }
  .analyBox {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep p {
    margin: 3px 0;
    img {
      vertical-align: middle;
    }
  }
}
.questionBox ::v-deep {
  margin-bottom: 5px;
  .el-button {
    vertical-align: top;
  }
  p {
    margin: 3px 0;
  }
}
</style>
