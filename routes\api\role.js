var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/RoleService");

router.post('/add', tryAsyncErrors(Service.create))
// router.get('/list',tryAsyncErrors(Service.list))
router.post('/list', tryAsyncErrors(Service.list))
router.get('/roleList', tryAsyncErrors(Service.roleList))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:roleId', tryAsyncErrors(Service.delete))

module.exports = router;
