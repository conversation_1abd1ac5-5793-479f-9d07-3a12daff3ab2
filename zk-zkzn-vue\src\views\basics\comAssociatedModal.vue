<template>
  <div>
    <vxe-modal v-model="dialogVisible" show-zoom resize show-footer width="1000" height="700" title="关联学员">
      <template #default>
        <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>

          <el-form-item label="用户名称" prop="userName">
            <el-input placeholder="请输入用户名称" v-model="formSearch.userName" clearable @keyup.enter.native="getData('formSearch')"> </el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <vxe-grid ref="xGrid" max-height="500px" row-id="id" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" @checkbox-all="selectChangeEvent" @checkbox-change="selectChangeEvent" :checkbox-config="{reserve:true}" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
          <template #toolbar_left>
            <el-button type="primary" plain size="mini">已勾选数量：{{chooseData?chooseData.length:0}}</el-button>
          </template>
          <!-- 用户的头像 -->
          <template v-slot:avatarSlot="{ row }">
            <el-image style="width: 48px; height: 48px" :src="row.avatar?$SOURCEURL+row.avatar:require('@/assets/img/avater.jpg')">
            </el-image>
          </template>
        </vxe-grid>
      </template>
      <template #footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'checkbox', width: 60 },
        { type: 'seq', width: 60, title: '序号' },
        { field: 'avatar', title: '用户头像', slots: { default: 'avatarSlot' } },
        { sortable: true, field: 'userName', title: '用户名称' },
        { sortable: true, field: 'nickName', title: '用户昵称' },
        // { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center' },
      ],
      tableData: [],
      formSearch: {
        userName: '',
        page: 1,
        pageSize: 10,
        gradeId: undefined,
      },
      loading: false,
      dialogVisible: false,
      chooseData: [],
      processTypeOptions: [],
    };
  },
  methods: {
    open (val) {
      this.chooseData = []
      this.formSearch.gradeId = val.gradeId
      this.dialogVisible = true
      this.tablePage.currentPage = 1
      this.formSearch.page = 1      
      this.getData();
    },
    getData () {
      this.loading = true;
      if (!this.formSearch.gradeId) {
        return this.$msg('参数缺失，请刷新界面后再试', 'warning')
      }      
      this.$http.gradeNotAssociatedStudentList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        console.log(this.tableData, 'this.tableData');
        this.loading = false;
      });
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage;
      this.tablePage.pageSize = pageSize;
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData();
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    selectChangeEvent ({ checked, records }) {
      this.chooseData = this.$refs.xGrid.getCheckboxReserveRecords()
      if (records.length > 0) {
        this.chooseData = this.chooseData.concat(records)
      }
    },
    save () {
      let formData = this.chooseData.map(item => {
        return {
          gradeId: this.formSearch.gradeId,
          userId: item.id || null,
        }
      })
      this.$http.addAssociationStudent(formData).then(res => {
        this.close()
        this.$parent.getData();
        this.$msg('操作成功', 'success')
      });
    },
    close () {
      this.$refs.xGrid.clearCheckboxReserve()
      this.dialogVisible = false;
    },
  }
};
</script>

<style lang="scss" scoped>
</style>
