const { Log, User, $LikeWhere, sequelize: $seq } = require('../models')
const fields = Object.keys(Log.tableAttributes)

class LogService {
  static async loginLogList (req, res) {
    let { page, pageSize } = req.query
    if (!page || !pageSize) {
      return res.sendSuccess({ code: -1, msg: "分页参数异常或缺失" })
    }
    let where = $LikeWhere(req, res, fields, { key: 'operType', value: { $eq: '登录' } })
    let { rows, count } = await Log.findAndCountAll({
      attributes: {
        include: [
          [$seq.col("User.userName"), 'userName'],
          [$seq.col("User.unit"), 'unit'],
          [$seq.col("User.avatar"), 'avatar'],
          [$seq.literal("(SELECT COUNT(1) FROM zk_user_serial u WHERE u.userId = `Log`.`userId`)"), 'userSerialNum'],
          [$seq.literal("(SELECT COUNT(DISTINCT serialNumber) FROM zk_user_serial u WHERE u.userId = `Log`.`userId`)"), 'serialNumberCount']
        ]
      },
      include: [
        {
          model: User,
          attributes: [],
        },
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['operTime', 'desc']]
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }
  static async operLogList (req, res) {
    let { page, pageSize } = req.query
    if (!page || !pageSize) {
      return res.sendSuccess({ code: -1, msg: "分页参数异常或缺失" })
    }
    let where = $LikeWhere(req, res, fields, { key: 'operType', value: { $ne: '登录' } })
    let { rows, count } = await Log.findAndCountAll({
      attributes: {
        include: [
          [$seq.col("User.userName"), 'userName'],
          [$seq.col("User.unit"), 'unit'],
          [$seq.col("User.avatar"), 'avatar'],
          [$seq.literal("(SELECT COUNT(1) FROM zk_user_serial u WHERE u.userId = Log.userId)"), 'userSerialNum'],
          [$seq.literal("(SELECT COUNT(DISTINCT serialNumber) FROM zk_user_serial u WHERE u.userId = `Log`.`userId`)"), 'serialNumberCount']
        ]
      },
      include: [
        {
          model: User,
          attributes: [],
        },
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['operTime', 'desc']]
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }
}

module.exports = LogService
