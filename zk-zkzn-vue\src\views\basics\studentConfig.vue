<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="届/年级名称" required>
        <el-select v-model="formSearch.gradeId" @change="getData('formSearch')" :disabled="disabled" placeholder="请先选择流程记录" clearable size="small">
          <el-option v-for="dict in gradeOption" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="学员名称" prop="userName">
        <el-input placeholder="请输入学员名称" v-model="formSearch.userName" clearable @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid ref="xGrid" :columns="tableColumn" :data="tableData" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" :pager-config="Object.assign({currentPage:formSearch.page,total:tablePage.total},formSearch)" @page-change="handlePageChange" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['grade/addAssociationStudent']" plain icon="el-icon-plus" @click="associatedTopic" size="mini">关联学员</el-button>
      </template>
      <!-- 用户的头像 -->
      <template v-slot:avatarSlot="{ row }">
        <el-image style="width: 48px; height: 48px" :src="row.avatar?$SOURCEURL+row.avatar:require('@/assets/img/avater.jpg')" :preview-src-list="srcList">
        </el-image>
      </template>
      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['grade/studentDel']" icon="el-icon-delete" @click="handleDel(row)">移除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.gradeRules" title-width="120" title-align="right"></vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>

    <Com-Associated-Modal ref="comAssociatedRef"></Com-Associated-Modal>

  </div>
</template>

<script>
import ComAssociatedModal from './comAssociatedModal.vue'
export default {
  name: "StudentConfig",
  components: {
    ComAssociatedModal
  },
  data () {
    return {
      srcList: [],
      modalTitle: '新增记录',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { field: 'gradeName', title: '届/年级' },
        { field: 'avatar', title: '用户头像', slots: { default: 'avatarSlot' } },
        { sortable: true, field: 'userName', title: '用户名称' },
        { sortable: true, field: 'nickName', title: '用户昵称' },
        { title: '操作', width: 220, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        gradeName: '',
        remark: '',
      },
      formItem: [
        { field: 'gradeName', title: '年级名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入名称' } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      formSearch: {
        gradeId: '',
        userName: '',
        page: 1,
        pageSize: 10
      },
      gradeOption: [],
      disabled: false
    };
  },
  created () {
    this.getGradeOption();
    if (this.$route.query.gradeId) {
      this.disabled = true
      this.formSearch.gradeId = Number(this.$route.query.gradeId)
    } else {
      this.disabled = false
      this.formSearch.gradeId = ''
    }
    this.getData()
  },
  activated () {
    this.getGradeOption();
    if (this.$route.query.gradeId) {
      this.disabled = true
      this.formSearch.gradeId = Number(this.$route.query.gradeId)
    } else {
      this.disabled = false
      this.formSearch.gradeId = ''
    }
    this.getData()
  },
  methods: {
    getData (params) {
      //年级列表
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.gradeStudentList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    getGradeOption () {
      this.$http.gradeOption().then(res => {
        this.gradeOption = res.data;
      });
    },
    associatedTopic () {
      if (!this.formSearch.gradeId) return this.$msg('请先选择届/年级名称', 'warning')
      this.$refs.comAssociatedRef.open({
        gradeId: this.formSearch.gradeId,
      });
    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          if (this.modalTitle == '新增年级') {
            this.$http.gradeAdd(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.gradeEdit(this.formData).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.menuExpand = false
      this.menuNodeAll = false
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    handleStudentConfig (row) {
      console.log(row, 'row');
    },
    handleAdd () {
      this.modalTitle = '新增年级';
      this.modalVisible = true;
      this.formData = {
        gradeName: '',
        classId: undefined,
        status: 1,
        remark: '',
      };
    },
    handleEdit (row) {
      this.modalTitle = '编辑年级';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('请确认是否删除此数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.gradeStudentDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}
</style>
