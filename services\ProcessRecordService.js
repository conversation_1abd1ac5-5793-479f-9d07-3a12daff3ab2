const { Record, ProcessRecord, Grade<PERSON>ser, Question, QuestionAnswer, VirtualData, VirtualProcess, Issue, sequelize: $seq } = require('../models')
const config = require('../config/config.js')["student_role"]

class ProcessRecordService {
  static async create (req, res) {
    req.body.forEach(item => {
      item['createBy'] = req.user.userName
      item['createById'] = req.user.userId
    })
    let data = await ProcessRecord.bulkCreate(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await ProcessRecord.update(req.body, {
      where: {
        processRecordId: req.body.processRecordId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { processRecordId } = req.params
    let data = await ProcessRecord.destroy({
      where: {
        processRecordId
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  //改版后的关联问题接口
  static async associationQuestion (req, res) {
    let { page, pageSize } = req.body;
    if (!req.body.recordId) return res.sendSuccess({ code: -1, msg: '记录名称id参数缺失' })
    if (!req.body.virtualId) return res.sendSuccess({ code: -1, msg: 'virtualId参数缺失' })
    if (!page || !pageSize) return res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    let where = {
      virtualId: req.body.virtualId,
      recordId: req.body.recordId,
    }
    if (req.body.questionType) {
      where['Question.questionType'] = $seq.literal('`Question`.`questionType` LIKE "%' + req.body.questionType + '%"')
    }
    if (req.body.questionContent) {
      where['Question.questionContent'] = $seq.literal('`Question`.`questionContent` LIKE "%' + req.body.questionContent + '%"')
    }
    let { rows, count } = await ProcessRecord.findAndCountAll({
      attributes: {
        include: [
          [$seq.col('Question.questionContent'), 'questionContent'],
          [$seq.col('Question.questionType'), 'questionType'],
          [$seq.col('Question.classType'), 'classType'],
          [$seq.col('Question.questionLevel'), 'questionLevel'],
          [$seq.col('Question.questionAnalysis'), 'questionAnalysis']
        ]
      },
      include: [
        {
          model: Question,
          required: true,
          attributes: [],
        },
        {
          model: QuestionAnswer,
          as: 'questionAnswers',
          attributes: {
            include: [['answerId', 'questionAnswerId']]
            // exclude:['isRight']
          },
        }
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  //改版后的关联议题接口
  static async associationIssue (req, res) {
    let { page, pageSize } = req.body;
    if (!req.body.virtualId) return res.sendSuccess({ code: -1, msg: 'virtualId参数缺失' })
    if (!page || !pageSize) return res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    let where = {
      virtualId: req.body.virtualId
    }
    if (req.body.issueName) {
      where['Issue.issueName'] = $seq.literal('`Issue`.`issueName` LIKE "%' + req.body.issueName + '%"')
    }
    let { rows, count } = await ProcessRecord.findAndCountAll({
      attributes: {
        include: [
          [$seq.col('Issue.issueContent'), 'issueContent'],
          [$seq.col('Issue.issueName'), 'issueName'],
          [$seq.col('Issue.remark'), 'remark'],
          [$seq.col('Issue.processTypeId'), 'processTypeId'],
          [$seq.col('Issue.id'), 'issueId'],
        ]
      },
      include: [
        {
          model: Issue,
          required: true,
          attributes: [],
        },
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  static async queryQuestionById (req, res) {
    // if (!req.body.virtualId) return res.sendSuccess({ code: -1, msg: 'virtualId参数缺失' })
    let recordId = null
    let record = null
    if (!req.user) {
      record = await Record.findOne({
        where: {
          userId: 1,
          defaultStatus: 1
        },
        raw: true
      })
    } else {
      if (req.user.roleId != config.roleId) {
        record = await Record.findOne({
          where: {
            userId: req.user.userId,
            defaultStatus: 1
          },
          raw: true
        })
      } else {
        let grade = await GradeUser.findOne({
          where: {
            userId: req.user.userId
          }
        })
        if (!grade) return res.sendSuccess({ code: -1, msg: '此用户未关联届/年级' })
        record = await Record.findOne({
          where: {
            gradeId: grade.gradeId,
            status: 1
          },
          raw: true
        })
      }
    }
    if (!record) return res.sendSuccess({ code: -1, msg: '未查询到相关流程题目配置' })
    recordId = record.id
    let data
    let codeall = []
    if (req.body.virtualId) {
      //先查询所有子节点code、
      let origin = await VirtualData.findOne({
        where: {
          id: req.body.virtualId
        },
        raw: true
      })
      codeall = [origin.code]

      await ProcessRecordService.getDeepCode(req.body.virtualId, codeall)
      data = await ProcessRecord.findAll({
        attributes: {
          include: [
            [$seq.col('Question.questionContent'), 'questionContent'],
            [$seq.col('Question.questionType'), 'questionType'],
            // [$seq.col('Question.classType'), 'classType'],
            [$seq.col('Question.questionLevel'), 'level'],
            [$seq.col('Question.questionAnalysis'), 'analysis']
          ],
          exclude: ['createTime', 'updateTime', 'radioData', 'checkboxData', 'createById']
        },
        include: [
          {
            model: Question,
            required: true,
            attributes: [],
          },
          {
            model: QuestionAnswer,
            as: 'questionAnswers',
            attributes: {
              include: [['answerId', 'questionAnswerId']],
              exclude: ['answerId', 'questionId']
            },
          }
        ],
        where: {
          code: req.body.virtualId ? {
            $in: codeall
          } : { $ne: null },
          questionId: {
            $ne: null
          },
          recordId
        }
      })
    } else {
      data = await ProcessRecord.findAll({
        attributes: {
          include: [
            [$seq.col('Question.questionContent'), 'questionContent'],
            [$seq.col('Question.questionType'), 'questionType'],
            // [$seq.col('Question.classType'), 'classType'],
            [$seq.col('Question.questionLevel'), 'level'],
            [$seq.col('Question.questionAnalysis'), 'analysis']
          ],
          exclude: ['createTime', 'updateTime', 'radioData', 'checkboxData', 'createById']
        },
        include: [
          {
            model: Question,
            required: true,
            attributes: [],
          },
          {
            model: QuestionAnswer,
            as: 'questionAnswers',
            attributes: {
              include: [['answerId', 'questionAnswerId']],
              exclude: ['answerId', 'questionId']
            },
          }
        ],
        where: {
          virtualId: { $ne: null },
          questionId: {
            $ne: null
          },
          recordId
        }
      })
    }

    data.forEach(item => {
      item.dataValues['processRecord'] = {
        questionId: item.questionId,
        virtualprocessId: item.virtualprocessId,
        deviceId: item.deviceId,
        deviceOperationId: item.deviceOperationId
      }
    })

    res.sendSuccess({ code: 200, data: { total: data.length, list: data } })
  }

  static async getDeepCode (id, arr) {
    let codeall = await VirtualData.findAll({
      where: {
        pId: id
      },
      raw: true
    })
    if (codeall && codeall.length > 0) {
      for (let i = 0; i < codeall.length; i++) {
        arr.push(codeall[i].code)
        ProcessRecordService.getDeepCode(codeall[i].virtualId, arr)
      }
    }
  }

  static async queryIssueById (req, res) {
    if (!req.body.virtualId) return res.sendSuccess({ code: -1, msg: 'virtualId参数缺失' })
    let recordId = null
    if (!req.user) {
      let record = await Record.findOne({
        where: {
          userId: 1,
          defaultStatus: 1
        },
        raw: true
      })
      recordId = record.id
    } else {
      if (req.user.roleId != config.roleId) {
        let record = await Record.findOne({
          where: {
            userId: req.user.userId,
            defaultStatus: 1
          },
          raw: true
        })
        recordId = record.id
      } else {
        let grade = await GradeUser.findOne({
          where: {
            userId: req.user.userId
          }
        })
        let record = await Record.findOne({
          where: {
            gradeId: grade.gradeId,
            status: 1
          },
          raw: true
        })
        recordId = record.id
      }
    }
    let data = await ProcessRecord.findAll({
      attributes: {
        include: [
          [$seq.col('Issue.issueContent'), 'issueContent'],
          [$seq.col('Issue.issueName'), 'issueName'],
          [$seq.col('Issue.remark'), 'remark'],
          [$seq.col('Issue.processTypeId'), 'processTypeId'],
          [$seq.col('Issue->VirtualProcess.vrProName'), 'processTypeName'],
        ]
      },
      include: [
        {
          model: Issue,
          // required: true,
          attributes: [],
          include: [
            {
              model: VirtualProcess,
              // required: true,
              attributes: []
            },
          ],
        },
      ],
      where: {
        virtualId: {
          $like: `${req.body.virtualId}%`
        },
        issueId: {
          $ne: null
        },
        recordId
      }
    })
    res.sendSuccess({ code: 200, data })
  }

  //虚拟程序访问请求---问题列表
  static async queryList (req, res) {
    let { virtualprocessId, deviceId, deviceOperationId, questionId, page, pageSize } = req.body
    let where = {
    }
    if (virtualprocessId) {
      where['virtualprocessId'] = virtualprocessId
    }
    if (deviceId) {
      where['deviceId'] = deviceId
    }
    if (deviceOperationId) {
      where['deviceOperationId'] = deviceOperationId
    }
    if (questionId) {
      where['questionId'] = questionId
    }
    let { rows, count } = await ProcessRecord.findAndCountAll({
      include: [
        {
          model: Question,
          required: true,
          attributes: [],
        },
        {
          model: QuestionAnswer,
          as: 'questionAnswers',
          attributes: {
            include: [['answerId', 'questionAnswerId']]
            // exclude:['isRight']
          },
        }
      ],
      attributes: {
        include: [
          [$seq.col('Question.questionContent'), 'questionContent'],
          [$seq.col('Question.questionType'), 'questionType'],
          [$seq.col('Question.classType'), 'classType'],
          [$seq.col('Question.questionLevel'), 'questionLevel'],
          [$seq.col('Question.questionLevel'), 'level'],
          [$seq.col('Question.questionAnalysis'), 'analysis'],
          [$seq.col('Question.questionAnalysis'), 'questionAnalysis']
        ]
      },
      where,
      distinct: true,
      // raw:true
    })
    res.sendSuccess({ code: 200, data: { list: rows, total: count }, msg: '成功' })
  }

  static async list (req, res) {
    let { deviceId, deviceOperationId, recordId, questionContent = '', questionType = '', page, pageSize } = req.body
    let where = {}
    if (questionType) {
      where['questionType'] = {
        $like: `%${questionType}%`,
      }
    }
    if (questionContent) {
      where['questionContent'] = {
        $like: `%${questionContent}%`,
      }
    }
    if (!deviceOperationId && !deviceId) {
      let { rows, count } = await ProcessRecord.findAndCountAll({
        include: [
          {
            model: QuestionAnswer,
            as: 'questionAnswers',
            // attributes: []
          },
        ],
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
        // raw:true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let twoWhere = { recordId }
      if (deviceId) {
        twoWhere['deviceId'] = deviceId
      }
      if (deviceOperationId) {
        twoWhere['deviceOperationId'] = deviceOperationId
      }
      if (!page && !pageSize) {
        let data = await ProcessRecord.findAll({
          include: [
            {
              model: Question,
              required: true,
              attributes: [],
            },
            {
              model: QuestionAnswer,
              as: 'questionAnswers',
              attributes: {
                exclude: ['isRight']
              },
            }
          ],
          attributes: {
            include: [
              [$seq.col('Question.questionContent'), 'questionContent'],
              [$seq.col('Question.questionType'), 'questionType'],
              [$seq.col('Question.classType'), 'classType'],
              [$seq.col('Question.questionLevel'), 'questionLevel'],
              [$seq.col('Question.questionLevel'), 'level'],
              [$seq.col('Question.questionAnalysis'), 'questionAnalysis'],
              [$seq.col('Question.questionAnalysis'), 'analysis']
            ]
          },
          where: twoWhere,
          // raw:true
        })
        res.sendSuccess({ code: 200, data })
      } else {
        let { rows, count } = await ProcessRecord.findAndCountAll({
          include: [
            {
              model: Question,
              required: true,
              attributes: [],
            },
            {
              model: QuestionAnswer,
              as: 'questionAnswers',
            }
          ],
          attributes: {
            include: [
              [$seq.col('Question.questionContent'), 'questionContent'],
              [$seq.col('Question.questionType'), 'questionType'],
              [$seq.col('Question.classType'), 'classType'],
              [$seq.col('Question.questionLevel'), 'questionLevel'],
              [$seq.col('Question.questionAnalysis'), 'questionAnalysis']
            ]
          },
          where: twoWhere,
          offset: (page - 1) * pageSize,
          limit: pageSize,
          distinct: true,
          // raw:true
        })
        res.sendSuccess({ code: 200, data: rows, total: count })
      }
    }
  }

  static async issueList (req, res) {
    let { deviceId, recordId, issueName = '', processTypeId, page, pageSize } = req.body
    if (!deviceId) {
      let { rows, count } = await ProcessRecord.findAndCountAll({
        // where: {
        //   questionType: {
        //     $like: `%${questionType}%`,
        //   },
        //   questionContent: {
        //     $like: `%${questionContent}%`,
        //   },
        // },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
        // raw:true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      if (!page && !pageSize) {
        let data = await ProcessRecord.findAll({
          include: [
            {
              model: Issue,
              required: true,
              attributes: [
                // include: [[$seq.col('VirtualProcess.vrProName'), 'processTypeName']]
              ],
              include: [
                {
                  model: VirtualProcess,
                  required: true,
                  attributes: []
                },
              ],
            },
          ],
          attributes: {
            include: [
              [$seq.col('Issue.issueContent'), 'issueContent'],
              [$seq.col('Issue.issueName'), 'issueName'],
              [$seq.col('Issue.remark'), 'remark'],
              [$seq.col('Issue.processTypeId'), 'processTypeId'],
              [$seq.col('Issue.id'), 'issueId'],
              [$seq.col('Issue->VirtualProcess.vrProName'), 'processTypeName'],
            ]
          },
          where: {
            deviceId,
            recordId
          },
          // raw:true
        })
        res.sendSuccess({ code: 200, data })
      } else {
        let { rows, count } = await ProcessRecord.findAndCountAll({
          include: [
            {
              model: Issue,
              required: true,
              attributes: [],
              include: [
                {
                  model: VirtualProcess,
                  required: true,
                  attributes: []
                },
              ],
            }
          ],
          attributes: {
            include: [
              [$seq.col('Issue.issueContent'), 'issueContent'],
              [$seq.col('Issue.issueName'), 'issueName'],
              [$seq.col('Issue.remark'), 'remark'],
              [$seq.col('Issue.processTypeId'), 'processTypeId'],
              [$seq.col('Issue.id'), 'issueId'],
              [$seq.col('Issue->VirtualProcess.vrProName'), 'processTypeName'],
            ]
          },
          where: {
            deviceId,
            recordId
          },
          offset: (page - 1) * pageSize,
          limit: pageSize,
          distinct: true,
          // raw:true
        })
        res.sendSuccess({ code: 200, data: rows, total: count })
      }
    }
  }
}

module.exports = ProcessRecordService
