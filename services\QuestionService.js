const { Question, QuestionAnswer, ProcessRecord, sequelize: $seq } = require('../models')
class QuestionService {
  static async create (req, res) {
    // let checkDate = await Question.findOne({
    //   where: { userName: userInfo.userName },
    // })
    // if (checkDate) return res.sendSuccess({ code: -1, msg: '此用户名已存在' })
    let data = await Question.create(req.body, {
      include: [{ model: QuestionAnswer, as: 'questionAnswers' }],
    })
    let trueAnswer = ''
    data.questionAnswers.forEach(item => {
      if (item.isRight == 1) {
        trueAnswer += item.answerId + ','
      }
    })
    await Question.update({ trueAnswer: trueAnswer.substring(0, trueAnswer.length - 1) }, {
      where: {
        questionId: data.questionId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    if (!req.body.questionId) {
      return res.sendSuccess({ code: -1, msg: '数据id缺失' })
    }
    let data = await Question.update(req.body, {
      where: {
        questionId: req.body.questionId
      }
    })
    await QuestionAnswer.destroy({ where: { questionId: req.body.questionId } })
    /* 2022.1.11 czk发现此处有bug，编辑时，如果新增了一个子项，由于子项没有questionId，导致异常。
    可前端添加也可后端添加，这里后端添加id */
    // await QuestionAnswer.bulkCreate(req.body.questionAnswers)
    req.body.questionAnswers.map(item => [
      item.questionId = req.body.questionId
    ])
    await QuestionAnswer.bulkCreate(req.body.questionAnswers)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { questionId } = req.params
    let data = await Question.destroy({
      where: {
        questionId
      },
    })
    await QuestionAnswer.destroy({ where: { questionId } })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async getOne (req, res) {
    const { questionId } = req.params
    let data = await Question.findByPk(questionId, {
      include: [
        {
          model: QuestionAnswer,
          as: 'questionAnswers',
        },
      ]
    })
    res.sendSuccess({ code: 200, data, msg: '获取成功' })
  }
  static async list (req, res) {
    let { virtualId, recordId, page, pageSize, questionType = '', questionContent = '' } = req.body
    if (!virtualId) {
      let { rows, count } = await Question.findAndCountAll({
        include: [
          {
            model: QuestionAnswer,
            as: 'questionAnswers',
            // attributes: []
          },
        ],
        where: {
          questionType: {
            $like: `%${questionType}%`,
          },
          questionContent: {
            $like: `%${questionContent}%`,
          },
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order: [
          ['createTime', 'DESC'],
        ],
        distinct: true,
        // raw:true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let ids = await ProcessRecord.findAll({
        attributes: ['questionId'],
        where: {
          virtualId,
          recordId
        },
        raw: true
      })
      ids = ids.map(item => { return item.questionId })
      let { rows, count } = await Question.findAndCountAll({
        include: [
          {
            model: QuestionAnswer,
            as: 'questionAnswers',
            // attributes: []
          },
        ],
        where: {
          questionId: {
            $notIn: ids,
          },
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
      })

      res.sendSuccess({ code: 200, data: rows, total: count })
    }
  }
}

module.exports = QuestionService
