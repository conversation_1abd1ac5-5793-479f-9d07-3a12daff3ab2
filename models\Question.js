'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Question extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsToMany(models.Role, {as:'roles', through: sequelize.models.UserRole,foreignKey:'roleId',otherKey:'userId' })
      this.hasMany(models.QuestionAnswer, { as: 'questionAnswers', foreignKey: 'questionId' })

      this.belongsTo(models.UserAnswer, { foreignKey: 'questionId', constraints: false })

    }
  }
  Question.init(
    {
      // level:{
      //   type: DataTypes.VIRTUAL,
      //   get() {
      //     return 1
      //   },
      // },
      questionId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "题目ID"
      },
      questionType: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "题目类型"
      },
      classType: {
        type: DataTypes.STRING,
        allowNull: true,
        // defaultValue: null,
        comment: "所属工艺流程"
      },
      questionLevel: {
        type: DataTypes.INTEGER(1),
        allowNull: true,
        defaultValue: 1,
        comment: "难度等级"
      },
      questionContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "题目内容"
      },
      trueAnswer:{
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "答案id"
      },
      questionAnalysis: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "题目分析"
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Question',
      tableName: 'zk_question',
      comment:'问题表'
    }
  )
  return Question
}
