const { sequelize: $seq } = require('../models')

//try catch封装报错
function transactionTryAsyncErrors (handler) {
  return async function (req, res, next) {
    const transaction = await $seq.transaction()
    try {
      await handler(req, res, next, transaction)
      await transaction.commit()
    } catch (error) {
      await transaction.rollback()
      next(error);
    }
  };
}

module.exports = {
  transactionTryAsyncErrors
};