'use strict'
const { Model } = require('sequelize')
const { customAlphabet } = require('nanoid')
const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)
module.exports = (sequelize, DataTypes) => {
  class VirtualData extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.hasMany(models.VirtualData, { as: 'children', foreignKey: 'pId', constraints: false })
      this.belongsTo(models.PathPrefix, { foreignKey: 'prefixId', constraints: false })
      this.belongsTo(models.PathWeb, { foreignKey: 'webUrlId', constraints: false })
      this.belongsTo(models.OperType, { foreignKey: 'operTypeId', constraints: false })
      this.belongsTo(models.VirtualCate, { foreignKey: 'virtualCateId', constraints: false })
      this.hasMany(models.Courseware, { as: 'coursewares', foreignKey: 'processTypeId', constraints: false })
      this.hasMany(models.Issue, { as: 'issues', foreignKey: 'processTypeId', constraints: false })
    }
  }
  VirtualData.init(
    {
      code: {
        // autoIncrement: true,
        type: DataTypes.CHAR,
        allowNull: false,
        primaryKey: true,
        defaultValue: () => 'VR_' + nanoid(),
        comment: "唯一code"
      },
      id: {
        // autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        // primaryKey: true,
        comment: "节点ID"
      },
      pId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue: 0,
        comment: "父节点ID"
      },
      virtualId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "进程ID"
      },
      virtualParentId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "父进程ID"
      },
      virtualName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "进程名称"
      },
      assetName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "资源名称"
      },
      operName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "操作名称"
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "简介"
      },
      processName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "工艺名称"
      },
      processDesc: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "工艺简介"
      },
      sortBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "显示顺序"
      },
      level: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "层级"
      },      
      /* 2022.1.13 czk根据刘工要求，虚拟课程管理的树形结构应该是虚拟流程管理的树形数据，按照刘工提供的3个文件生成。
      原吴兴城又单独新建了一张表，不知道为什么？  
      所以现在添加部分字段，对应需要传递的数据信息，数据图片等
      */
      prefixId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "路径前缀id"
      },
      webUrlId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "分类id"
      },
      operTypeId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "操作类型id"
      },
      virtualCateId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "分类id"
      },
      // virtualCateIds: {
      //   type: DataTypes.STRING,
      //   allowNull: true,
      //   comment: "分类ids"
      // },
      type: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "类型"
      },
      pic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "图片地址"
      },
      videoSrc: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "视频地址"
      },
      linkSrc: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "",
        comment: "外链参数"
      },
      webParams: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "",
        comment: "web外链参数"
      },
      isPlatform: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: '1',
        comment: "平台（1.web，2.window）"
      },
      webSrc: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "web端访问地址"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "1",
        comment: "菜单状态（1正常 2停用）"
      },
      isHostShow: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "是否主目录显示（1.是，0.否）"
      },
      isExecutable: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "是否可执行资源（1.是，0.否）"
      },
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建者id"
      },
      updateById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建者id"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualData',
      tableName: 'zk_virtual_data',
      comment: '仿真数据表（改版后）'
    }
  )
  return VirtualData
}
