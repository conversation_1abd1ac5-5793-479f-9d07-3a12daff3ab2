<template>
  <div>
    <vxe-modal v-model="modalVisible" width="900" height="690" :zIndex="899999999999" show-footer destroyOnClose title="富文本编辑器">
      <!-- 编辑器 -->
      <WangEditor ref="wangEditorRef" :text="content" />
      <template #footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="contentSave">保存</el-button>
          <el-button @click="contentClose">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
import WangEditor from '@/components/WangEditor'

export default {
  name: 'DialogEditor',
  components: { WangEditor },
  data () {
    return {
      content: '',
      modalVisible: false, // 控制对话框显示与隐藏      
    }
  },
  methods: {
    contentSave () {
      this.$emit("getContent", this.$refs.wangEditorRef.getContent())
      this.contentClose()
    },
    contentClose () {
      this.content = ""
      this.modalVisible = false
    }
  },
}
</script>
