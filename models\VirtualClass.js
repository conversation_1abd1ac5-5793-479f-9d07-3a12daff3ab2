'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class VirtualClass extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // this.belongsToMany(models.Role, {as:'roles', through: sequelize.models.UserRole,foreignKey:'roleId',otherKey:'userId' })
    }
  }
  VirtualClass.init(
    {
      homePageId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "首页ID"
      },
      homePageName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "首页名称"
      },
      homePageNumber: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: '',
        comment: "首页编号"
      },
      homePageInfo: {
        type: DataTypes.STRING(255),
        allowNull: true,
        defaultValue: "",
        comment: "首页介绍"
      },
      homePageType: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "首页类型"
      },
      parentId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue: 0,
        comment: "父菜单ID"
      },
      levelCode: {
        type: DataTypes.STRING(200),
        allowNull: false,
        // defaultValue: 0,
        comment: "层次码"
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },            
      homePageSrc: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: "图片路径"
      },      
      linkSrc: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "0",
        comment: "外链路径"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0正常 1停用）"
      },      
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualClass',
      tableName: 'sys_home_page',
      comment:'未知，不知道还有没有用的表'
    }
  )
  return VirtualClass
}
