<template>
  <div class="formEditor">
    <v-form-designer ref="vfDesigner" :designer-config="designerConfig">
      <!-- 自定义按钮插槽演示 -->
      <template #customToolButtons>
        <el-button type="text" @click="saveFormJson"><i class="el-icon-finished" />保存</el-button>
      </template>
    </v-form-designer>
    <div class="tool">
      <el-button type="primary" size="medium" @click="saveFormJson">保 存</el-button>
      <el-button size="medium" @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "questionnaireEditor",
  data () {
    return {
      fieldListApi: {
        URL: 'https://www.fastmock.site/mock/2de212e0dc4b8e0885fea44ab9f2e1d0/vform/listField',
        labelKey: 'fieldLabel',
        nameKey: 'fieldName'
      },
      designerConfig: {
        externalLink: false,
      },
      formId: null
    }
  },
  created () {
    let id = this.$route.query.id
    this.formId = id
  },
  mounted () {
    this.getData(this.formId)
  },
  methods: {
    getData (id) {
      if (!id) return this.$msg('id参数缺失', 'warning')
      this.$http.questionnaireById({ id }).then(res => {
        this.formJson = res.data.quesJson
        this.$refs.vfDesigner.setFormJson(res.data.quesJson)
      })
    },
    saveFormJson () {
      let formJson = this.$refs.vfDesigner.getFormJson()
      let formFiled = this.$refs.vfDesigner.getFieldWidgets()
      console.log(formFiled, 'formFiled');
      //TODO: 将formJson提交给后端保存接口，需自行实现！！
      // this.$message.success('表单已保存！')

      let form = {
        formKey: this.formId,
        quesJson: formJson,
        fieldJson: formFiled,
      }
      // if (!this.formId) {
      //   this.$http.questionnaireAdd(form).then(res => {
      //     this.$msg('保存成功', 'success')
      //     this.$router.push('/questionnaire/index')
      //   })
      // } else {
      this.$http.questionnaireEdit(form).then(res => {
        this.$msg('保存成功', 'success')
        this.$router.push('/questionnaire/index')
      })
      // }

    },
    cancel () {
      this.$router.push('/questionnaire/index')
    }
  }
}
</script>

<style lang="scss" scoped>
.formEditor ::v-deep .main-header {
  display: none !important;
}
.formEditor {
  position: relative;
  // height: calc(100vh - 130px);
  // height: 100%;
  // overflow-y: auto;
  // overflow: hidden;
  .tool {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    // position: absolute;
    // top: 0;
    // right: 10px;
  }
}
</style>