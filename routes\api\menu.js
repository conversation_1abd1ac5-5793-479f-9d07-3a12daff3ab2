var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index");
const Service = require("../../services/MenuService");

router.get("/menuList", tryAsyncErrors(Service.menuList));
router.post("/menuList", tryAsyncErrors(Service.menuList));
router.post("/menuBeauList", tryAsyncErrors(Service.menuBeauList));
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.delete('/delete/:menuId', tryAsyncErrors(Service.del))
router.get('/list', tryAsyncErrors(Service.menuList))

module.exports = router;
