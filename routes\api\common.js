var express = require("express");
var router = express.Router();
const path = require("path");
const { tryAsyncErrors } = require(path.join(process.cwd(), "/utils/index"));
const UserService = require("../../services/UserService");
const ConfigService = require("../../services/ConfigService");
const GradeService = require("../../services/GradeService");
const RegisterService = require("../../services/RegisterService");

//验证码
router.get('/captcha', tryAsyncErrors(UserService.captcha))
router.get('/config', tryAsyncErrors(ConfigService.info))
router.get('/grade/notAssociatedStudentList', tryAsyncErrors(GradeService.notAssociatedStudentList))

router.post('/comRegister', tryAsyncErrors(RegisterService.comRegister))

module.exports = router;
