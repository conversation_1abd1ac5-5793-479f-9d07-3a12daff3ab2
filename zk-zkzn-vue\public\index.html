<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
  <title>
    <%= VUE_APP_TITLE %>
  </title>
  <link rel="stylesheet" href="<%= BASE_URL %>static/css/loading.css" />
  <script src="./config.js"></script>
  <!-- 禁止使用开发者工具 -->
  <script>
    // 禁止控制台调试
    (function() {
      // 防止控制台查看
      function disableConsole() {
        try {
          // 覆盖console对象的主要方法
          const methods = ['log', 'debug', 'info', 'warn', 'error', 'dir', 'trace'];
          const emptyFn = function() {};
          
          // 备份原始方法以备需要
          window._console = {};
          methods.forEach(method => {
            window._console[method] = console[method];
            console[method] = emptyFn;
          });
          
          // 完全禁用console对象
          Object.defineProperty(window, 'console', {
            get() {
              return {
                log: emptyFn,
                debug: emptyFn,
                info: emptyFn,
                warn: emptyFn,
                error: emptyFn,
                dir: emptyFn,
                trace: emptyFn
              };
            }
          });
        } catch (e) {}
      }
      
      // 仅在生产环境启用
      if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
        disableConsole();
      }
    })();
  </script>
  <!-- <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?7174bade1219f9cc272e7978f9523fc8";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script> -->
</head>

<body>
  <noscript>
    非常抱歉鉴于安全考量,您无法查看<%= VUE_APP_TITLE %>
      源代码 
  </noscript>
  <div id="vue-admin-beautiful">
    <div class="first-loading-wrp">
      <div class="loading-wrp">
        <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
      <h1>
        <%= VUE_APP_TITLE %>
      </h1>
    </div>
  </div>
</body>
</html>