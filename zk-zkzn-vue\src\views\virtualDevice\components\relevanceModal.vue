<template>
  <div v-loading="loading">
    <vxe-modal ref="xModal" v-model="modalVisible" width="1000" height="700" :title="modalTitle" @close="close" esc-closable show-footer resize transfer>
      <template v-slot>
        <div>
          <tree-select style="width: 200px;" :options="virtualCateOption" v-model="formData.id" @select="cateSelectNode" :show-count="true" :normalizer="cateNormalizer" placeholder="选择分类" />
        </div>

        <vxe-grid id="virtual_relevamce_tab" :custom-config="{ storage: true }" ref="xTreeRele" :id="'code'" :checkbox-config="{labelField: 'code', checkStrictly: true, checkRowKeys: checkRow}" keep-source :columns="tableColumn" :data="tableData" v-loading="loading" :scroll-y="{ enabled: true, mode: 'wheel' }" :row-config="{ keyField: 'code' }" :tree-config="{
        transform: true,
        accordion: false,
        rowField: 'id',
        parentField: 'pId',
        iconOpen: 'vxe-icon--arrow-bottom',
        iconClose: 'vxe-icon--arrow-right',
        iconLoaded: 'vxe-icon--refresh roll',
      }" :toolbarConfig="{
        size: 'mini',
        // custom: true,
        slots: { buttons: 'toolbar_left' },
      }">
          <template #toolbar_left>
            <!-- <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button> -->
          </template>
          <template #idSlot="{ row }">
            <el-tag :type="row.level == 1 ? '' : row.level == 2 ? 'warning' : 'success'">{{ row.id }}</el-tag>
            <el-button style="margin-left: 10px;" v-if="row.pId == 0" type="text" @click.native.stop="handleSelectAll(row)">全选子级</el-button>
          </template>
          <template #imgSlot="{ row }">
            <el-image style="height: 48px; vertical-align: bottom" :fit="'contain'" :src="row.pic ? $SOURCEURL + row.pic : ''" :preview-src-list="srcList">
              <div slot="error" class="el-image__error">
                <i class="el-icon-picture-outline" style="min-width: 50px; font-size: 32px"></i>
              </div>
            </el-image>
          </template>
          <template #operate="{ row }">
            <el-button type="text" v-permissions="['virtualCate/notRelevance']" @click="handleNotRelevant(row)">清除关联</el-button>
          </template>
        </vxe-grid>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="saveAndAgain">保存并继续</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<script>
export default {
  props: {},
  data () {
    return {
      cateNormalizer (node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children,
        };
      },
      loading: false,
      modalVisible: false,
      modalTitle: '关联课件',
      formData: {
        id: null
      },
      tableColumn: [
        { field: "id", title: "编号", type: "checkbox", treeNode: true, align: "left", width: 160, slots: { default: "idSlot" } },
        { field: "virtualName", title: "名称" },
        { field: "virtualCateName", title: "所属分类", width: 180 },
        // { field: "pic", title: "图片", showOverflow: false, slots: { default: "imgSlot" }, minWidth: 80, },
        { title: "操作", minWidth: 100, slots: { default: "operate" } },
      ],
      tableData: [],
      virtualCateOption: [],
      checkRow: [],
      selectIdArr: [],
    }
  },
  methods: {
    open (formData) {
      this.getVirtualDataListOption()
      this.getVirtualCateOption();
      this.formData = JSON.parse(JSON.stringify(formData))
      this.modalVisible = true
      this.$nextTick(() => {
        this.setCheck()
      })
    },
    setCheck () {
      let timer = setInterval(() => {
        if (this.$refs.xTreeRele) {
          clearInterval(timer)
          let arr = this.tableData.filter(item => {
            return item.virtualCateId == this.formData.id
          })
          this.$refs.xTreeRele.setCheckboxRow(arr, true)
        }
      }, 300)
    },
    async getVirtualDataListOption () {
      let { data } = await this.$http.virtualDataListOption()
      this.tableData = data
    },
    getVirtualCateOption () {
      this.$http.virtualCateOption().then((res) => {
        this.virtualCateOption = res.data
      });
    },
    cateSelectNode (node) {
      this.$refs.xTreeRele.clearCheckboxRow()
      this.formData.id = node.id
      this.setCheck()
    },
    handleSelectAll (row) {
      let index = this.selectIdArr.findIndex(item => item == row.id)
      if (index == -1) {
        this.selectIdArr.push(row.id)
        let arr = this.tableData.filter(item => {
          return item.pId == row.id
        })
        this.$refs.xTreeRele.setCheckboxRow(arr, true)
      } else {
        this.selectIdArr.splice(index, 1)
        let arr = this.tableData.filter(item => {
          return item.pId == row.id
        })
        this.$refs.xTreeRele.setCheckboxRow(arr, false)
      }
    },
    handleNotRelevant (row) {
      this.$http.virtualCateNotRelevance({ code: row.code }).then(res => {
        this.$msg('操作成功', "success");
        this.getVirtualDataListOption()
        this.$refs.xTreeRele.clearCheckboxRow()
        this.setCheck()
      })
    },
    save () {
      if (!this.formData.id) return this.$msg('请先选择分类', 'warning')
      let record = this.$refs.xTreeRele.getCheckboxRecords()
      if (!record || record.length == 0) return this.$msg('请勾选需要关联的课件', 'warning')
      this.loading = true
      let form = record.map(t => {
        return { code: t.code, virtualCateId: this.formData.id }
      })
      this.$http.virtualCateRelevance(form).then((res) => {
        this.loading = false
        this.$msg('关联成功', "success");
        this.close()
      });
    },
    saveAndAgain () {
      if (!this.formData.id) return this.$msg('请先选择分类', 'warning')
      let record = this.$refs.xTreeRele.getCheckboxRecords()
      if (!record || record.length == 0) return this.$msg('请勾选需要关联的课件', 'warning')
      this.loading = true
      let form = record.map(t => {
        return { code: t.code, virtualCateId: this.formData.id }
      })
      this.$http.virtualCateRelevance(form).then((res) => {
        this.loading = false
        this.$msg('关联成功', "success");
        this.getVirtualDataListOption()
        this.$refs.xTreeRele.clearCheckboxRow()
        this.setCheck()
      });
    },
    close () {
      this.checkRow = []
      this.$refs.xTreeRele.clearCheckboxRow()
      this.modalVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.taskInfo {
  padding: 10px 30px;
}

.operate {
  padding: 0 30px;

  .taskList {
  }

  .listBox {
    padding: 5px 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;

    .list {
      padding: 8px 0;

      .list-item {
        cursor: pointer;
        margin-bottom: 5px;
        &:hover {
          background-color: #f8f6f6;
        }
      }
    }
  }
}
</style>