'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class UserSerial extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      this.belongsTo(models.User,{foreignKey:'userId', constraints: false})
    }
  }
  UserSerial.init(
    {      
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "唯一ID"
      },
      userId:{
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "用户id"
      },        
      serialNumber:{
        type: DataTypes.STRING,
        allowNull: false,
        comment: "设备编号"
      }, 
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'UserSerial',
      tableName: 'zk_user_serial',
      comment: '用户设备编号记录表'
    }
  )
  return UserSerial
}
