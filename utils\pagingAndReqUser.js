module.exports = (req, res, next) => {
  
  if (req.method == 'GET') {
    //get请求中的list列表添加用户id，用来区分用户数据权限，实现原理是通过自定义封装的$LikeWhere检测是否传入了表的创建人id字段
    // if(req.originalUrl.indexOf('list') != -1) {
    //   if(!req.query.createById) {
    //     req.query.createById = req.user.userId
    //   }
    // }
    //处理get请求的分页参数转换为数字类型
    if (req.query.page && req.query.pageSize) {
      req.query.page = parseInt(req.query.page)
      req.query.pageSize = parseInt(req.query.pageSize)      
      if (isNaN(req.query.page) || isNaN(req.query.pageSize)) {
        return res.send({ code: -1, msg: "分页参数异常或缺失" })
      }
    }
  }
  //处理post请求的分页参数以及排除login请求
  if (req.method == 'POST' && req.originalUrl.indexOf('login') < 0) {
    //对全局add请求添加用户信息，用来给需要记录用户id和用户名的数据全局设置
    if (req.originalUrl.toLowerCase().indexOf('add') != -1 && req.user && req.user.userName && req.user.userId) {
      if (!req.body.createBy) {
        req.body.createBy = req.user.userName
      }
      if (!req.body.userId) {
        req.body.userId = req.user.userId
      }
      if (!req.body.createById) {
        req.body.createById = req.user.userId
      }
      if (!req.body.updateById) {
        req.body.updateById = req.user.userId
      }
      if (!req.body.userName) {
        req.body.userName = req.user.userName
      }
    }
    //处理post请求的分页参数转换为数字类型
    if (req.body.page && req.body.pageSize) {
      req.body.page = parseInt(req.body.page)
      req.body.pageSize = parseInt(req.body.pageSize)
      if (isNaN(req.body.page) || isNaN(req.body.pageSize)) {
        return res.send({ code: -1, msg: "分页参数异常或缺失" })
      }
    }
  }
  next()
}