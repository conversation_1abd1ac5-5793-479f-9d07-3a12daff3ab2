// 引入 ioredis 包
const Redis = require('ioredis')
// 引入配置文件 需要传入给实例对象
const { redisConfig, redisOptions } = require('../config/redisConfig')
console.log(redisConfig, 'redisConfig');
let RedisClient;
// 创建实例 连接NoSQL服务器
if (redisConfig) {
  const redisNodes = redisConfig;   //多于一个节点，创建集群连接
  if (redisNodes.length > 1) {
    RedisClient = new Redis.Cluster(redisNodes, {
      redisOptions: {
        password: null,
      },      
    });
  } else if (redisNodes.length === 1) {
    // 如果只配置一个，那么认为是单节点连接
    // RedisClient = new Redis(`redis://${redisNodes[0].host}:${redisNodes[0].port}/0`, {
    //     // keyPrefix: prefix,
    // });
    RedisClient = new Redis(redisNodes[0], {
      // keyPrefix: prefix,
      reconnectOnError(err) {
        const targetError = "READONLY";
        if (err.message.includes(targetError)) {
          // Only reconnect when the error contains "READONLY"
          return true; // or `return 1;`
        }
      },
    });
  } else {
    throw ('redis 配置错误')
  }
} else {
  throw ('redis 配置错误')
}

RedisClient.on('connect', () => {
  console.log('********连接redis成功**********');
  // console.log(RedisClient, 'RedisClient');
})
module.exports = {
  RedisClient
}
