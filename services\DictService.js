const { VirtualProcess, OperType, PathPrefix, PathWeb, VirtualData, VirtualCate, ProcessRecord, Dict, DictData, $LikeWhere, sequelize: $seq } = require('../models')
const RedisService = require("./RedisService");
const { handleTree } = require('../utils/index')
const fields = Object.keys(Dict.tableAttributes)

class DictService {
  static async create (req, res) {
    if (!req.body.dictName || !req.body.dictCode) return res.sendSuccess({ code: -1, msg: '字典名称和编码不能为空' })
    let data = await Dict.create(req.body)
    RedisService.setDictCodeOption(req.body.dictCode)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await Dict.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    RedisService.setDictCodeOption(req.body.dictCode)
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    if (!id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = $seq.transaction(async transaction => {
      let originData = await Dict.findByPk(id, { transaction })
      await DictData.destroy({
        where: {
          dictCode: originData.dictCode
        },
        transaction
      })
      RedisService.setDictCodeOption(originData.dictCode)
      return await Dict.destroy({
        where: {
          id
        },
        transaction
      })
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async list (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await Dict.findAndCountAll({
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        // order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }

  static async operTypeOption (req, res) {
    let data = await OperType.findAll({
      attributes: [
        ['id', 'value'], ['operTypeName', 'label']
      ],
      where: {

      },
      raw: true
    })
    res.send({ code: 200, data })
  }

  static async pathPrefixOption (req, res) {
    let data = await PathPrefix.findAll({
      attributes: [
        ['id', 'value'], ['prefixName', 'label']
      ],
      where: {

      },
      raw: true
    })
    res.send({ code: 200, data })
  }

  static async pathWebOption (req, res) {
    let data = await PathWeb.findAll({
      attributes: [
        ['id', 'value'], ['webName', 'label'], 'webUrl'
      ],
      where: {

      },
      raw: true
    })
    res.send({ code: 200, data })
  }

  static async virtualCateOption (req, res) {
    let result = await VirtualCate.findAll({
      attributes: [
        'id', 'pId', 'name'
      ],
      where: {
        status: 1
      },
      order: [
        ['orderNum'], ['id']
      ],
      raw: true
    })
    let data = handleTree(result, 'id', 'pId')
    res.send({ code: 200, data })
  }

  static async virtualDataListOption (req, res) {
    let data = await VirtualData.findAll({
      attributes: {
        include: [
          [$seq.col('VirtualCate.name'), 'virtualCateName']
        ]
      },
      include: [
        {
          model: VirtualCate,
          attributes: []
        }
      ],
      where: {
        level: {
          $lt: 3
        }
      },
      order: [
        // [$seq.literal('FIELD(`VirtualData`.`sortBy`,NULL) ASC')],
        [$seq.literal('ISNULL(`VirtualData`.`sortBy`)')],
        ['sortBy'],
        ['id']
      ],
      raw: true
    })
    res.send({ code: 200, data })
  }

  static async getDictList (req, res) {
    let { dictField } = req.query
    // let rows = await VirtualProcess.findAll({
    //   attributes: [['id', 'value'], ['vrProName', 'label']],
    //   // raw: true
    // })
    let rows = await VirtualData.findAll({
      attributes: [['code', 'value'], ['virtualName', 'label']],
      where: {
        pId: 0
      }
      // raw: true
    })
    res.sendSuccess({ code: 200, data: rows })
  }

  static async codeOption (req, res) {
    let data = await Dict.findAll({
      attributes: [['dictCode', 'value'], ['dictName', 'label'], ['id', 'dictId']],
      where: {
        status: 1,
      },
      raw: true
    })
    res.send({ code: 200, data })
  }

  static async codeTreeDataOption (req, res) {
    let data = await Dict.findAll({
      attributes: [['dictCode', 'value'], ['dictName', 'label'], ['id', 'dictId']],
      include: [
        {
          model: DictData,
          attributes: [['dictValue', 'value'], ['dictLabel', 'label']]
        }
      ],
      where: {
        status: 1,
      },
      order: [
        [
          $seq.col('DictData.sortBy')
        ]
      ]
    })
    res.send({ code: 200, data })
  }

  static async virtualTreeOption (req, res) {
    let result = await VirtualData.findAll({
      // where: {
      //   status: '1'
      // },
      order: [
        ['sortBy'], ['id']
      ]
    })
    let data = handleTree(result, 'id', 'pId')
    res.send({ code: 200, data })
  }

  static async option (req, res) {
    if (!req.query.dictCode) return res.sendSuccess({ code: -1, msg: 'dictCode参数缺失' })
    let data = await RedisService.getDictCodeOption(req.query.dictCode)
    res.send({ code: 200, data })
  }

  static async roleOption (req, res) {
    let data = await RedisService.getRoleOption()
    res.send({ code: 200, data })
  }

  static async roleMenuOption (req, res) {
    let data = await RedisService.getRoleMenuOption()
    res.send({ code: 200, data })
  }

  static async gradeOption (req, res) {
    let data = await RedisService.getGradeOption()
    res.send({ code: 200, data })
  }
}

module.exports = DictService
