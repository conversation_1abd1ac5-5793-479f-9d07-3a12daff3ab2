\<template>
  <el-row :gutter="10" class="singleChoice">
    <el-col>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="cardTitle">课程创编：</span>
        </div>
        <el-form :model="formData" :rules="$rules.coursewareRules" ref="formRef" label-width="120px" class="demo-formData">
          <el-form-item label="所属工艺流程" prop="processTypeId">
            <el-select v-model="formData.processTypeId" placeholder="请选择所属工艺流程">
              <el-option 
                v-for="item in coursewareTypeSelect" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程名称" prop="coursewareName">
            <el-input v-model="formData.coursewareName" placeholder="请输入课程名称"></el-input>
          </el-form-item>
          <el-form-item label="封面图片" prop="coursewarePic">
            <el-upload class="avatar-uploader" :headers="{token: $store.getters['user/accessToken']}" accept=".jpg,.png,.jpeg" :action="$BASEURL+'/file/upload'" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <div class="avaterBox">
                <img v-if="formData.coursewarePic" :src="$SOURCEURL+formData.coursewarePic" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div v-if="formData.coursewarePic" class="avaterMask" @click.stop="delUploadImg">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="课程资源">
            <el-upload class="upload-demo sourceBox" :headers="{ token: $store.getters['user/accessToken'] }" multiple accept=".mp4,.doc,.docx,.xls,.xlsx,.pdf" :action="$BASEURL+'/file/uploadSourceFile'" :before-upload="beforeUploadSource" :on-error="uploadErrSource" :on-success="uploadSuccessSource" :file-list="formData.courseSources" :before-remove="beforeRemoveSource" :on-remove="removeSource">
              <el-button size="small" type="primary">上传课程资源</el-button>
              <div slot="tip" class="el-upload__tip">只能上传mp4、doc、docx、xls、xlsx、pdf文件，且不超过50MB</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="课程概述" prop="coursewareContent" class="coursewareContent">
            <el-upload class="upload-demo" :headers="{ token: $store.getters['user/accessToken'] }" accept=".doc,.docx" :action="$BASEURL+'/file/uploadFileGetContent'" :before-upload="beforeUpload" :on-error="uploadErr" :on-success="uploadSuccess">
              <el-button size="small" type="primary">导入Word文档</el-button>
              <div slot="tip" class="el-upload__tip">只能上传doc/docx文件，且不超过5MB</div>
            </el-upload>
            <WangEditor ref="wangEditorRef" :text="formData.coursewareContent"></WangEditor>
          </el-form-item>
          <el-form-item style="text-align:center">
            <el-button type="primary" @click="save">保存</el-button>
            <el-button type="primary" @click="save('continue')">保存并继续</el-button>
            <el-button @click="close">取消</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
import myMixin from './mixin.js';
import WangEditor from '@/components/WangEditor';
export default {
  name: "coursewareOperator",
  mixins: [myMixin],
  components: {
    WangEditor
  },
  data() {
    return {
      formData: {
        processTypeId: '',
        coursewareName: '',
        coursewareContent: '',
        coursewarePic: '',
        courseSources: [],
      },
      sourceFileList: [],
    };
  },
  created() {
    if (this.$route.query.coursewareId) {
      this.updateFormData();
    } else {
      this.close();
    }
  },
  activated() {
    if (this.$route.query.coursewareId) {
      this.updateFormData();
    } else {
      this.close();
    }
  },
  methods: {
    updateFormData() {
      Promise.all([
        this.$http.getDictList({ dictField: 'processFlow' }),
        this.$http.coursewareOne(this.$route.query.coursewareId)
      ]).then(([dictRes, coursewareRes]) => {
        this.coursewareTypeSelect = dictRes.data;
        
        this.$nextTick(() => {
          this.formData = {
            ...coursewareRes.data,
            processTypeId: coursewareRes.data.processTypeId ? String(coursewareRes.data.processTypeId) : '',
            courseSources: coursewareRes.data.courseSources ? coursewareRes.data.courseSources.map(item => ({
              name: item.name,
              url: item.url,
              id: item.id,
              status: 'success'
            })) : []
          };
        });
      });
    },
    /* 封面图片上传------- start */
    handleAvatarSuccess(res, file) {
      this.formData.coursewarePic = res.url;
      this.$message.success('上传成功！');
    },
    beforeAvatarUpload(file) {
      let prefix = file.name.split('.').pop().toLowerCase();
      let extension = ['jpg', 'png', 'jpeg'].includes(prefix);
      if (!extension) {
        this.$message({
          message: '上传封面图片只能是 jpg、png、jpeg格式!',
          type: 'warning'
        });
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传封面图片大小不能超过 2MB!');
      }
      return extension && isLt2M;
    },
    delUploadImg() {
      this.formData.coursewarePic = "";
    },
    /* 封面图片上传------- end */
    /* 富文本导入word------- start */
    beforeUpload(file) {
      let prefix = file.name.split('.').pop().toLowerCase();
      let extension = ['doc', 'docx'].includes(prefix);
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!extension) {
        this.$message({
          message: '上传文件只能是 doc、docx格式!',
          type: 'warning'
        });
      }
      if (!isLt5M) {
        this.$message({
          message: '上传文件大小不能超过 5MB!',
          type: 'warning'
        });
      }
      return extension && isLt5M;
    },
    uploadSuccess(res, file, fileList) {
      if (res.code != -1) {
        this.formData.coursewareContent += res.html;
        this.$msg('文件上传成功', 'success');
      } else {
        this.$msg(res.msg, 'error');
      }
    },
    uploadErr(err, file, fileList) {
      this.$msg(err.toString(), 'error');
    },
    /* 富文本导入word------- end */
    /* 课程资源上传------- start */
    beforeUploadSource(file) {
      let prefix = file.name.split('.').pop().toLowerCase();
      console.log('File name:', file.name, 'Extracted extension:', prefix);
      let extension = ['mp4', 'doc', 'docx', 'xls', 'xlsx', 'pdf'].includes(prefix);
      if (!extension) {
        this.$message({
          message: '上传文件只能是 mp4、doc、docx、xls、xlsx、pdf格式!',
          type: 'warning'
        });
        return false;
      }
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message({
          message: '上传文件大小不能超过 50MB!',
          type: 'warning'
        });
        return false;
      }
      return true;
    },
    uploadSuccessSource(res, file, fileList) {
      if (res.code != -1) {
        file.id = res.id;
        this.formData.courseSources = fileList.map(item => ({
          ...item,
          id: item.id || res.id,
          name: item.name,
          url: res.url,
          status: 'success'
        }));
        this.$msg('文件上传成功', 'success');
      } else {
        this.$msg(res.msg, 'error');
      }
    },
    uploadErrSource(err, file, fileList) {
      this.$msg(err.toString(), 'error');
    },
    beforeRemoveSource(file, fileList) {
      console.log('File to remove:', file);
      console.log('FileList:', fileList);
      if (!file.id) {
        this.formData.courseSources = fileList.filter(item => item.uid !== file.uid);
        return true;
      }
      return this.$confirm('请确认是否删除此文件资源?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let data = await this.$http.coursewareSourceDel(file);
        if (data.code != -1) {
          this.$msg('操作成功', 'success');
          return true;
        } else {
          this.$msg(data.msg, 'error');
          return false;
        }
      }).catch(() => {
        return false;
      });
    },
    removeSource(file, fileList) {
      this.formData.courseSources = fileList;
    },
    /* 课程资源上传------- end */
    save(params) {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.formData.coursewareContent = this.$refs.wangEditorRef.getContent();
          let form = this.formData;
          if (!this.formData.id) {
            this.$http.coursewareAdd(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close();
              if (params != 'continue') {
                this.$router.push({ path: '/courseware/coursewareManage' });
              }
            });
          } else {
            this.$http.coursewareEdit(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close();
              if (params != 'continue') {
                this.$router.push({ path: '/courseware/coursewareManage' });
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    close() {
      this.$resetForm('formRef');
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },
    // 在mixin的getProcessFlow方法执行后设置默认值
    getProcessFlow() {
      this.$http.getDictList({ dictField: 'processFlow' }).then(res => {
        this.coursewareTypeSelect = res.data;
        // 移除默认值设置，让用户主动选择
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.singleChoice {
  .cardTitle {
    font-size: 16px;
    color: $base-color-default;
  }
  .suffixIcon {
    font-size: 22px;
    margin-top: 6px;
    cursor: pointer;
  }
  .coursewareLevel ::v-deep {
    .el-rate {
      line-height: 45px;
    }
  }
  .coursewareBox ::v-deep {
    margin-bottom: 5px;
    p {
      margin: 3px;
    }
  }
  .coursewareContent ::v-deep {
    line-height: 20px;
    .el-upload__tip {
      margin-top: 0px;
    }
    .el-upload-list__item {
      margin-top: 0px;
    }
    .el-upload-list__item:first-child {
      margin-top: 0px;
    }
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 100%;
    height: 250px;
    display: block;
  }
  .avaterBox:hover .avaterMask {
    background-color: #f56c6c;
  }
  .avaterBox:hover .avaterMask .success {
    display: none;
  }
  .avaterBox:hover .avaterMask .delete {
    display: block;
  }
  .avaterMask {
    width: 80px;
    height: 80px;
    line-height: 40px;
    border-radius: 8px;
    transform: rotate(45deg);
    position: absolute;
    right: -40px;
    top: -40px;
    font-size: 20px;
    background-color: #67c23a;
    transition: all 0.4s;
    span {
      position: absolute;
      top: 55px;
      left: 30px;
      color: #fff;
      .delete {
        display: none;
      }
      .success {
        display: block;
        transform: rotate(-45deg);
      }
    }
  }
  .upload-demo ::v-deep {
    .el-upload__tip {
      margin-top: 0px !important;
    }
  }
  .sourceBox ::v-deep {
    max-width: 360px;
    .el-upload-list {
      height: 100px;
      border: 1px solid #ccc;
      border-radius: 4px;
      max-height: 100px;
      overflow: auto;
      .el-upload-list__item:first-child {
        margin-top: 5px;
      }
    }
  }
}
</style>




















