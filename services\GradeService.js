const { Grade, GradeUser, User, sequelize: $seq, $LikeWhere } = require('../models')
const RedisService = require('./RedisService')
const fields = Object.keys(Grade.tableAttributes)
const config = require(__dirname + '/../config/config.js')["student_role"]

class GradeService {

  static async create (req, res) {
    let data = await Grade.create(req.body)
    RedisService.setGradeOption()
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    let data = await Grade.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    RedisService.setGradeOption()
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await Grade.destroy({
      where: {
        id
      },
    })
    RedisService.setGradeOption()
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  //默认获取个人的届/年级列表
  static async list (req, res) {
    let { page, pageSize, gradeName } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await Grade.findAndCountAll({
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        // order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let data = await Grade.findAll({
        attributes: ['id', 'gradeName'],
        where,
        // order:[['createTime','desc']]
      })
      res.sendSuccess({ code: 200, data })
    }
  }

  //获取关联的学生列表
  static async studentList (req, res) {
    let { page, pageSize, gradeId, userName } = req.query
    //2022.3.15 czk先查询用户个人创建的届/年级列表----start
    let ids = await Grade.findAll({
      attributes: ['id'],
      where: {
        createById: req.user.userId
      },
      raw: true
    })
    ids = ids.map(item => { return item.id })
    let where = {
      gradeId: {
        $in: ids
      }
    }
    //2022.3.15 czk先查询用户个人创建的届/年级列表----end
    let userWhere = {}
    if (gradeId) {
      where.gradeId = gradeId
    }
    if (userName) {
      userWhere['userName'] = { $like: '%' + userName + '%' }
    }
    let { rows, count } = await GradeUser.findAndCountAll({
      attributes: {
        include: ['User.userName', 'User.nickName', 'User.avatar', 'User.phone', 'User.email', 'Grade.gradeName']
      },
      include: [
        {
          model: User,
          attributes: [],
          where: userWhere
        },
        {
          model: Grade,
          attributes: []
        }
      ],
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      raw: true
      // order:[['createTime','desc']]
    })
    //2022.3.11 czk发现此处使用sequelize查询的数据很奇怪。生成的sql也比较复杂，所以需要手动转换数据
    //下班前又发现是自己定义的关联关系有问题，将hasMany改成hasOne就OK了
    // rows = rows.map((item) => {
    //   return {
    //     id:item.id,
    //     gradeId:item.gradeId,
    //     userId:item.userId,
    //     userName:item.Users[0].userName,
    //     avatar:item.Users[0].avatar,
    //     nickName:item.Users[0].nickName,
    //     phone:item.Users[0].phone,
    //     email:item.Users[0].email
    //   }
    // })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  //关联学生移除
  static async studentDel (req, res) {
    const { id } = req.params
    let data = await GradeUser.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '移除成功' })
  }

  //获取未关联的学生列表
  static async notAssociatedStudentList (req, res) {
    let { page, pageSize, userName } = req.query
    let where = {
      id: {
        $notIn: $seq.literal(`(SELECT userId FROM zk_basic_grade_user)`),
        $in: $seq.literal(`(SELECT userId FROM sys_user_role WHERE roleId = ${config.roleId})`)
      }
    }
    if (userName) {
      where['userName'] = { $like: '%' + userName + '%' }
    }
    let { rows, count } = await User.findAndCountAll({
      where,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      // order:[['createTime','desc']]
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  //添加关联学生列表信息
  static async addAssociationStudent (req, res) {
    let data = await GradeUser.bulkCreate(req.body)
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = GradeService
