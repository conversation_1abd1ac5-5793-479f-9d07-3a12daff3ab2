const { VirtualData, VirtualProcess, VirtualProcessDevice, VirtualProcessDeviceOper, sequelize: $seq } = require('../models')
const { readTxtFileToArr } = require('../utils/txtToArray')
class FileService {
  static async vrProcessUpload (req, res, next) {
    let txtPath = req.file.path
    readTxtFileToArr(1, txtPath, async (err, arr) => {
      if (err) return res.sendSuccess({ code: -1, msg: err, error: err })

      const transaction = await $seq.transaction()
      try {
        let [finalId, results] = await $seq.query(`INSERT INTO zk_virtual_process(id,assetName,vrProName,deviceIds,vrProInfo) VALUES ? 
          ON DUPLICATE KEY UPDATE assetName = VALUES(assetName),vrProName = VALUES(vrProName),deviceIds = VALUES(deviceIds),vrProInfo = VALUES(vrProInfo)`, { replacements: [arr], type: 'INSERT', transaction })

        let vpData = await $seq.query(`SELECT vp.id,vp.vrProName virtualName,vp.assetName,vp.vrProInfo description FROM zk_virtual_process vp LEFT JOIN zk_virtual_data vd ON vp.id = vd.id WHERE vd.id IS NULL`, { type: 'SELECT', transaction, raw: true })
        if (vpData && vpData.length > 0) {
          vpData.forEach(item => {
            item['pId'] = 0
            item['isExecutable'] = 1
            item['isPlatform'] = 2
            item['createById'] = 1
            item['updateById'] = 1
            item['level'] = 1
          })
          await VirtualData.bulkCreate(vpData, { transaction })
        }
        await transaction.commit();
        res.sendSuccess({ code: 200, msg: '上传成功', data: txtPath, url: txtPath, arr, finalId, results })
      } catch (error) {
        await transaction.rollback();
        // res.sendSuccess({ code: -1, msg: error.original })
        next(error)
      }
    })
  }
  static async vrDeviceUpload (req, res, next) {
    let txtPath = req.file.path
    readTxtFileToArr(2, txtPath, async (err, arr) => {
      if (err) return res.sendSuccess({ code: -1, msg: err, error: err })
      const transaction = await $seq.transaction()
      try {
        let [finalId, results] = await $seq.query(`INSERT INTO zk_process_device(id,assetName,processName,deviceName,position,rotation,scaling,unitsIds,operationIds,processInfo,deviceInfo) VALUES ? 
          ON DUPLICATE KEY UPDATE assetName = VALUES(assetName),processName = VALUES(processName),deviceName = VALUES(deviceName),position = VALUES(position),rotation = VALUES(rotation),scaling = VALUES(scaling),unitsIds = VALUES(unitsIds),operationIds = VALUES(operationIds),processInfo = VALUES(processInfo),deviceInfo = VALUES(deviceInfo)`, { replacements: [arr], transaction })
        let [updates] = await $seq.query('UPDATE zk_process_device zd LEFT JOIN zk_virtual_process zvp ON FIND_IN_SET(zd.id,zvp.deviceIds) SET zd.vpId = zvp.id', { transaction })

        let vpData = await $seq.query(`SELECT vp.id,vp.vpId pId,vp.deviceName virtualName,vp.assetName,vp.processName,vp.processInfo processDesc,vp.deviceInfo description FROM zk_process_device vp LEFT JOIN zk_virtual_data vd ON vp.id = vd.id AND vp.vpId = vd.pId WHERE vd.id IS NULL AND vd.pId IS NULL AND vp.vpId IS NOT NULL`, { type: 'SELECT', transaction, raw: true })
        if (vpData && vpData.length > 0) {
          vpData.forEach(item => {
            item['isExecutable'] = 1
            item['isPlatform'] = 2
            item['createById'] = 1
            item['updateById'] = 1
            item['level'] = 2
          })
          await VirtualData.bulkCreate(vpData, { transaction })
        }

        await transaction.commit();
        res.sendSuccess({ code: 200, msg: '上传成功', data: txtPath, url: txtPath, arr, finalId, results, updates })

      } catch (error) {
        await transaction.rollback();
        next(error)
      }
    })
  }
  static async vrOperationUpload (req, res, next) {
    let txtPath = req.file.path
    readTxtFileToArr(3, txtPath, async (err, arr) => {
      if (err) return res.sendSuccess({ code: -1, msg: err, error: err })
      const transaction = await $seq.transaction()
      try {
        let arrnull = arr.filter(item => {
          return item.every(val => { return val == null })
        })
        let [finalId, results] = await $seq.query(`INSERT INTO zk_process_device_oper(id,assetName,remark,functionName,operationName,typeId,operationInfo,qizzTips) VALUES ? 
          ON DUPLICATE KEY UPDATE assetName = VALUES(assetName),remark = VALUES(remark),functionName = VALUES(functionName),operationName = VALUES(operationName),typeId = VALUES(typeId),operationInfo = VALUES(operationInfo),qizzTips = VALUES(qizzTips)`, { replacements: [arr], type: 'INSERT', transaction })
        let [updates] = await $seq.query('UPDATE zk_process_device_oper zdo LEFT JOIN zk_process_device zd ON FIND_IN_SET(zdo.id,zd.operationIds) SET zdo.vpdId = zd.id', { transaction })
        
        let vpData = await $seq.query(`SELECT vp.id,vp.vpdId pId,vp.operationName virtualName,vp.assetName,vp.operationInfo description FROM zk_process_device_oper vp LEFT JOIN zk_virtual_data vd ON vp.id = vd.id AND vp.vpdId = vd.pId WHERE vd.id IS NULL AND vd.pId IS NULL AND vp.vpdId IS NOT NULL`, { type: 'SELECT', transaction, raw: true })
        if (vpData && vpData.length > 0) {
          vpData.forEach(item => {
            item['isExecutable'] = 0
            item['isPlatform'] = 2
            item['createById'] = 1
            item['updateById'] = 1
            item['level'] = 3
          })
          await VirtualData.bulkCreate(vpData, { transaction })
        }

        await transaction.commit();
        res.sendSuccess({ code: 200, msg: '上传成功', data: txtPath, url: txtPath, arr, len: arr.length, finalId, results, updates })
      } catch (error) {
        await transaction.rollback();
        next(error)
      }
    })
  }

}

module.exports = FileService
