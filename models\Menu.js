'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Menu extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsToMany(models.Role, {as:'roles', through: sequelize.models.UserRole,foreignKey:'roleId',otherKey:'userId' })
      // this.hasOne(models.Menu,{as:'To',foreignKey:'parentId'})
    }
  }
  Menu.init(
    {      
      menuId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "菜单ID"
      },
      menuName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "菜单名称"
      },
      parentId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        defaultValue: 0,
        comment: "父菜单ID"
      },
      levelCode: {
        type: DataTypes.STRING(200),
        allowNull: true,
        // defaultValue: '0001',
        comment: "层次码"
      },
      sortBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      // name: {
      //   type: DataTypes.VIRTUAL,
      //   get () {
      //     return this.path.charAt(0).toUpperCase() + this.path.slice(1);
      //   }
      // },
      path: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "路由地址",
      },
      component: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: "组件路径",
      },
      menuType: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "",
        comment: "菜单类型（M目录 C菜单 F按钮）"
      },
      menuTypeName: {
        type: DataTypes.VIRTUAL,
        get () {
          if (this.menuType == 'M') {
            return '目录'
          } else if (this.menuType == 'C') {
            return '菜单'
          } else {
            return '按钮'
          }
        }
      },
      visible: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0显示 1隐藏）"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0正常 1停用）"
      },
      isFrame: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "是否外链（0是 1否）"
      },
      isCache: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "是否缓存（0是 1否）"
      },
      perms: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "权限标识"
      },
      icon: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "#",
        comment: "菜单图标"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Menu',
      tableName: 'sys_menu',
      comment:'系统菜单表'
    }
  )
  return Menu
}
