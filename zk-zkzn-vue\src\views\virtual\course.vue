<template>
  <div class="container">
    <div class="bread">
      <el-button icon="el-icon-refresh" size="mini" @click="init()"></el-button>
      <span v-for="(item, i) in muluData" :key="i" @click="getMulu(item, i)">
        <span v-if="i != 0">/</span>
        {{ item.name }}
      </span>
    </div>
    <el-row :gutter="10" class="rowBox" v-loading="loading">
      <el-col class="course-col-xll" :xs="24" :sm="12" :md="12" :lg="8" :xl="6" v-for="(item, index) in cardData"
        :key="index">
        <el-card shadow="hover" :body-style="{ padding: '0px' }">
          <div slot="header" class="clearfix" style="height: 24px; overflow: hidden">
            <span>
              <!-- <el-tag>{{item.virtualId}}</el-tag> -->
              <i class="el-icon-connection"></i>{{ item.name }}
            </span>
            <!-- <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button> -->
            <!-- <el-tag v-show="item.children" style="float: right"
              >子节点：{{ item.children ? item.children.length : 0 }}</el-tag
            > -->
          </div>
          <div class="box" @click="getImg(item, index)">
            <div class="demo">
              <el-image style="height: 180px" :fit="'contain'" :src="item.pic ? $SOURCEURL + item.pic : ''">
                <div slot="error" class="el-image__error">
                  <i class="el-icon-picture-outline" style="min-width: 180px; font-size: 32px"></i>
                </div>
              </el-image>
            </div>
            <div class="infoBox" :title="item.description">
              {{ item.description }}
            </div>
            <p>
              <span class="prefix">
                <a v-if="isPlatformValid(item.isPlatform, '1')" class="button-exe"
                  @click.stop="handleProgramWebLoad(item)"><i class="iconfont icon-webx"></i>&nbsp;载入web课程</a>
              </span>
              <a class="button-exe" @click.stop="handleProgramLoad(item)"
                v-if="isPlatformValid(item.isPlatform, '2')"><i class="el-icon-cpu"></i>&nbsp;载入课件</a>

              <a class="button-video" @click.stop="handleVideoLoad(item)" v-if="item.videoSrc"><i
                  class="el-icon-video-camera"></i>视频</a>
            </p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <vxe-modal v-model="fileUrlShow" :width="titleMarking == '课件下载' ? 200 : 450" :title="titleMarking"
      style="text-align: center" @close="onCloseModal">
      <template #title>
        <span style="color:red" v-if="titleMarking != '课件下载'">{{ titleMarking }}</span>
        <span v-else>{{ titleMarking }}</span>
      </template>
      <template #default>
        <div class="modal-content">
          <el-button type="primary" @click="downloadFile">下载课件</el-button>
        </div>
      </template>
    </vxe-modal>
    <el-dialog 
      :visible.sync="loadingVisible" 
      title="下载中" 
      width="30%" 
      :close-on-click-modal="false" 
      :show-close="true"
      @close="onCloseModal"
      style="text-align: center;">
      <div>{{ downloadFileName }}课件仅需<span style="color:red">下载一次</span>，<span
          style="color:red">安装完成后</span>即可使用所有"载入课件"的功能，无需下载每一个课件。</div>
      <br>
      <div v-if="progressVisible" class="download-progress-container">
        <el-progress 
          :percentage="progress" 
          :text-inside="true" 
          :stroke-width="20"
          :status="downloadStatus"
          :format="progressFormat"
        />
        <div class="download-status-text" v-if="bytesInfo">{{ bytesInfo }}</div>
        <div class="download-actions" v-if="isDownloading">
          <el-button size="small" type="danger" @click="onCloseModal">取消下载</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- <div v-if="loadingVisible" class="loading-container" style="text-align: center;">
      <div class="loading-content">
        <div>正在下载，请稍候...</div>
        <br>
        <el-progress :percentage="progress" v-if="progressVisible" :text-inside="true" :stroke-width="20"/>
      </div>
    </div> -->

    <!-- 视频播放对话框 -->
    <vxe-modal v-model="videoDialogVisible" :title="currentVideoTitle" :width="1000" :height="700" :mask="true" :lock-view="true"
      :show-close="true" :resize="true" @show="handleDialogOpened" @hide="closeVideoDialog" class="video-modal" center >
      <template #default>
        <div class="video-container">
          <XGPlayer v-if="showPlayer" :src="currentVideoSrc" :poster="videoPoster" :width="'100%'" :height="'100%'"
            :autoplay="true" :volume="0.6" :disableContextmenu="true" @play="onVideoPlay" @pause="onVideoPause"
            @ended="onVideoEnded" @error="onVideoError" />
          <div v-else class="player-loading">
            <i class="el-icon-loading"></i>
            <p>加载播放器中...</p>
          </div>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>
<script>
import protocolCheck from "@/protocolCheck/index.js";
import { Base64 } from 'js-base64';
import XGPlayer from '@/components/XGPlayer';
export default {
  name: "Course",
  components: {
    XGPlayer
  },
  data() {
    return {
      isDownloading: false,
      loadingVisible: false,
      progressVisible: false,
      progress: 0,
      downloadStatus: '', // 只使用Element UI允许的值: '', 'success', 'exception', 'warning'
      bytesInfo: '', // 新增：显示已下载/总大小的信息
      fileUrlShow: false,
      titleMarking: "课件下载",
      websock: null,
      loading: false,
      muluData: [{ name: "主目录", id: 0 }],
      tableData: [],
      cardData: [],
      fileUrlData: null, // 文件的 URL
      fileName: "chipManufacturing.exe", // 下载文件的名称
      downloadFileName: "",
      downloadingFile: null,   // 当前正在下载的文件 URL
      abortController: null, // AbortController 实例
      videoDialogVisible: false, // 视频播放对话框显示状态
      currentVideoSrc: '', // 当前视频源
      videoPoster: '', // 视频海报图
      showPlayer: false, // 控制播放器显示
      currentVideoTitle: '', // 新增的 currentVideoTitle 数据属性
      corsProxyUrl: '', // 可选的CORS代理URL，如果需要绕过CORS
      downloadMethod: 'direct', // 'direct' 或 'iframe' 或 'a-tag'
    };
  },
  computed: {},
  created() {
    this.init();
  },
  destroyed: function () {
    // 离开页面生命周期函数
    //   this.websocketclose();
  },
  methods: {
    // 检查 URL 是否有效
    isValidUrl(url) {
      try {
        new URL(url);
        return true; // 没有抛出错误，返回 true
      } catch (error) {
        console.log(error);

        // console.error("Invalid URL:", error); // 可选：输出错误信息
        return false; // 抛出错误，返回 false
      }
    },

    isPlatformValid(isPlatform, platformId) {
      if (!isPlatform) return false
      return isPlatform.split(',').includes(platformId);
    },
    onCloseModal() {
      if (this.abortController) {
        this.abortController.abort(); // 取消下载
        this.$message({
          message: '下载已取消',
          type: 'info'
        });
      }
      this.loadingVisible = false;
      this.progressVisible = false;
      this.downloadingFile = null;
      this.progress = 0;
      this.downloadStatus = ''; // 只使用Element UI允许的值
      this.bytesInfo = '';
      this.isDownloading = false;
    },
    isValidString(str) {
      return str !== null && str !== undefined && str.trim() !== '';
    },
    async downloadFile() {
      if (this.isDownloading) {
        this.$message({
          message: '已有文件正在下载中，请等待下载完成或取消当前下载。',
          type: 'warning'
        });
        return;
      }
      
      this.isDownloading = true;
      this.fileUrlShow = false;
      this.loadingVisible = true;
      this.progressVisible = true;
      this.progress = 0; // 初始化为0%而不是Infinity
      this.downloadStatus = ''; // 确保是Element UI支持的值
      this.bytesInfo = '准备下载中...';

      let urlDown = '';
      if (this.fileUrlData.attchAddr.includes("http")) {
        urlDown = this.fileUrlData.attchAddr;
      } else {
        urlDown = this.$SOURCEURL + this.fileUrlData.attchAddr;
      }
      
      // 确定文件名
      let fileName = 'IC_Package&Test_Install.exe'; // 默认文件名
      // 从URL中提取文件名
      const urlFileName = urlDown.split('/').pop();
      if (urlFileName && urlFileName.includes('.')) {
        fileName = urlFileName;
      }
      
      try {
        // 检查是否存在CORS或迅雷拦截问题，决定下载方式
        if (this.downloadMethod === 'direct') {
          // 标准的fetch方式下载，有进度显示
          this.abortController = new AbortController();
          const { signal } = this.abortController;
          
          // 尝试获取文件大小
          this.bytesInfo = '获取文件信息...';
          
          try {
            // 先尝试HEAD请求获取文件大小，可能会因CORS失败
            const headResponse = await fetch(urlDown, { 
              method: 'HEAD',
              signal,
              mode: 'cors',
              credentials: 'omit' // 不发送cookies，可能减少CORS问题
            }).catch(() => null);
            
            const contentLength = headResponse && headResponse.ok ? 
              +headResponse.headers.get('Content-Length') : 0;
              
            // 开始实际下载
            this.bytesInfo = '开始下载...';
            const response = await fetch(urlDown, { 
              signal,
              mode: 'cors',
              credentials: 'omit'
            });
            
            if (!response.ok) throw new Error('网络错误');
            
            const reader = response.body.getReader();
            // 如果HEAD请求未能获取大小，尝试从GET请求获取
            const totalLength = contentLength || +response.headers.get('Content-Length') || 0;
            let receivedLength = 0;
            
            const chunks = [];
            let lastProgressUpdate = Date.now();
            let lastSpeed = 0;
            let lastBytes = 0;
            let lastTime = Date.now();
            
            while (true) {
              const { done, value } = await reader.read();
              
              if (done) {
                // 确保下载完成时进度为100%
                this.progress = 100;
                this.downloadStatus = 'success';
                this.bytesInfo = this.formatBytes(totalLength) + ' (下载完成)';
                break;
              }
              
              chunks.push(value);
              receivedLength += value.length;
              
              // 限制更新频率，避免UI卡顿
              const now = Date.now();
              if (now - lastProgressUpdate > 200) {
                // 计算下载速度
                const timeDiff = now - lastTime;
                if (timeDiff > 0) {
                  const bytesDiff = receivedLength - lastBytes;
                  lastSpeed = (bytesDiff * 1000) / timeDiff; // 字节/秒
                  lastBytes = receivedLength;
                  lastTime = now;
                }
                
                const speedText = lastSpeed > 0 
                  ? ` - ${this.formatBytes(lastSpeed)}/秒` 
                  : '';
                
                if (totalLength > 0) {
                  // 正常情况：有Content-Length
                  this.progress = Math.min(Math.floor((receivedLength / totalLength) * 100), 99);
                  this.bytesInfo = `${this.formatBytes(receivedLength)} / ${this.formatBytes(totalLength)}${speedText}`;
                } else {
                  // 异常情况：没有Content-Length
                  this.progress = 99; // 保持在99%直到完成
                  this.bytesInfo = `已下载: ${this.formatBytes(receivedLength)}${speedText}`;
                }
                lastProgressUpdate = now;
                await this.$nextTick();
              }
            }
            
            // 组装并下载文件
            const blob = new Blob(chunks);
            const url = window.URL.createObjectURL(blob);
            this.downloadWithATag(url, fileName);
            window.URL.revokeObjectURL(url);
            
            // 下载成功后保持对话框显示一段时间
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.$message({
              message: '文件下载成功！',
              type: 'success'
            });
          } catch (fetchError) {
            console.error('Fetch下载失败，尝试使用备用方案:', fetchError);
            
            // 如果fetch失败，切换到iframe下载方式
            this.downloadStatus = 'warning';
            this.bytesInfo = '直接下载失败，正在尝试其他方式...';
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 使用iframe下载
            this.downloadWithIframe(urlDown);
            
            // 让用户知道已经开始下载，但无法跟踪进度
            this.progress = 100;
            this.downloadStatus = 'success';
            this.bytesInfo = '已启动下载，请检查浏览器下载栏';
            
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } else if (this.downloadMethod === 'iframe') {
          // 直接使用iframe下载，不显示进度
          this.progress = 99;
          this.bytesInfo = '正在启动下载...';
          this.downloadWithIframe(urlDown);
          
          // 假装下载完成
          await new Promise(resolve => setTimeout(resolve, 1500));
          this.progress = 100;
          this.downloadStatus = 'success';
          this.bytesInfo = '已启动下载，请检查浏览器下载栏';
          
          await new Promise(resolve => setTimeout(resolve, 1500));
        } else {
          // 使用a标签下载
          this.progress = 99;
          this.bytesInfo = '正在启动下载...';
          this.downloadWithATag(urlDown, fileName);
          
          // 假装下载完成
          await new Promise(resolve => setTimeout(resolve, 1500));
          this.progress = 100;
          this.downloadStatus = 'success';
          this.bytesInfo = '已启动下载，请检查浏览器下载栏';
          
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('下载被取消');
          this.downloadStatus = 'warning';
          this.bytesInfo = '下载已取消';
        } else {
          console.error('下载失败:', error);
          // 确保status只使用Element UI允许的值
          this.downloadStatus = 'exception';
          this.bytesInfo = '下载失败: ' + (error.message || '未知错误');
          
          // 最后尝试直接使用a标签下载
          try {
            this.bytesInfo = '尝试浏览器直接下载...';
            this.downloadWithATag(urlDown, fileName);
          } catch (e) {
            console.error('所有下载方式均失败:', e);
          }
          
          this.$message({
            message: '下载失败: ' + (error.message || '未知错误'),
            type: 'error'
          });
        }
        // 在错误情况下保持对话框显示一段时间
        await new Promise(resolve => setTimeout(resolve, 2000));
      } finally {
        if (this.isDownloading) { // 仅在需要时更新 UI
          this.loadingVisible = false;
          this.progressVisible = false;
          this.isDownloading = false;
          this.abortController = null;
          this.progress = 0;
          this.downloadStatus = '';
          this.bytesInfo = '';
        }
      }
    },
    // 新增格式化字节大小的方法
    formatBytes(bytes, decimals = 2) {
      if (bytes === 0) return '0 字节';
      
      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = ['字节', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
      
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },
    init() {
      this.loading = true;
      this.$http.virtualCourseList().then((res) => {
        this.loading = false;
        this.cardData = res.data;
        this.tableData = res.data;
        this.muluData = [{ name: "主目录", id: 0 }];
      });
    },
    handleVideoLoad(item) {
      if (item.videoSrc) {
        // 注释掉HLS相关代码，直接播放原始视频
        // const isHLS = item.videoSrc.endsWith('.m3u8') || item.videoSrc.includes('/hls/');
        
        // 直接使用原始视频源
        const videoUrl = this.$SOURCEURL + item.videoSrc;
        console.log('使用原始视频源:', videoUrl);
        
        // 直接设置视频源并显示播放器
        this.currentVideoSrc = videoUrl;
        this.videoPoster = item.pic ? this.$SOURCEURL + item.pic : '';
        this.currentVideoTitle = item.name;
        
        // 延迟显示对话框，避免DOM未就绪
        setTimeout(() => {
          this.videoDialogVisible = true;
          this.showPlayer = true;
        }, 100);
        
        // 注释掉HLS转换相关代码
        /*
        if (isHLS) {
          // 直接使用HLS视频源
          videoUrl = this.$SOURCEURL + item.videoSrc;
          console.log('使用HLS视频源:', videoUrl);
          
          // 直接设置视频源并显示播放器
          this.currentVideoSrc = videoUrl;
          this.videoPoster = item.pic ? this.$SOURCEURL + item.pic : '';
          this.currentVideoTitle = item.name;
          
          // 延迟显示对话框，避免DOM未就绪
          setTimeout(() => {
            this.videoDialogVisible = true;
          }, 100);
        } else {
          // 原始视频URL (仅用于记录)
          const originalUrl = this.$SOURCEURL + item.videoSrc;
          console.log('原始视频源:', originalUrl);
          
          // 显示加载中对话框
          this.currentVideoTitle = item.name + ' (正在转换视频格式，请稍候...)';
          this.videoPoster = item.pic ? this.$SOURCEURL + item.pic : '';
          this.videoDialogVisible = true;
          this.showPlayer = false;
          
          // 调用后端API实时转换视频为HLS格式
          this.$http.videoConvertToHLS({
            videoPath: item.videoSrc
          }).then(res => {
            if (res && res.code === 200) {
              const hlsUrl = this.$SOURCEURL + res.data.hlsUrl;
              console.log('视频已转换为HLS格式:', hlsUrl);
              
              // 设置转换后的视频源
              this.currentVideoSrc = hlsUrl;
              this.currentVideoTitle = item.name; // 去掉"正在转换"的提示
              
              // 显示播放器
              setTimeout(() => {
                this.showPlayer = true;
              }, 300);
            } else {
              console.error('视频转换失败:', res);
              this.$msg('视频转换失败，请稍后再试', 'error');
              this.videoDialogVisible = false;
            }
          }).catch(err => {
            console.error('视频转换请求失败:', err);
            this.$msg('视频转换请求失败，请稍后再试', 'error');
            this.videoDialogVisible = false;
          });
        }
        */
      } else {
        return this.$msg("请先完善数据的访问地址", "warning");
      }
    },
    closeVideoDialog() {
      // 重置播放器状态
      this.showPlayer = false;
      setTimeout(() => {
        this.currentVideoSrc = '';
      }, 300);
    },
    handleDialogOpened() {
      // 对话框打开后延迟显示播放器，确保DOM已渲染
      setTimeout(() => {
        this.showPlayer = true;
      }, 300);
    },
    onVideoPlay() {
      console.log('视频开始播放');
    },
    onVideoPause() {
      console.log('视频暂停');
    },
    onVideoEnded() {
      console.log('视频播放完成');
      this.videoDialogVisible = false; // 播放结束后关闭对话框
    },
    onVideoError(error) {
      console.error('视频播放错误:', error);
      this.$msg('视频播放出错，请稍后再试', 'error');
      this.videoDialogVisible = false;
    },
    handleProgramWebLoad(item) {
      if (item.PathWeb && item.PathWeb.webUrl) {
        // let form = {};
        // if (item.virtualParentId) {
        //   form["deviceId"] = item.virtualId; // 设置设备ID
        //   form["virtualprocessId"] = item.virtualParentId; // 设置虚拟进程ID
        //   form["status"] = "0"; // 设置状态
        // } else {
        //   form["virtualprocessId"] = item.virtualId; // 仅设置虚拟进程ID
        //   form["status"] = "0"; // 设置状态
        // }
        // this.$http.userOperAdd(form).then((res) => { });
        let mode = 4
        let process = 1
        if (item.virtualParentId == 0) {
          mode = 4
          process = item.virtualId
        } else {
          mode = 1
          process = item.virtualParentId
        }

        return window.open(
          item.PathWeb.webUrl +
          `?access_token=${this.$store.getters["user/accessToken"]
          }&asset=${item.assetName
          }&mode=${mode
          }&process=${process
          }&device=${item.virtualId
          }`,
          "_blank"
        );
      } else {
        return this.$msg("请先联系管理员,完善web课程地址", "warning");
      }

    },
    handleProgramLoad(item) {
      if (!item.PathPrefix || !item.PathPrefix.prefixName) {
        return this.$msg("请先完善程序启动协议路径", "warning");
      }
      this.downloadFileName = item.PathPrefix.attchName;

      let url = "";
      let urlNew = "";
      if (item.virtualParentId === 0) {
        url += item.virtualId + "|" + 3 + "|1|" + this.$store.getters["user/accessToken"];
      } else {
        url += item.virtualParentId + "|" + 1 + "|" + item.virtualId + "|" + this.$store.getters["user/accessToken"];
      }
      url += "|" + window.client_ip; // 用"|"分割// 将最终构造的 URL 作为自定义协议的内容
      
      // 检测是否是Firefox浏览器
      const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
      
      // 获取浏览器内核信息
      const browser = this.$getBrowserKernelVersion();
      
      // 暂时只针对谷歌内核130及以上的路由器做处理(加?),低于130内核的还是沿用以前的方式启动
      if (browser.isChromeAbove130 || isFirefox) {
        // 对于Firefox和Chrome 130+使用相同的URL格式
        urlNew += (item.PathPrefix.prefixName + "?" + url);
      } else {
        urlNew += (item.PathPrefix.prefixName + url);
      }
      
      // URL编码，避免特殊字符问题
      urlNew = encodeURI(urlNew);
      
      // 统一使用protocolCheck方法处理协议调用
      protocolCheck(
        urlNew,
        () => {
          // 协议启动失败，处理下载逻辑
          if (this.isDownloading) {
            this.$message({
              message: '已有文件正在下载中，请等待下载完成。',
              type: 'warning'
            });
            return;
          }
          this.fileUrlData = item.PathPrefix;
          if (this.isValidString(this.fileUrlData.attchAddr)) {
            this.titleMarking = "课件下载"
            this.fileUrlShow = true;
            
            // 默认使用直接下载，发生错误时会自动切换到iframe或a标签下载
            this.downloadMethod = 'direct';
            
            // 清理之前的下载状态
            this.onCloseModal();
          } else {
            this.$message({
              message: '文件不存在...',
              type: 'warning'
            });
          }
        },
        () => {
          // 协议启动成功，可以在这里添加其他逻辑（如果需要）
          console.log('协议启动成功');
        },
        () => {
          // 浏览器不支持
          console.warn("此浏览器不支持此功能，请使用谷歌，火狐等浏览器");
          this.$msg(
            "此浏览器不支持此功能，请使用谷歌，火狐等浏览器",
            "warning"
          );
        }
      );
    },
    getImg(val, index) {
      // 检查当前层级，限制最多到第三层
      if (this.muluData.length >= 3) {
        this.$msg('已到最深层级，无法继续展开', 'warning');
        return;
      }

      if ((val.children && val.children.length > 0) || (val.VirtualData && val.VirtualData.length > 0)) {
        this.loading = true;
        this.muluData.push({
          name: val.name,
          id: val.id,
          level: val.level,
          index,
          VirtualData: val.VirtualData
        });
        this.cardData = [];
        if (val.VirtualData && val.VirtualData.length > 0) {
          this.cardData = val.VirtualData
        }
        if (val.children && val.children.length > 0) {
          this.cardData = [...this.cardData, ...val.children];
        }
        this.cardData.sort((a, b) => { return a.sortBy - b.sortBy })
      } else {
        this.$msg('没有下级数据', 'warning');
      }
      setTimeout(() => {
        this.loading = false;
      }, 500);
    },
    getMulu(val, i) {
      this.loading = true;
      if (val.name != this.muluData[this.muluData.length - 1].name) {
        this.muluData = this.muluData.slice(0, i + 1);
        // this.getImg(val, true);
        if (val.name !== "主目录") {
          this.cardData = []
          if (val.VirtualData && val.VirtualData.length > 0) {
            this.cardData = val.VirtualData
          }
          if (this.tableData[val.index].children && this.tableData[val.index].children.length > 0) {
            this.cardData = [...this.cardData, ...this.tableData[val.index].children];
          }
          this.cardData.sort((a, b) => { return a.sortBy - b.sortBy })
        } else {
          this.cardData = this.tableData;
        }
      }
      setTimeout(() => {
        this.loading = false;
      }, 500);
    },
    // 自定义进度条显示格式
    progressFormat(percentage) {
      // 如果是NaN或Infinity，显示0%
      if (isNaN(percentage) || !isFinite(percentage)) {
        return '0%';
      }
      return percentage + '%';
    },
    // 使用 a 标签下载文件，可以避免部分浏览器的拦截
    downloadWithATag(url, fileName) {
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '';
      link.target = '_blank'; // 在新标签页打开
      link.rel = 'noopener noreferrer'; // 安全属性
      document.body.appendChild(link);
      link.click();
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);
    },
    // 使用iframe下载文件，避免CORS和拦截问题
    downloadWithIframe(url) {
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = url;
      document.body.appendChild(iframe);
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 5000); // 给足够的时间加载
    },
  },
};
</script>

<style lang="scss" scoped>
.progress-container {
  margin-top: 20px;
}

.container {
  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(40, 44, 52, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: #fff !important;
  }

  .loading-content {
    text-align: center;
    color: #fff !important;
  }

  .modal-content {
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中（如果需要） */
    height: 100%;
    /* 高度为100% */
    width: 100%;
    /* 宽度为100% */
  }

  .bread {
    font-size: 14px;
    color: #606266;
    margin-top: 10px;
    padding-left: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ebeef5;

    span {
      &:hover {
        // font-weight: bold;
        cursor: pointer;
        color: #22a1ff;
      }
    }
  }

  .rowBox {
    padding: 0 5px;
    padding-top: 10px;
  }

  .el-card {
    &:hover {
      box-shadow: 0 2px 12px 0 #22a1ff;
    }

    .box {
      width: 100%;
      cursor: pointer;

      .demo {
        // background-color: rgb(184, 189, 190);
        text-align: center;

        .el-image {
          height: 250px;
          object-fit: cover;
          display: block;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .infoBox {
        color: #78849b;
        font-size: 14px;
        padding: 5px 20px;
        height: 55px;
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      p {
        height: 30px;
        line-height: 30px;
        padding: 0 20px;
        margin-top: 5px;
        margin-bottom: 10px;

        // text-align: center;
        .remark {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          // white-space: nowrap;
        }

        .prefix {
          float: left;
          display: inline-block;

          .el-tag {
            margin-top: 3px;
          }
        }

        .button-exe {
          float: right;

          &:hover {
            text-decoration: underline;
          }
        }

        .button-video {
          float: right;
          margin-right: 10px;

          &:hover {
            text-decoration: underline;
          }
        }

        span {
          font-size: 14px;
          // color: #78849b;
          display: inline-block;
          float: left;

          &:nth-child(2) {
            float: right;
          }
        }
      }
    }
  }

  .download-progress-container {
    padding: 10px 0;
  }
  
  .download-status-text {
    margin-top: 8px;
    font-size: 13px;
    color: #606266;
    text-align: center;
  }
  
  .download-actions {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }
}
</style>
<style lang="scss">
.el-tooltip__popper {
  max-width: 45%;
}

@media only screen and (min-width: 2560px) {
  .el-col.course-col-xll {
    width: 16.666666%;
  }
}

.video-container {
  width: 100%;
  height: calc(100% - 40px);
  /* 留出控制栏空间 */
  min-height: 450px;
  position: relative;
  overflow: visible !important;
  /* 修改为可见溢出，确保控件不被裁剪 */
}

.player-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.player-loading i {
  font-size: 32px;
  color: #409EFF;
  margin-bottom: 10px;
}

.player-loading p {
  color: #606266;
  font-size: 14px;
}

/* 确保vxe-modal内容区域有足够高度 */
.vxe-modal--wrapper .vxe-modal--body {
  height: calc(100% - 52px) !important;
  overflow: visible !important;
  /* 允许内容溢出 */
}

/* 解决视频播放器控制栏被遮挡问题 */
.vxe-modal.video-modal .vxe-modal--content {
  overflow: visible !important;
}

.vxe-modal.video-modal .vxe-modal--body {
  padding-bottom: 40px !important;
  /* 为控制栏预留空间 */
}

/* 全局样式，确保西瓜播放器控制栏正常显示 */
.vxe-modal.video-modal {
  overflow: visible !important;

  .vxe-modal--content {
    overflow: visible !important;
  }

  .vxe-modal--body {
    overflow: visible !important;
    padding-bottom: 50px !important;
    /* 给控制栏留出空间 */
  }

  .video-container {
    overflow: visible !important;
    position: relative;
    padding-bottom: 40px;
    /* 控制栏空间 */
  }
}

/* 确保播放器控制栏正常显示 */
.xgplayer .xgplayer-controls {
  position: absolute !important;
  bottom: 0 !important;
  z-index: 99999 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 在外层添加全局样式 */
.vxe-modal {
  &.video-modal {
    .vxe-modal--content {
      position: relative !important;
    }
  }
}

/* 避免vxe-modal的z-index影响控制栏 */
.vxe-modal--wrapper {

  .xgplayer .xgplayer-controls,
  .xgplayer .xgplayer-progress,
  .xgplayer .xgplayer-play,
  .xgplayer .xgplayer-time,
  .xgplayer .xgplayer-volume,
  .xgplayer .xgplayer-fullscreen {
    z-index: 2147483647 !important;
    /* 最大z-index值 */
  }
}
</style>




