const { Questionnaire, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(Questionnaire.tableAttributes)

class QuestionnaireService {
  static async create (req, res) {
    let checkDate = await Questionnaire.findOne({
      where: { name: req.body.name },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此问卷名已存在' })
    let data = await Questionnaire.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }

  static async update (req, res) {
    if (!req.body.formKey) return res.sendSuccess({ code: -1, msg: '数据id缺失' })
    let data = await Questionnaire.update(req.body, {
      where: {
        formKey: req.body.formKey
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }

  static async delete (req, res) {
    const { id } = req.params
    let data = await Questionnaire.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async getOne (req, res) {
    const { questionnaireId } = req.params
    let data = await Questionnaire.findByPk(questionnaireId, {
      include: [
        {
          model: QuestionnaireAnswer,
          as: 'questionAnswers',
        },
      ]
    })
    res.sendSuccess({ code: 200, data, msg: '获取成功' })
  }

  static async list (req, res) {
    if (!req.query.page && !req.query.pageSize) return res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    let where = $LikeWhere(req, res, fields)
    let { rows, count } = await Questionnaire.findAndCountAll({
      where,
      offset: (req.query.page - 1) * req.query.pageSize,
      limit: req.query.pageSize,
      order: [
        ['createTime', 'DESC'],
      ],
      raw: true
    })
    res.sendSuccess({ code: 200, data: rows, total: count })
  }

  static async option (req, res) {
    let data = await Questionnaire.findAll({
      attributes: [['formKey', 'value'], ['name', 'label']],
      order: [
        ['createTime', 'DESC'],
      ],
      raw: true
    })
    res.sendSuccess({ code: 200, data })
  }

  static async byId (req, res) {
    if (!req.query.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await Questionnaire.findOne({
      where: {
        formKey: req.query.id
      },
      raw: true
    })
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = QuestionnaireService
