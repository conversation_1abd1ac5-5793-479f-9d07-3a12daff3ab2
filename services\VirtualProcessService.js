const { VirtualProcess, VirtualProcessDevice, VirtualProcessDeviceOper, sequelize: $seq } = require('../models')
const { handleTree } = require('../utils/index')

function getLevelCode (str, splitStr) {
  let strOne
  if (!splitStr) {
    strOne = parseInt(str)
  } else {
    strOne = parseInt(str.substr(splitStr.length))
  }
  let num = strOne + 1
  switch ((num + '').length) {
    case 1:
      return splitStr + '000' + num
    case 2:
      return splitStr + '00' + num
    case 3:
      return splitStr + '0' + num
    case 4:
      return splitStr + num
  }
}
class VirtualProcessService {
  static async create (req, res) {
    let { name, level, parentId } = req.body
    let checkDate = await VirtualProcess.findOne({ where: { homePageName } })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此菜单名称已存在' })
    let maxLevelCode
    if (!levelCode || !parentId) {
      maxLevelCode = await VirtualProcess.max('levelCode', { where: { parentId: 0 } })
    } else {
      maxLevelCode = await VirtualProcess.max('levelCode', { where: { parentId } })
    }

    let code = ''
    if (!parentId && !maxLevelCode) {
      code = '0001'
    } else if (!parentId && maxLevelCode) {
      code = getLevelCode(maxLevelCode, '')
    } else if (parentId && maxLevelCode) {
      code = getLevelCode(maxLevelCode, levelCode ? levelCode : '')
    } else if (parentId && !maxLevelCode) {
      code = (levelCode ? levelCode : '') + '0001'
    } else {
      code = (levelCode ? levelCode : '') + '0001'
    }
    if (!code || (code && code.indexOf('NaN') > -1))
      return res.sendSuccess({ code: -1, msg: 'levelCode存在异常:' + code })
    req.body['levelCode'] = code
    req.body['parentId'] = req.body.parentId || 0
    let data = await VirtualProcess.create(req.body)
    res.sendSuccess({ code: 200, data })
  }
  static async update (req, res) {
    const { homePageId, homePageName } = req.body
    let checkDate = await VirtualProcess.findOne({
      where: {
        homePageName,
        homePageId: {
          $ne: homePageId,
        },
      },
    })
    if (checkDate) return res.sendSuccess({ code: -1, msg: '此菜单名称已存在' })
    let data = await VirtualProcess.update(req.body, {
      where: { homePageId }
    })
    res.sendSuccess({ code: 200, data })
  }
  static async del (req, res) {
    const { homePageId } = req.params
    let data = await VirtualProcess.destroy({
      where: { homePageId },
    })
    res.sendSuccess({ code: 200, data })
  }
  static async homePageList (req, res) {
    let result = await VirtualProcess.findAll()
    let data = handleTree(result, 'menuId')
    // console.log(data, 'data')
    res.sendSuccess({ code: 200, data })
  }
  static async menuBeauList (req, res) {
    let result = await VirtualProcess.findAll()
    let data = handleTree(result, 'menuId')
    res.sendSuccess({ code: 200, data })
  }
  static async treeList (req, res) {
    let data = await VirtualProcess.findAll({
      include: {
        model: VirtualProcessDevice,
        as: 'children',
        // attributes:['id'],
        required: false,
        include: {
          model: VirtualProcessDeviceOper,
          as: 'children',
        }
      },
      order: [
        ['orderNum'],
        [$seq.col('`children`.orderNum')],
        [$seq.col('`children->children`.orderNum')]
      ]
    })
    res.sendSuccess({ code: 200, data })
  }
}

module.exports = VirtualProcessService
