//权限模块--按钮与接口权限
const RedisService = require("../services/RedisService")
const whiteArr = ['/dict/', '/common/', '/register/', '/system/getRoutes', '/token/', '/file/', '/echart/', '/statis/', 'user/login', 'user/logout', 'user/register', '/user/info', '/user/editInfo', '/user/pwdEdit','/user/uploadAvatar','/user/uploadUserAvatar',
  //刘工的webGL需要用到的接口
  "/processRecord/issueList",
  "/processRecord/queryQuestionById",
  "/processRecord/articleList",
  "/processRecord/queryIssueById",
  
  // AI聊天相关接口
  "/ai-chat/remaining",
  "/ai-chat/send",
  "/ai-chat/history"
]
let checkUrl = function (url, whiteArr) {
  return whiteArr.some(item => url.indexOf(item) > -1)
}

module.exports = async (req, res, next) => {
  try {
    let url = req.originalUrl

    //1.先判断请求地址是否存在/dict/字典接口或/common/公共接口或/file/文件上传或/echart/大屏统计相关接口   或 用户是超级管理员。存在直接通过
    if (checkUrl(url, whiteArr) || (req.user && req.user.roleId && (req.user.roleId == 1 || String(req.user.roleId).indexOf('1') > -1))) {
      next()
    } else {
      // if (req.user && req.user.userId != 1) return res.send({ code: -1, msg: '用户所属地区字段未绑定，无权限访问' })

      //2.从redis中获取权限接口列表
      let SysPermission = await RedisService.getSysPermission()
      let indexArr = []
      SysPermission.forEach(item => {
        if (url.indexOf(item.perms) > -1) {
          indexArr.push(item.menuId)
        }
      })
      if (indexArr.length != 0) {
        if (!req.user || (req.user && !req.user.roleId)) return res.sendSuccess({ code: 403, msg: '用户角色信息缺失，无权限访问' })
        //获取请求用户的roleId,从redis查询role的权限id列表。然后判断是否存在此权限
        let roleId = req.user.roleId
        let isNext = false
        if (typeof roleId === 'number' || typeof Number(roleId) === 'number') {
          let SysRolePermission = await RedisService.getSysRolePermission(roleId)
          for (let i = 0; i < indexArr.length; i++) {
            let menuId = indexArr[i]
            if ((SysRolePermission.menuIds && SysRolePermission.menuIds.indexOf(menuId) > -1) || (SysRolePermission.halfIds && SysRolePermission.halfIds.indexOf(menuId) > -1)) {
              isNext = true
              break
            }
          }
        } else if (typeof roleId === 'string') {
          let roleArr = roleId.split(',')
          for (let j = 0; j < indexArr.length; j++) {
            let menuId = indexArr[j]
            for (let i = 0; i < roleArr.length; i++) {
              let SysRolePermission = await RedisService.getSysRolePermission(roleArr[i])
              if ((SysRolePermission.menuIds && SysRolePermission.menuIds.indexOf(menuId) > -1) || (SysRolePermission.halfIds && SysRolePermission.halfIds.indexOf(menuId) > -1)) {
                isNext = true
                break
              }
            }
          }
        }

        if (isNext) {
          next()
        } else {
          return res.sendSuccess({ code: 403, msg: '无权限访问' })
        }

      } else {
        return res.sendSuccess({ code: 403, msg: '无权限访问' })
        // next()
      }
    }

  } catch (error) {
    next(error)
  }
}