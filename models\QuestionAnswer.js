'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class QuestionAnswer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      this.belongsTo(models.Question, { foreignKey: 'questionId' })
      this.belongsTo(models.UserAnswer, { foreignKey: 'questionId', constraints: false })
    }
  }
  QuestionAnswer.init(
    {
      // level:{
      //   type: DataTypes.VIRTUAL,
      //   get() {
      //     return 1
      //   },
      // },
      answerId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "选项ID"
      },
      answerContent: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "题目内容"
      },
      isRight: {
        type: DataTypes.CHAR(1),
        allowNull: false,
        defaultValue: 1,
        comment: "正确答案（1正确2不正确）"
      },
      answerNum: {
        type: DataTypes.STRING(64),
        allowNull: true,
        comment: "答案序号"
      },
      // orderNum: {
      //   type: DataTypes.INTEGER,
      //   allowNull: true,
      //   defaultValue: 0,
      //   comment: "显示顺序"
      // },      
      // createTime: {
      //   type: DataTypes.DATE,
      //   defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      //   allowNull: true,
      //   comment: "创建时间"
      // },
      // updateTime: {
      //   type: DataTypes.DATE,
      //   defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      //   allowNull: true,
      //   comment: "更新时间"
      // },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'QuestionAnswer',
      tableName: 'zk_question_answer',
      comment:'问题答案表'
    }
  )
  return QuestionAnswer
}
