<template>
  <div class="container">
    <el-card>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-image style="width: 100%; height: 175px" :fit="'contain'"
            :src="courseInfo.coursewarePic ? $SOURCEURL + courseInfo.coursewarePic : ''">
            <div slot="error" class="el-image__error">
              <i class="el-icon-picture-outline" style="min-width: 175px; font-size: 32px"></i>
            </div>
          </el-image>
        </el-col>
        <el-col :span="18">
          <div>
            <h3>{{ courseInfo.coursewareName }}</h3>
            <p>发布人：{{ courseInfo.createBy }}</p>
            <p>发布时间：{{ courseInfo.updateTime }}</p>
          </div>
          <div></div>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="courseLearn-detail-bottom">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="课程概述" name="first">
          <div v-html="courseInfo.coursewareContent"></div>
        </el-tab-pane>
        <el-tab-pane label="学习资料" name="second">
          <ul class="sourceInfo">
            <li v-for="(item, i) in courseInfo.courseSources" :key="i" :title="item.sourceName">
              <a class="download-link" :href="item.sourceUrl ? $SOURCEURL + item.sourceUrl : ''"
                :title="item.sourceName" :download="item.sourceName || 'download'"
                @click.prevent="handleDownload(item.sourceUrl, item.sourceName)">
                <i class="el-icon-download"></i> {{ item.sourceName }}
              </a>
            </li>
          </ul>
        </el-tab-pane>
        <el-tab-pane label="评论" name="third" style="padding: 0 20px" v-permissions="['tipComment/list']">
          <ul class="commentBox">
            <li class="commentNode" v-for="(item, i) in commentData" :key="i">
              <div class="avater">
                <el-image style="width: 50px; height: 50px"
                  :src="item.avatar ? $SOURCEURL + item.avatar : require('@/assets/img/avater.jpg')">
                </el-image>
              </div>
              <div class="commentInfo">
                <div class="head">
                  <span class="head-left">{{ item.userName }}</span>
                  <span class="head-right">{{ item.createTime }}</span>
                </div>
                <div class="foot">
                  {{ item.content }}
                </div>
              </div>
              <div class="commentActions">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  @click="handleDelete(item)"
                  v-permissions="['tipComment/delete']"
                  class="delete-btn"
                >删除</el-button>
              </div>
            </li>
          </ul>

          <el-pagination @size-change="getComment" @current-change="getComment" :current-page="tablePage.page"
            :page-sizes="[10, 20, 50, 100]" :page-size="tablePage.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="tablePage.total">
          </el-pagination>

          <div style="margin-top:15px">
            <el-form :model="formData" :rules="$rules.coursewareRules" ref="formRef" label-width="120px"
              class="demo-formData">
              <el-form-item label="评论" prop="coursewareContent" class="coursewareContent">
                <el-input type="textarea" v-model="formData.content"></el-input>
              </el-form-item>
              <el-form-item style="text-align: center">
                <el-button type="primary" @click="save" v-permissions="['tipComment/add']">发布评论</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane label="AI助手" name="fourth" style="padding: 0 20px">
          <ai-chat 
            :courseId="$route.query.id"
            :courseName="courseInfo.coursewareName"
          ></ai-chat>
        </el-tab-pane> -->
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import AiChat from "@/components/AiChat.vue";

export default {
  name: "Detail",
  components: {
    AiChat
  },
  data() {
    return {
      activeName: "first",
      loading: false,
      courseInfo: {},
      formData: {
        articleId: null,
        content: "",
      },
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      commentData: [],
      formSearch: {
        articleId: null,
        page: 1,
        pageSize: 20,
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    handleDownload(url, name) {
      if (!url) {
        this.$message.error('文件地址无效');
        return;
      }
      const fullUrl = this.$SOURCEURL + url;
      const link = document.createElement('a');
      link.href = fullUrl;
      link.download = name || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    getData() {
      this.loading = true;
      let id = this.$route.query.id;
      this.formSearch.articleId = id;
      this.formData.articleId = id;
      this.$http.coursewareLearnOne(id).then((res) => {
        this.loading = false;
        this.courseInfo = res.data;
      });
    },
    getComment() {
      this.$http.commentList(this.formSearch).then((res) => {
        this.commentData = res.data;
        this.tablePage.total = res.total;
      });
    },
    handleClick(tab, event) {
      if (tab.name == "third") {
        this.getComment();
      }
    },
    save() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let form = this.formData;
          if (!form.id) {
            this.$http.commentAdd(form).then((res) => {
              this.$msg(res.msg, "success");
              this.getComment();
              this.formData.content = '';
            });
          } else {
            this.$http.commentEdit(form).then((res) => {
              this.$msg(res.msg, "success");
              this.getComment();
              this.formData.content = '';
            });
          }
        } else {
          return false;
        }
      });
    },
    handleDelete(item) {
      this.$confirm('确定删除此评论吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
      // alert(item)
      console.log(item)
        this.$http.commentDel(item.commentId).then((res) => {
          this.$msg(res.msg, 'success');
          this.getComment();
        }).catch(() => {
          this.$msg('删除失败', 'error');
        });
      }).catch(() => {});
    },
    close() {
      this.$resetForm("formRef");
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  .download-link {
    color: #409eff;
    font-size: 16px;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s ease;
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
    .el-icon-download {
      margin-right: 5px;
      vertical-align: middle;
    }
  }
  .bread {
    font-size: 14px;
    color: #606266;
    margin-top: 10px;
    padding-left: 5px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    span {
      &:hover {
        color: #22a1ff;
      }
    }
  }
  .courseLearn-detail-bottom ::v-deep {
    .el-tabs__item {
      font-size: 16px;
    }
    .sourceInfo {
      padding-left: 0;
      margin-top: 0;
      color: #22a1ff;
      li {
        font-size: 16px;
        margin-bottom: 10px;
        padding: 10px 10px;
        background-color: #ebeef5;
        border-radius: 4px;
        cursor: pointer;
      }
    }
    .commentBox {
      height: 40vh;
      padding-left: 0;
      margin-top: 0;
      overflow-y: auto;
      .commentNode {
        display: flex;
        align-items: flex-start;
        min-height: 50px;
        padding: 12px 15px;
        border-bottom: 1px solid #ccc;
        .avater {
          display: inline-block;
          vertical-align: top;
          width: 50px;
          height: 50px;
          border-radius: 50%;
          overflow: hidden;
        }
        .commentInfo {
          display: inline-block;
          margin-top: -10px;
          margin-left: 20px;
          width: calc(100% - 150px);
          min-height: 50px;
          .head {
            height: 32px;
            line-height: 32px;
            color: #9199b6;
            overflow: hidden;
            .head-left {
              font-size: 16px;
              font-weight: bold;
              color: #000;
            }
            .head-right {
              margin-left: 20px;
            }
          }
          .foot {
            font-size: 16px;
          }
        }
        .commentActions {
          margin-left: 20px;
          display: flex;
          align-items: center;
          .delete-btn {
            padding: 6px 12px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.3s ease;
            &:hover {
              background-color: #ff4d4f;
              border-color: #ff4d4f;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }
}
 
.el-tooltip__popper {
  max-width: 45%;
}
</style>