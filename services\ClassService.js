const { Class, Grade, sequelize: $seq, $LikeWhere } = require('../models')
const fields = Object.keys(Class.tableAttributes)
class ClassService {
  static async create (req, res) {
    let data = await Class.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await Class.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await Class.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { page, pageSize, className, gradeId } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await Class.findAndCountAll({
        attributes: {
          include: [[$seq.col('Grade.gradeName'), 'gradeName']]
        },
        include: {
          model: Grade,
          attributes: []
        },
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      let data = await Class.findAll({
        where
      })
      res.sendSuccess({ code: 200, data })
    }
  }
}

module.exports = ClassService
