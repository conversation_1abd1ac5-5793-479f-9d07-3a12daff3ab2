<template>
  <div class="updloadCl">
    <el-row :gutter="20">
      <el-col :span="8">
        <!-- 初始化流程 -->
        <el-card class="box-card" style="text-align: center">
          <div slot="header" class="clearfix">
            <span>初始化流程</span>
          </div>
          <el-upload class="upload-demo" :headers="{ token: $store.getters['user/accessToken'] }" drag :action="$BASEURL+'/file/vrProcessUpload'" multiple accept=".txt">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传txt文件</div>
          </el-upload>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card" style="text-align: center">
          <div slot="header" class="clearfix">
            <span>初始化设备工艺</span>
          </div>
          <div class="grid-content bg-purple">
            <el-upload class="upload-demo" :headers="{ token: $store.getters['user/accessToken'] }" drag :action="$BASEURL+'/file/vrDeviceUpload'" multiple accept=".txt">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">只能上传txt文件</div>
            </el-upload>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card" style="text-align: center">
          <div slot="header" class="clearfix">
            <span>初始化操作流程</span>
          </div>

          <div class="grid-content bg-purple">
            <el-upload class="upload-demo" :headers="{ token: $store.getters['user/accessToken'] }" drag :action="$BASEURL+'/file/vrOperationUpload'" multiple accept=".txt">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">只能上传txt文件</div>
            </el-upload>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "ProcessDataImport",
  created () {
  }
};
</script>