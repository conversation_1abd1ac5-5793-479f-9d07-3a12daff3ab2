export default {
  bind (el, binding, vnode) {
    // if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
    //   //移动设备
    //   console.log(1111111);
    //   vnode.context.$refs.xModal.fullscreen = true
    // } else {
    //   vnode.context.$refs.xModal.fullscreen = false
    // }
  },
  update (el, binding) {
  },
};
