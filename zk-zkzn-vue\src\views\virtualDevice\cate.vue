<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="分类名称" prop="name">
        <el-input placeholder="请输入名称" v-model="formSearch.name" clearable @keyup.enter.native="getData('formSearch')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetFormSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid id="virtual_cate_tab" :custom-config="{ storage: true }" ref="xGrid" :id="'id'" :row-config="{ keyField: 'id' }" :columns="tableColumn" :data="tableData" keep-source :tree-config="{
        transform: true,
        rowField: 'id',
        parentField: 'pId',
        reserve: formSearch.reserveTree,
        accordion: formSearch.accordionTree,
        iconOpen: 'vxe-icon--arrow-bottom',
        iconClose: 'vxe-icon--arrow-right',
        iconLoaded: 'vxe-icon--refresh roll',
      }" :edit-config="{trigger: 'click' ,mode: 'row',showStatus:true}" :seq-config="{startIndex: (formSearch.page - 1) * formSearch.pageSize}" @sort-change="handleSortChange" v-loading="loading" :toolbarConfig="{size:'mini',custom: true,slots: {buttons: 'toolbar_left' }}">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['virtualCate/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
        <el-button type="primary" v-permissions="['virtualCate/edit']" plain icon="el-icon-success" @click="bulkSave" size="mini">保存</el-button>
        <!-- <el-button type="danger" plain  icon="el-icon-delete"  @click="handleDel(null)">批量删除</el-button> -->
      </template>

      <template v-slot:statusSlot="{ row }">
        <vxe-switch v-model="row.status" open-label="启用" :open-value="1" close-label="停用" :close-value="0" @change="statusChange(row)"></vxe-switch>
      </template>
      <template #imgSlot="{ row }">
        <el-image style="height: 48px; vertical-align: bottom" :fit="'contain'" :src="row.pic ? $SOURCEURL + row.pic : ''" :preview-src-list="srcList">
          <div slot="error" class="el-image__error">
            <i class="el-icon-picture-outline" style="min-width: 50px; font-size: 32px"></i>
          </div>
        </el-image>
      </template>

      <!-- 表格的侧边操作栏按钮 -->
      <template v-slot:operate="{ row }">
        <el-button type="text" v-permissions="['virtualCate/add']" icon="el-icon-plus" @click="handleChildAdd(row)">新增下级</el-button>
        <el-button type="text" v-permissions="['virtualCate/relevance']" icon="el-icon-plus" @click="$refs.relevanceModalRef.open(row)">关联课件</el-button>
        <el-button type="text" v-permissions="['virtualCate/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['virtualCate/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal width="800" height="650" :title="modalTitle" v-model="modalVisible" show-footer resize remember transfer @close="close">
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :data="formData" :items="formItem" :rules="$rules.classRules" title-width="120" title-align="right">
          <template #treeSelect="{ data }">
            <tree-select :options="menuOptions" v-model="data.pId" @select="treeSelectNode" :show-count="true" :normalizer="normalizer" placeholder="选择上级" />
          </template>
          <template #uploadImg="{ data }">
            <el-upload class="avatar-uploader" :headers="{ token: $store.getters['user/accessToken'] }" accept=".jpg,.png,.jpeg" :action="$BASEURL + '/file/upload'" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <div class="avaterBox">
                <img v-if="data.pic" :src="$SOURCEURL + data.pic" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div v-if="data.pic" class="avaterMask" @click.stop="delUploadImg">
                  <span>
                    <i class="el-icon-plus delete"></i>
                    <i class="el-icon-check success"></i>
                  </span>
                </div>
              </div>
            </el-upload>
          </template>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>

    <RelevanceModal ref="relevanceModalRef"></RelevanceModal>
  </div>
</template>

<script>
import RelevanceModal from "./components/relevanceModal.vue"
export default {
  name: "cate",
  components:{
    RelevanceModal
  },
  data () {
    return {
      normalizer (node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children,
        };
      },
      srcList: [],
      modalTitle: '新增信息',
      modalVisible: false,
      loading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tableColumn: [
        // { type: 'seq', width: 60, title: '序号' },
        { sortable: true, field: 'name', treeNode: true, align: "left", title: '分类名称', editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'pic', title: '图片', minWidth: 80, showOverflow: false, slots: { default: "imgSlot" } },
        { field: 'description', title: '简介', editRender: { name: '$input', props: { placeholder: '请输入简介' } } },
        { field: 'status', title: '状态', slots: { default: "statusSlot" } },
        { field: 'orderNum', title: '排序', editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'remark', title: '备注', editRender: { name: '$input', props: { placeholder: '请输入' } } },
        { title: '操作', width: 280, slots: { default: 'operate' }, align: 'center', fixed: 'right' },
      ],
      tableData: [],
      formData: {
        name: '',
        remark: '',
      },
      formItem: [
        { field: "pId", title: "上级", span: 24, slots: { default: "treeSelect" }, },
        { field: 'name', title: '分类名称', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入分类名称' } } },
        { field: 'pic', title: '图片', span: 24, slots: { default: "uploadImg" }, },
        { field: 'description', title: '简介', span: 24, itemRender: { name: '$textarea', props: { placeholder: '请输入简介' } } },
        { field: 'status', title: '状态', span: 24, itemRender: { name: "$radio", options: [{ label: "正常", value: 1 }, { label: "停用", value: 0 },], } },
        { field: 'orderNum', title: '排序', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入' } } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      formSearch: {
        name: undefined,
        page: 1,
        pageSize: 10
      },
      menuOptions: [],
    };
  },
  created () {
    this.getData();
  },
  methods: {
    getData (params) {
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.virtualCateList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        this.loading = false;
      });
    },
    bulkSave () {
      let record = this.$refs.xGrid.getUpdateRecords()
      this.$http.virtualCateEdit(record).then(res => {
        this.$msg('操作成功', 'success');
        this.getData();
      });
    },
    handleRelevance(row) {

    },
    //重置按钮
    resetFormSearch () {
      this.$resetForm('queryForm');
      if (this.$refs.xGrid) {
        this.$refs.xGrid.clearSort()
        this.formSearch.sortName = undefined
        this.formSearch.sortOrder = undefined
      }
      this.getData()
    },
    //分页
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    //排序
    handleSortChange ({ property, order }) {
      this.formSearch.sortName = property
      this.formSearch.sortOrder = order
      this.getData()
    },
    statusChange (row) {
      let form = {
        id: row.id,
        status: row.status,
      };
      this.loading = true
      this.$http.virtualCateEdit(form).then((res) => {
        this.$msg("操作成功", "success");
        this.getData()
      }).catch((err) => {
        row.status = row.status == 1 ? 0 : 1;
      });
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData
          if (!form.id) {
            this.$http.virtualCateAdd(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.virtualCateEdit(form).then(res => {
              this.$msg(res.msg, 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      this.$refs.formRef.clearValidate()
      this.modalVisible = false
    },
    getTreeselect () {
      this.$http.virtualCateOption().then((res) => {
        this.menuOptions = [];
        let menu = { id: 0, pId: 0, level: 0, name: "主类目", children: [] };
        menu.children = res.data;
        this.menuOptions.push(menu);
      });
    },
    treeSelectNode (node, id) {
      // this.formData.level = node.level + 1;
    },
    handleAdd () {
      this.getTreeselect();
      this.modalTitle = '新增信息';
      this.formData = {
        pId: 0,
        name: '',
        pic: '',
        description: '',
        status: 1,
        orderNum: 1,
        remark: '',
      };
      this.modalVisible = true;
    },
    handleChildAdd (row) {
      this.getTreeselect();
      this.modalTitle = "新增信息";
      this.formData = {
        pId: row.id,
        name: '',
        pic: '',
        description: '',
        status: 1,
        orderNum: 1,
        remark: '',
      };
      this.modalVisible = true;
    },
    handleEdit (row) {
      this.getTreeselect();
      this.modalTitle = '编辑信息';
      this.formData = JSON.parse(JSON.stringify(row))
      if (this.formData.children) {
        this.formData.children = undefined;
      }
      this.modalVisible = true;
    },
    handleDel (row) {
      let list = '';
      if (row == null) {
        let selectRecords = this.$refs.xGrid.getCheckboxRecords();
        if (selectRecords.length == 0) {
          return this.$msg('请选择删除项', 'warning');
        }
        selectRecords.forEach((item, i) => {
          if (i == selectRecords.length - 1) {
            list += item.id;
          } else {
            list += item.id + ',';
          }
        });
      } else {
        list = row.id;
      }
      this.$confirm('删除操作会将自身及子级全部删除，且关联课件的分类会重置为空，请确认是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.virtualCateDel(list).then(res => {
          this.$msg(res.msg, 'success');
          this.getData();
        });
      })
    },
    handleAvatarSuccess (res, file) {
      this.formData.pic = res.url;
      this.$message.success("上传成功！");
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过5MB!");
      }
      return isLt2M;
    },
    delUploadImg (e) {
      this.formData.pic = "";
    },
  }
};
</script>

<style lang="scss" scoped>
.formHidden {
  display: none;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 100%;
  height: 250px;
  /* height: auto; */
  display: block;
}

.avaterBox:hover .avaterMask {
  background-color: #f56c6c;
}

.avaterBox:hover .avaterMask .success {
  display: none;
}

.avaterBox:hover .avaterMask .delete {
  display: block;
}

.avaterMask {
  // width: 100%;
  // height: 250px;
  width: 80px;
  height: 80px;
  line-height: 40px;
  border-radius: 8px;
  transform: rotate(45deg);
  position: absolute;
  right: -40px;
  top: -40px;
  font-size: 20px;
  background-color: #67c23a;
  transition: all 0.4s;

  span {
    position: absolute;
    top: 55px;
    left: 30px;
    color: #fff;

    // width: 33.33%;
    // height: 100%;
    // line-height:inherit;
    // display: inline-block;
    // i {
    //   display: inline-block;
    //   line-height: 1;
    //   color: #fff;
    // }
    .delete {
      display: none;
    }

    .success {
      display: block;
      transform: rotate(-45deg);
    }
  }
}
</style>
