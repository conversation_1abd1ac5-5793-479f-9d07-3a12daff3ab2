const { Config } = require('../models')

class ConfigService {
  static async info (req, res) {   
    let data = await ConfigService.getInfo()
    res.sendSuccess({ code: 200, data })
  }

  static async getInfo () {
    let data = await Config.findOne({
      where: {
        id: 1
      }
    })
    if (!data) {
      data = await Config.create({ isRegister: 1 })
    }
    return data
  }

  static async update (req, res) {
    if(!req.body.id) return res.sendSuccess({code:-1,msg:'id参数缺失'})
    let data = await Config.update(req.body, {
      where: {
        id: req.body.id
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
}

module.exports = ConfigService
