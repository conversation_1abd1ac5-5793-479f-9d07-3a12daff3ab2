<template>
  <div class="formRender">
    <div class="title">{{title}}</div>
    <v-form-render ref="vFormRef" class="renderBox" :form-json="formJson" :form-data="formData" :option-data="optionData"></v-form-render>
    <div class="tool">
      <el-button type="primary" size="medium" @click="submitForm">保 存</el-button>
      <el-button size="medium" @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "questionnaireRender",
  data () {
    return {
      formJson: {},
      formData: {},
      optionData: {},
      formId: null,
      title: '',
      fieldJson: {}
    }
  },
  created () {
    let id = this.$route.query.id
    this.formId = id
  },
  mounted () {
    this.getData(this.formId)
  },
  methods: {
    getData (id) {
      if (!id) return this.$msg('id参数缺失', 'warning')
      this.$http.questionnaireById({ id }).then(res => {
        console.log(res, 'res');
        this.formJson = res.data.quesJson
        this.fieldJson = res.data.fieldJson
        this.title = res.data.name
        this.$refs.vFormRef.setFormJson(res.data.quesJson)
      })
    },
    submitForm () {
      this.$refs.vFormRef.getFormData().then(formData => {
        // Form Validation OK
        // alert(JSON.stringify(formData))
        let fieldArr = Object.keys(formData)
        fieldArr.forEach((item, i) => {
          let status = 0
          if (item.indexOf('radio') > -1) {
            status = 1
          }
          if (item.indexOf('checkbox') > -1) {
            status = 2
          }
          if (item.indexOf('select') > -1) {
            status = 3
          }
          if (status > 0) {
            let optionItem = []
            if (this.fieldJson[i].name == item) {
              optionItem = this.fieldJson[i].field.options.optionItems
            } else {
              let obj = this.fieldJson.find(item2 => item2.name == item)
              if (obj) {
                optionItem = obj.field.options.optionItems
              }
            }
            switch (status) {
              case 1:
                let findObj = optionItem.find(item3 => item3.value == formData[item])
                formData[item + '_label'] = findObj ? findObj.label : ''
                break;
              case 2:
                if (formData[item] && formData[item].length > 0) {
                  let str = ''
                  for (let x = 0; x < formData[item].length; x++) {
                    let findObj = optionItem.find(item4 => item4.value == formData[item][x])
                    if (findObj) {
                      str += findObj.label + '┋'
                    }
                  }
                  formData[item + '_label'] = str.substring(0, str.length - 1)
                } else {
                  formData[item + '_label'] = ''
                }
                break;
              case 3:
                if (Array.isArray(formData[item]) && formData[item].length > 0) {
                  let str = ''
                  for (let x = 0; x < formData[item].length; x++) {
                    let findObj = optionItem.find(item4 => item4.value == formData[item][x])
                    if (findObj) {
                      str += findObj.label + '┋'
                    }
                  }
                  formData[item + '_label'] = str.substring(0, str.length - 1)
                } else {
                  let findObj = optionItem.find(item3 => item3.value == formData[item])
                  formData[item + '_label'] = findObj ? findObj.label : ''
                }
                break;
            }
          }
        })
        let form = {
          formKey: this.formId,
          dataJson: formData
        }
        this.$http.questionnaireDataAdd(form).then(res => {
          this.$msg('保存成功')
        })

      }).catch(error => {
        // Form Validation failed
        this.$message.error(error + '，请按提示填写正确的内容')
      })
    },
    cancel () {
      this.$router.push('/questionnaire/index')
    }
  }
}
</script>

<style lang="scss" scoped>
.formRender ::v-deep .main-header {
  display: none !important;
}
.formRender {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-color: #f6f8f9;
  // height: calc(100vh - 130px);
  // height: 100%;
  // overflow-y: auto;
  overflow: hidden;
  .title {
    width: 900px;
    margin: auto;
    margin-top: 15px;
    text-align: center;
    height: 5vh;
    line-height: 5vh;
    font-size: 16px;
    font-weight: bold;
    color: #606266;
  }
  .renderBox {
    width: 900px;
    height: 85vh;
    margin: 0 auto;
    margin-bottom: 10px;
    padding: 20px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .tool {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    // position: absolute;
    // top: 0;
    // right: 10px;
  }
}
</style>