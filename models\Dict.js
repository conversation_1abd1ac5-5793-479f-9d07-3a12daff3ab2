'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Dict extends Model {
    static associate (models) {
      this.hasMany(models.DictData, { sourceKey:'dictCode',foreignKey: 'dictCode', constraints: false })
    }
  }
  Dict.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },
      dictName: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: '#',
        comment: "字典名称"
      },
      dictCode: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "字典编码"
      },      
      sortBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "显示顺序"
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "菜单状态（1正常 2停用）"
      },
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建人id"
      },
      remark: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "备注"
      },      
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Dict',
      tableName: 'sys_dict',
      comment: '字典表',
    }
  )
  return Dict
}
