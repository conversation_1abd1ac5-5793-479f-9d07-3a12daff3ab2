'use strict'
const { Model } = require('sequelize')
const { nanoid } = require('nanoid');
module.exports = (sequelize, DataTypes) => {
  class Questionnaire extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
    }
  }
  Questionnaire.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "id"
      },      
      name: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '',
        comment: "问卷名称"
      },
      formKey: {
        type: DataTypes.STRING(100),
        allowNull: false,
        defaultValue: () => nanoid(8),
        comment: "问卷key字符串"
      },
      quesJson: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '动态表单json',
      },
      fieldJson: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '动态表单字段json',
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: '状态1是启用2是禁用',
      },
      sortBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createById: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "创建人id"
      },
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'Questionnaire',
      tableName: 'zk_questionnaire',
      comment: '问卷表'
    }
  )
  return Questionnaire
}
