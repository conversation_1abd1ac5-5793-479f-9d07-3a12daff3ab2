<template>
  <div :class="'logo-container-' + layout">
    <router-link to="/">
      <!-- <vab-remix-icon v-if="logo" class="logo" :icon-class="logo" /> -->
      <img v-if="logo" :src="computedLogoSrc" alt="" :class="computedClass" />
      <!-- <span class="title" :class="{ 'hidden-xs-only': layout === 'horizontal' }" :title="title">
        {{ title }}
      </span> -->
      <!-- <span class="title" :class="{ 'hidden-xs-only': layout === 'horizontal' }" :title="title">
        <img :src="require('@/assets/logoha.png')" alt="" class="czk-logoha">
      </span> -->
    </router-link>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { logo } from "@/config/settings";

export default {
  name: "Logo",
  data() {
    return {
      title: this.$baseTitle,
    };
  },
  computed: {
    ...mapGetters({
      logo: "settings/logo",
      layout: "settings/layout",
      collapse: "settings/collapse", // 获取折叠状态
    }),
    computedLogoSrc() {
      // 根据折叠状态返回不同的图片路径
      return this.collapse
        ? require("@/assets/gkd.png") // 折叠时的图片
        : require("@/assets/kslogodNew.png"); // 默认图片
    },
    computedClass() {
      // 根据折叠状态返回不同的图片路径
      return this.collapse
        ? "czk-logo2" // 折叠时的图片
        : "czk-logo"; // 默认图片
    },
  },
};
</script>
<style lang="scss" scoped>
@mixin container {
  position: relative;
  height: $base-top-bar-height;
  overflow: hidden;
  line-height: $base-top-bar-height;
  background: $base-menu-background;
}

@mixin logo {
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 5px;
  color: $base-title-color;
  vertical-align: middle;
}

@mixin title {
  display: inline-block;
  overflow: hidden;
  font-size: 20px;
  font-weight: 600;
  // line-height: 55px;
  line-height: 24px;
  color: $base-title-color;
  text-overflow: ellipsis;
  // white-space: nowrap;
  vertical-align: middle;
}

.logo-container-horizontal {
  @include container;

  .logo {
    @include logo;
  }

  .title {
    @include title;
  }
}

.logo-container-vertical {
  @include container;

  height: $base-logo-height;
  line-height: $base-logo-height;
  text-align: center;

  .logo {
    @include logo;
  }

  .title {
    @include title;

    // max-width: calc(#{$base-left-menu-width} - 80px);
    max-width: calc(#{$base-left-menu-width} - 65px);
    height: 32px;
    // background-image: linear-gradient(135deg, #3e7bff, #06ccad);
    background: #bf9540;
    -webkit-background-clip: text;
    color: transparent;
    padding-left: 15px;
  }
}

.czk-logo {
  vertical-align: top;
  margin-top: 5px;
  width: 210px;
  height: 70px;
}
.czk-logo2 {
  vertical-align: top;
  margin-top: 10px;
  width: 50px;
  height: 50px;
}

.czk-logoha {
  // width: 50px;
  width: 100%;
  height: 60px;
}
</style>
