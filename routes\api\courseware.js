var express = require("express");
var router = express.Router();
const { nanoid } = require('nanoid')
const fs = require('fs')
const path = require('path')
const { tryAsyncErrors } = require("../../utils/index")
const { transactionTryAsyncErrors } = require("../../utils/transaction")
const Service = require("../../services/CoursewareService");

router.get('/list', tryAsyncErrors(Service.list))
router.get('/query', tryAsyncErrors(Service.query))
router.get('/list/:id', tryAsyncErrors(Service.getOne))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', transactionTryAsyncErrors(Service.update))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))
router.post('/sourceDelete', tryAsyncErrors(Service.sourceDelete))

module.exports = router;
