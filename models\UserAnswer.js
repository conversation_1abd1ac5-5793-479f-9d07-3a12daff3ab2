'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class UserAnswer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      // this.belongsTo(models.TipArticle,{foreignKey:'articleId',constraints:false})
      // this.belongsTo(models.User,{foreignKey:'userId',constraints:false})
      this.belongsTo(models.UserExam,{foreignKey:'examId',constraints:false})
      this.hasOne(models.Question,{sourceKey:'questionId',foreignKey:'questionId',constraints:false})
      this.hasMany(models.QuestionAnswer,{as:'questionAnswers',sourceKey:'questionId',foreignKey:'questionId',constraints:false})
    }
  }
  UserAnswer.init(
    {       
      // radioData:{
      //   type: DataTypes.VIRTUAL,
      //   get() {
      //     return ''
      //   },
      // },
      // checkboxData:{
      //   type: DataTypes.VIRTUAL,
      //   get() {
      //     return []
      //   },
      // },     
      userAnswerId: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "唯一ID"
      },
      examId:{
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "考试id"
      },
      userId:{
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "用户id"
      },      
      deviceOperationId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "设备操作id"
      },
      answerIds:{
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "用户选择答案"
      },
      questionId:{
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "题目id"
      },     
      createTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: true,
        comment: "更新时间"
      },
      // remark: {
      //   type: DataTypes.STRING(500),
      //   allowNull: true,
      //   defaultValue: "",
      //   comment: "备注"
      // },      
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'UserAnswer',
      tableName: 'zk_user_answer',
      comment:'用户答题表'
    }
  )
  return UserAnswer
}
