const { DictData, Role, Grade, Menu, $LikeWhere, sequelize: $seq } = require('../models')
const { setRedis, getRedis } = require('../utils/redisUtils')
const { handleTree } = require('../utils/index')
const dayjs = require('dayjs')
const { albToCn } = require("../utils/index");

class RedisService {
  /*  
  @ params 用于echart图统计配置项，false表示每次都重新访问数据库，true表示读redis缓存
  */
  static flag = false

  //所有权限接口列表
  static async setSysPermission () {
    let data = await Menu.findAll({
      attributes: ['menuId', 'perms'],
      where: {
        status: 0,
        menuType: 'F',
        perms: {
          $ne: null
        }
      },
      raw: true
    })
    setRedis('SysPermission', data)
    return data
  }
  static async getSysPermission () {
    let data = await getRedis('SysPermission')
    if (data && data.length > 0) return data
    return this.setSysPermission()
  }

  //角色权限id列表
  static async setSysRolePermission (roleId) {
    let data = await Role.findOne({
      attributes: ['roleId', 'menuIds', 'halfIds'],
      where: {
        roleId
      },
      raw: true
    })
    setRedis('SysRolePermission_' + roleId, data)
    return data
  }
  static async getSysRolePermission (roleId) {
    let data = await getRedis('SysRolePermission_' + roleId)
    if (data && data.menuIds) return data
    return this.setSysRolePermission(roleId)
  }

  //获取字典管理的相关字典数据
  static async setDictCodeOption (dictCode) {
    let data = await DictData.findAll({
      attributes: [['dictValue', 'value'], ['dictLabel', 'label']],
      where: {
        dictCode,
        status: 1,
      },
      raw: true
    })
    setRedis(`${dictCode}Option`, data)
    return data
  }
  static async getDictCodeOption (dictCode) {
    let data = await getRedis(`${dictCode}Option`)
    if (data && data.length > 0) return data
    return this.setDictCodeOption(dictCode)
  }

  //获取字典转换对象
  static async setDictCodeOptionObj (dictCode) {
    let data = await DictData.findAll({
      attributes: [['dictValue', 'value'], ['dictLabel', 'label']],
      where: {
        dictCode,
        status: 1,
      },
      raw: true
    })
    let obj = {}
    data.map(t => obj[t.value] = t.label)
    setRedis(`${dictCode}OptionObj`, obj)
    return obj
  }
  static async getDictCodeOptionObj (dictCode) {
    let obj = await getRedis(`${dictCode}OptionObj`)
    if (obj && Object.keys(obj).length != 0) return obj
    return this.setDictCodeOption(dictCode)
  }

  //角色列表
  static async setRoleOption () {
    let data = await Role.findAll({
      attributes: [['roleId', 'value'], ['roleName', 'label']],
      where: {
        status: 1,
        roleId: {
          $ne: 1
        }
      },
      raw: true
    })
    setRedis(`RoleOption`, data)
    return data
  }
  static async getRoleOption () {
    let data = await getRedis(`RoleOption`)
    if (data && data.length > 0) return data
    return this.setRoleOption()
  }

  //角色菜单列表
  static async setRoleMenuOption () {
    let result = await Menu.findAll({
      where: {
        status: 1,
      },
      order: ['sortBy']
    })
    let data = handleTree(result, 'menuId')
    setRedis(`RoleMenuOption`, data)
    return data
  }
  static async getRoleMenuOption () {
    let data = await getRedis(`RoleMenuOption`)
    if (data && data.length > 0) return data
    return this.setRoleMenuOption()
  }

  //届/年级列表
  static async setGradeOption () {
    let data = await Grade.findAll({
      attributes: [['id', 'value'], ['gradeName', 'label']],
      where: {
        // status: 1        
      },
      raw: true
    })
    setRedis(`GradeOption`, data)
    return data
  }
  static async getGradeOption () {
    let data = await getRedis(`GradeOption`)
    if (data && data.length > 0) return data
    return this.setGradeOption()
  }

}


module.exports = RedisService