const fs = require('fs');

module.exports = {
  fileDownload: (path, name, req, res) => {
    if (fs.existsSync(path)) {
      let stream = fs.createReadStream(path)
      let userAgent = (req.headers['user-agent'] || '').toLowerCase()
      let size = fs.statSync(path).size
      res.setHeader("Content-Length", size + "")
      res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition')

      if (userAgent.indexOf('msie') >= 0 || userAgent.indexOf('chrome') >= 0) {
        /*谷歌和IE浏览器下载*/
        res.writeHead(200, {
          'Content-Type': 'application/force-download;charset=utf-8',
          'Content-Disposition':
            'attachment; filename=' + encodeURIComponent(name),
        })
      } else if (userAgent.indexOf('firefox') >= 0) {
        /*火狐浏览器下载*/
        res.writeHead(200, {
          'Content-Type': 'application/force-download;charset=utf-8',
          'Content-Disposition':
            "attachment; filename*=\"utf8''" + encodeURIComponent(name) + '"',
        })
      } else {
        /* safari等其他非主流浏览器只能自求多福了 */
        res.writeHead(200, {
          'Content-Type': 'application/force-download;charset=utf-8',
          'Content-Disposition':
            'attachment; filename=' + new Buffer(name).toString('binary'),
        })
      }
      stream.pipe(res)
    } else {
      res.sendSuccess({ code: 404, success: false, msg: '文件不存在', error: 'file not exit' })
    }
  }
}