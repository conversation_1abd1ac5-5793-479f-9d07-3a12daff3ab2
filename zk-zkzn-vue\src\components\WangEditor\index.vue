<template>
  <div>
    <div style="border: 1px solid #ccc; margin-top: 10px;z-index: 999999999999999;">
      <!-- 工具栏 -->
      <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" />
      <!-- 编辑器 -->
      <Editor style="height: 480px; overflow-y: hidden;" :defaultConfig="editorConfig" v-model="content" @onChange="onChange" @onCreated="onCreated" />
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

export default {
  name: 'WangEditor',
  components: { Editor, Toolbar },
  props: {
    text: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      content: this.text, // 富文本内容
      imgCount: 0, // 已上传的图片数量
      imgMaxCount: 10, // 最大上传图片数量
      editor: null, // 编辑器实例
      toolbarConfig: {
        /* 显示哪些菜单，如何排序、分组 */
        toolbarKeys: [
          'headerSelect', '|', "indent", // 增加缩进
          "delIndent", // 减少缩进
          "justifyLeft", // 左对齐
          "justifyRight", // 右对齐
          "justifyCenter", // 居中对齐
          "justifyJustify", // 两端对齐
          '|', 'bold', 'underline', 'italic', 'color', 'bgColor', '|', 'fontSize', 'fontFamily', 'lineHeight', '|', 'bulletedList', 'numberedList', '|', 'emotion', 'insertLink', 'insertTable', 'codeBlock', 'divider', '|',
          {
            "key": "group-image",
            "title": "图片",
            "iconSvg": "<svg viewBox=\"0 0 1024 1024\"><path d=\"M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z\"></path></svg>",
            "menuKeys": [
              "insertImage",
              "uploadImage"
            ]
          },
          {
            "key": "group-video",
            "title": "视频",
            "iconSvg": "<svg viewBox=\"0 0 1024 1024\"><path d=\"M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z\"></path></svg>",
            "menuKeys": [
              "insertVideo",
              "uploadVideo"
            ]
          }, "clearStyle", "code",
          "fullScreen"
          // 'todo',
          // "insertLink", "editLink", "unLink", // 取消链接
          // "viewLink", // 查看链接 // 修改链接
        ],
      }, // 工具栏配置
      editorConfig: {
        placeholder: '请输入内容...', // 编辑器的占位符
        MENU_CONF: {
          uploadImage: {
            customUpload: this.FileUploadImage // 自定义图片上传方法
          },
          uploadVideo: {
            customUpload: this.FileUploadVideo // 自定义视频上传方法
            //   customUpload: async (file, insertFn) => {
            //     let resultUrl = await this.upqiniu(file, file.name);
            //     insertFn(resultUrl);
            //   },
          },
        },
        // toolbar: [
        //   'bold', 'italic', 'underline',
        //   'head', 'list', 'link', 'image', 'justify'
        //    // 不包括 'fullscreen'
        // ],
      },
      mode: 'default' // 编辑器模式，'default' 或 'simple'
    }
  },
  watch:{
    text() {
      this.content = this.text
    }
  },
  methods: {
    getContent() {
      return this.content
    },
    onCreated (editor) {
      this.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
    },
    onChange (editor) {
      this.content = this.editor.getHtml() // 获取编辑器内容并更新 data
    },
    FileUploadImage (file, insertFn) {
      if (this.imgCount >= this.imgMaxCount) {
        this.$message.warning(`图片最多上传${this.imgMaxCount}张`) // 超过最大上传数量提示
        return
      }
      let urlApi = this.$BASEURL
      let urlImage = this.$SOURCEURL
      var axios = require("axios");
      var FormData = require("form-data");
      var data = new FormData();
      data.append("file", file); // file 即选中的文件
      var config = {
        method: "post",
        url: urlApi + "/file/upload", //上传图片地址
        headers: {
          "Content-Type": "multipart/form-data",
          Accesstoken: localStorage.getItem("vue-admin-beautiful")
        },
        data: data
      };
      axios(config)
        .then(function (res) {
          let url = urlImage + res.data.url; //拼接成可浏览的图片地址
          insertFn(url);
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    FileUploadVideo (file, insertFn) {
      if (this.imgCount >= this.imgMaxCount) {
        this.$message.warning(`视频最多上传${this.imgMaxCount}个`) // 超过最大上传数量提示
        return
      }
      let urlApi = this.$BASEURL
      let urlImage = this.$SOURCEURL
      var axios = require("axios");
      var FormData = require("form-data");
      var data = new FormData();
      data.append("file", file); // file 即选中的文件
      var config = {
        method: "post",
        url: urlApi + "/file/uploadVideo", //上传图片地址
        headers: {
          "Content-Type": "multipart/form-data",
          Accesstoken: localStorage.getItem("vue-admin-beautiful")
        },
        data: data
      };
      axios(config)
        .then(function (res) {
          let url = urlImage + res.data.url; //拼接成可浏览的图片地址
          insertFn(url); //插入图片
        })
        .catch(function (error) {
          console.log(error);
        });
    }
  },
  mounted () {
    // 模拟 ajax 请求，异步渲染编辑器
    // setTimeout(() => {
    //   this.html = '<p>Ajax 异步设置内容 HTML</p>'
    // }, 1500)
  },
  beforeDestroy () {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁 editor ，重要！！！
  },
}
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
