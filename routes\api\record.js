var express = require("express");
var router = express.Router();
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/RecordService");

router.get('/list', tryAsyncErrors(Service.list))
router.post('/add', tryAsyncErrors(Service.create))
router.post('/edit', tryAsyncErrors(Service.update))
router.post('/statusChange', tryAsyncErrors(Service.statusChange))
router.post('/defaultStatusChange', tryAsyncErrors(Service.defaultStatusChange))
router.delete('/delete/:id', tryAsyncErrors(Service.delete))

module.exports = router;
