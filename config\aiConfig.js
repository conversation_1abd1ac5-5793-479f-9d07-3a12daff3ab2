/**
 * AI服务配置
 * 根据实际使用的AI服务修改配置
 */

module.exports = {
  // OpenAI API配置
  openai: {
    apiKey: process.env.OPENAI_API_KEY || 'sk-HOj6mEQ6mvQhpeLnP1P3uW86LBJOqfaOQoV5YqNDe9gmvx8Q',
    model: 'deepseek-ai/DeepSeek-R1', // 使用的模型
    // 修改为标准OpenAI模型名称
    maxTokens: 500, // 最大回复长度
    temperature: 0.7, // 回复创造性（0-1之间，越高创造性越强）
    baseURL: process.env.OPENAI_API_BASE || 'https://api.hdgsb.com/v1', // 修正API路径，添加/v1
    timeout: 60000, // 请求超时时间（毫秒）
  },
  
  // 其他AI服务配置可以在此添加
  // 例如百度文心、讯飞星火等
  // baidu: {
  //   apiKey: process.env.BAIDU_API_KEY || 'your-baidu-api-key',
  //   secretKey: process.env.BAIDU_SECRET_KEY || 'your-baidu-secret-key',
  //   model: 'ERNIE-Bot-4',
  // },
  
  // 默认使用的AI服务
  defaultProvider: 'openai',
  
  // 每个用户每门课程的最大提问次数
  maxQuestionsPerCourse: 70,
  
  // 是否启用真实AI服务 (true表示使用真实AI服务)
  enableRealAI: true,
}; 