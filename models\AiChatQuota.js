'use strict';
const { Op } = require('sequelize');
const aiService = require('../utils/aiService');

/**
 * AI聊天剩余次数模型
 * 用于存储用户每个课程的AI聊天剩余次数
 */
module.exports = (sequelize, DataTypes) => {
  // 从配置获取默认值
  const defaultQuestionLimit = aiService.getMaxQuestionsPerCourse();
  
  const AiChatQuota = sequelize.define('AiChatQuota', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '配额记录ID'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '用户ID，可以为null表示未登录用户'
    },
    tempUserId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '临时用户ID，用于未登录用户'
    },
    courseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '课程ID'
    },
    remainingChats: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: defaultQuestionLimit,
      comment: '剩余聊天次数，默认从配置获取'
    },
    totalChats: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: defaultQuestionLimit,
      comment: '总聊天次数，默认从配置获取'
    },
    lastResetDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '上次重置日期'
    },
    dailyLimit: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否启用每日重置，默认为false(不重置)'
    },
    createTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updateTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'ai_chat_quota',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'courseId'],
        where: {
          userId: {
            [Op.ne]: null
          }
        },
        name: 'uk_user_course'
      },
      {
        unique: true,
        fields: ['tempUserId', 'courseId'],
        where: {
          tempUserId: {
            [Op.ne]: null
          }
        },
        name: 'uk_temp_user_course'
      }
    ],
    comment: 'AI聊天剩余次数表'
  });

  // 注释掉关联以避免创建外键约束
  /*
  AiChatQuota.associate = function(models) {
    // 与用户表关联
    AiChatQuota.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    
    // 与课程表关联
    AiChatQuota.belongsTo(models.Courseware, {
      foreignKey: 'courseId',
      as: 'course'
    });
  };
  */

  return AiChatQuota;
}; 