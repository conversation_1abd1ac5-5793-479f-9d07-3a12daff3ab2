import { shieldF12 } from "@/config/settings";

if (process.env.NODE_ENV !== "development") {
  if (shieldF12) {
    // 禁用F12键和Ctrl+Shift+I等开发者工具快捷键
    document.onkeydown = function (event) {
      event = event || window.event;
      // 禁用F12
      if (event.keyCode === 123) {
        return false;
      }
      // 禁用Ctrl+Shift+I
      if (event.ctrlKey && event.shiftKey && event.keyCode === 73) {
        return false;
      }
      // 禁用Ctrl+Shift+J
      if (event.ctrlKey && event.shiftKey && event.keyCode === 74) {
        return false;
      }
      // 禁用Ctrl+Shift+C
      if (event.ctrlKey && event.shiftKey && event.keyCode === 67) {
        return false;
      }
      // 禁用Ctrl+S
      if (event.ctrlKey && event.keyCode === 83) {
        return false;
      }
      // 禁用Ctrl+U (查看源代码)
      if (event.ctrlKey && event.keyCode === 85) {
        return false;
      }
    };

    // 禁用右键菜单
    document.oncontextmenu = function (event) {
      event = event || window.event;
      try {
        const element = event.srcElement || event.target;
        // 只允许在输入框和文本域中使用右键
        if (
          !(
            (element.tagName === "INPUT" && element.type.toLowerCase() === "text") ||
            element.tagName === "TEXTAREA"
          )
        ) {
          return false;
        }
      } catch (e) {
        return false;
      }
    };

    // 检测开发者工具打开状态
    function detectDevTools() {
      // 方法1: 检测窗口尺寸变化
      const checkWindowSize = () => {
        const widthThreshold = window.outerWidth - window.innerWidth > 160;
        const heightThreshold = window.outerHeight - window.innerHeight > 160;
        if (widthThreshold || heightThreshold) {
          // 发现开发者工具打开，执行干扰操作
          document.body.innerHTML = "非常抱歉，出于安全考虑，禁止使用开发者工具查看本站点。";
        }
      };

      // 方法2: 使用调试器检测
      function isDebuggerOpened() {
        let startTime = new Date();
        debugger;
        let endTime = new Date();
        return endTime - startTime > 100;
      }

      // 方法3: 使用Console API检测
      let devtoolsDetector = {
        isOpen: false,
        orientation: null
      };

      // 使用console.clear绕过控制台可能的监听
      const originalConsole = { ...console };
      console.clear = function() {};
      console.log = function() {};
      console.info = function() {};
      console.warn = function() {};
      console.error = function() {};
      console.debug = function() {};

      window.addEventListener("resize", checkWindowSize);
      setInterval(checkWindowSize, 1000);

      // 定期检查debugger状态
      setInterval(function() {
        if (isDebuggerOpened()) {
          document.body.innerHTML = "非常抱歉，出于安全考虑，禁止使用开发者工具查看本站点。";
        }
      }, 1000);
    }

    // 在页面加载完成后启动检测
    if (document.readyState === "complete") {
      detectDevTools();
    } else {
      window.addEventListener("load", detectDevTools);
    }
  }
}
