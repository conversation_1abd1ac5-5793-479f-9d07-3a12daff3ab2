.vxe-radio-group {
  .vxe-radio {
    margin-right: 30px;
    &:nth-child(3n) {
      margin-right: 0;
    }
  }
}
.vxe-modal--wrapper .vxe-modal--header {
  background-color: #fff!important;
  font-size: 16px!important;
  font-weight: normal!important;
}

.el-form-item__label {
  font-weight: bold;
}
.vab-main .vxe-table--render-default.border--inner .vxe-table--header-wrapper, .vxe-table--render-default.border--none .vxe-table--header-wrapper {
  background-color: #f8f8f9;
}

.text-center {
  text-align: center
}

.list-group-striped > .list-group-item {
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	padding-left: 0;
	padding-right: 0;
}

.list-group {
	padding-left: 0px;
	list-style: none;
}

.list-group-item {
	border-bottom: 1px solid #e7eaec;
	border-top: 1px solid #e7eaec;
	margin-bottom: -1px;
	padding: 11px 0px;
	font-size: 14px;
  i {
    margin-right: 10px;
  }
}

.pull-right {
	float: right !important;
}

/* image */
.img-circle {
	border-radius: 50%;
}

.img-lg {
	width: 120px;
	height: 120px;
}
//图片上传全局样式
.avatar-upload-preview {
	position: absolute;
	top: 50%;
	transform: translate(50%, -50%);
	width: 200px;
	height: 200px;
	border-radius: 50%;
	box-shadow: 0 0 4px #ccc;
	overflow: hidden;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
//
.el-rate.inlineRate {
  display: inline-block;
}

//2022.1.10 czk添加，解决移动端分页栏错位突出问题
.vxe-grid--pager-wrapper .vxe-pager {
  height: auto;
  min-height: 48px!important;
}
.vxe-table .vxe-header--column {
  font-weight: normal;
}
body .vxe-pager.is--perfect {
  border: none;
  border-bottom: 1px solid #e8eaec;
  background-color: #fff;
}
//2022.1.18 czk 解决ueditor层级问题导致的缩小到小屏时，会显示在最上层的问题
.ueditor .edui-editor {
  line-height: 20px;
}
//2022.1.21 czk 页面搜索的form表单下间距减小
.formSearchBox {
  .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    margin-bottom: 5px;
  }
}
//2022.2.9 czk修改vxe-table的input框禁用后文字颜色以及select框的option宽度问题
.vxe-input--inner[disabled] {
  color:#606266!important;
}
.vxe-select-option {
  max-width: none!important;
}
.vxe-textarea--inner {
  resize: vertical;
}

html body .el-tag + .el-tag {
  margin-left: 4px!important;
}

// .el-tree-node.is-current {
//   background-color: #409EFF;
// }

.el-input.is-disabled .el-input__inner {
  color:#606266!important;
}