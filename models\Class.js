'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Class extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsTo(models.Question, {foreignKey:'questionId',constraints:false })
      this.belongsTo(models.Grade, { foreignKey: 'gradeId', constraints: false })
      this.hasMany(models.Record, { foreignKey: 'classId', constraints: false })
      // this.hasMany(models.User,{foreignKey:'classId',constraints:false})
    }
  }
  Class.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "ID"
      },
      gradeId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: "届/年级id"
      },
      className: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "班级名称"
      },
      createBy: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      modelName: 'Class',
      tableName: 'zk_basic_class',
      timestamps: true,
      comment: '班级表'
    }
  )
  return Class
}
