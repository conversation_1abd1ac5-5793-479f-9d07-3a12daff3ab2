'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class VirtualProcess extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate (models) {
      // define association here
      // this.belongsToMany(models.Role, {as:'roles', through: sequelize.models.UserRole,foreignKey:'roleId',otherKey:'userId' })
      this.hasMany(models.VirtualProcessDevice, { as: 'children', foreignKey: 'vpId', constraints: false })
      this.belongsTo(models.PathPrefix, { foreignKey: 'prefixId', constraints: false })
      this.hasMany(models.Courseware, { as: 'coursewares', foreignKey: 'processTypeId', constraints: false })
      this.hasMany(models.Issue, { as: 'issues', foreignKey: 'processTypeId', constraints: false })
    }
  }
  VirtualProcess.init(
    {
      name: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.vrProName
        },
      },
      info: {
        type: DataTypes.VIRTUAL,
        get () {
          return this.vrProInfo
        },
      },
      level: {
        type: DataTypes.VIRTUAL,
        get () {
          return 1
        },
      },
      parentId: {
        type: DataTypes.VIRTUAL,
        get () {
          return 0
        },
      },
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        comment: "进程ID"
      },
      assetName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "资源名称"
      },
      operName: {
        type: DataTypes.STRING(120),
        allowNull: true,
        comment: "操作名称"
      },
      vrProName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "进程名称"
      },
      vrProInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: "",
        comment: "进程介绍"
      },
      deviceIds: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: '',
        comment: "使用设备ID",
      },
      orderNum: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "显示顺序"
      },
      createBy: {
        type: DataTypes.STRING(64),
        allowNull: true,
        defaultValue: "",
        comment: "创建者"
      },
      remark: {
        type: DataTypes.STRING(500),
        allowNull: true,
        defaultValue: "",
        comment: "备注"
      },
      /* 2022.1.13 czk根据刘工要求，虚拟课程管理的树形结构应该是虚拟流程管理的树形数据，按照刘工提供的3个文件生成。
      原吴兴城又单独新建了一张表，不知道为什么？  
      所以现在添加部分字段，对应需要传递的数据信息，数据图片等
      */
      prefixId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "路径前缀id"
      },
      type: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "类型"
      },
      pic: {
        type: DataTypes.STRING(200),
        allowNull: true,
        defaultValue: "",
        comment: "图片地址"
      },
      videoSrc: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "视频地址"
      },
      linkSrc: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        comment: "外链参数"
      },
      isPlatform:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 1,
        comment: "平台（1.web，2.window）"
      },
      webSrc:{
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "web端访问地址"
      },
      status: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        defaultValue: "0",
        comment: "菜单状态（0正常 1停用）"
      },
      createTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updateTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      },
    },
    {
      sequelize,
      timestamps: true,
      modelName: 'VirtualProcess',
      tableName: 'zk_virtual_process',
      comment:'仿真数据表（改版前）'
    }
  )
  return VirtualProcess
}
