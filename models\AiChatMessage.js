'use strict';

/**
 * AI聊天消息记录模型
 * 用于存储用户与AI助手的聊天历史
 */
module.exports = (sequelize, DataTypes) => {
  const AiChatMessage = sequelize.define('AiChatMessage', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '聊天消息ID'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '用户ID，可以为null表示未登录用户'
    },
    tempUserId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '临时用户ID，用于未登录用户'
    },
    courseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '课程ID'
    },
    courseName: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '课程名称'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '消息内容'
    },
    isUser: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否用户发送的消息，true为用户，false为AI'
    },
    messageTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '消息发送时间'
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否已删除'
    },
    createTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updateTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'ai_chat_message',
    timestamps: false,
    comment: 'AI聊天消息记录表'
  });

  // 注释掉关联，以便先创建表
  /*
  AiChatMessage.associate = function(models) {
    // 与用户表关联
    AiChatMessage.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    
    // 与课程表关联
    AiChatMessage.belongsTo(models.Courseware, {
      foreignKey: 'courseId',
      as: 'course'
    });
  };
  */

  return AiChatMessage;
}; 