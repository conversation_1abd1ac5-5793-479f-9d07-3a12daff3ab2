var express = require("express");
var router = express.Router();
const path = require("path");
const { tryAsyncErrors } = require("../../utils/index")
const Service = require("../../services/TokenService");

router.post('/check',tryAsyncErrors(Service.check))
router.post('/refresh',tryAsyncErrors(Service.refresh))
router.get('/getRefreshToken',tryAsyncErrors(Service.getRefreshToken))

module.exports = router;
