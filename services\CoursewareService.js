const { Courseware, CourseSource, VirtualProcess, VirtualData, $LikeWhere, sequelize: $seq } = require('../models')
const fields = Object.keys(Courseware.tableAttributes)
const fs = require('fs');
const path = require('path');

class CoursewareService {
  static async create (req, res) {
    let list = req.body.courseSources
    await $seq.transaction(async t => {
      let data = await Courseware.create(req.body, { transaction: t })
      if (data.id && list.length > 0) {
        let addList = list.map(item => {
          return { coursewareId: data.id, sourceName: item.response.name, sourceUrl: item.response.url, previewUrl: item.response.previewUrl }
        })
        await CourseSource.bulkCreate(addList, { transaction: t })
      }
      res.sendSuccess({ code: 200, data, msg: '新增成功' })
    });
  }
  static async update (req, res, next, transaction) {
    if (!req.body.id) {
      return res.sendSuccess({ code: -1, msg: '数据id缺失' })
    }
    let list = req.body.courseSources
    let data = await Courseware.update(req.body, {
      where: {
        id: req.body.id
      }
    }, { transaction })
    if (list.length > 0) {
      let addList = []
      list.forEach(item => {
        if (!item.id) {
          addList.push({ coursewareId: req.body.id, sourceName: item.response.name, sourceUrl: item.response.url, previewUrl: item.response.previewUrl })
        }
      })
      if (addList.length > 0) {
        await CourseSource.bulkCreate(addList, { transaction })
      }
    }
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { id } = req.params
    let data = await Courseware.destroy({
      where: {
        id
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async sourceDelete (req, res) {
    let file = req.body
    if (!file.id && file.status != 'success') {
      return res.sendSuccess({ code: -1, msg: '文件参数状态缺失' })
    }
    let sourceUrl = file.url || file.response.url
    let url = path.join(__dirname, '../' + sourceUrl)
    if (url) {
      if (fs.existsSync(url)) {
        fs.unlinkSync(url)
      }
    }
    if (file.id) {
      await CourseSource.destroy({
        where: {
          id: file.id
        },
      })
    }
    res.sendSuccess({ code: 200, msg: '删除成功' })
  }
  static async getOne (req, res) {
    const { id } = req.params
    let data = await Courseware.findByPk(id, {
      include: [
        {
          model: CourseSource,
          as: 'courseSources',
          attributes: {
            include: [['sourceName', 'name'], ['sourceUrl', 'url']]
          }
        },
      ]
    })
    res.sendSuccess({ code: 200, data, msg: '获取成功' })
  }
  static async list (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    if (page && pageSize) {
      let { rows, count } = await Courseware.findAndCountAll({
        attributes: {
          include: [
            [$seq.literal(`(SELECT virtualName FROM zk_virtual_data WHERE zk_virtual_data.code = Courseware.processTypeId)`), 'processTypeName']
          ]
        },
        include: [          
          {
            model: CourseSource,
            as: 'courseSources',
          },
        ],
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order: [['id', 'desc']],
        distinct: true,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }

  static async query (req, res) {
    let { page, pageSize } = req.query
    let where = $LikeWhere(req, res, fields)
    where['status'] = 1
    if (page && pageSize) {
      let { rows, count } = await Courseware.findAndCountAll({
        attributes: {
          include: [
            [$seq.literal(`(SELECT virtualName FROM zk_virtual_data WHERE zk_virtual_data.code = Courseware.processTypeId)`), 'processTypeName']
          ]
        },
        include: [
          // {
          //   model: VirtualData,
          //   required: true,
          //   attributes: []
          // },
          {
            model: CourseSource,
            as: 'courseSources',
          },
        ],
        where,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        order: [['id', 'desc']],
        distinct: true,
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }
}

module.exports = CoursewareService
