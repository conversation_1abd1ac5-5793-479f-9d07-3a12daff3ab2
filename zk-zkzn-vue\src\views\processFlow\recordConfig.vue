<template>
  <div>
    <process-manage ref="processRef">
      <template v-slot="{data}">
        <el-card class="box-card rightBox">
          <el-form :model="data" ref="queryForm">
            <el-form-item label="记录名称" required prop="recordId" class="recordSelect">
              <el-select style="width:calc(100% - 80px)" v-model="data.recordId" @change='recordChange' :disabled="disabled" placeholder="请先选择流程记录" clearable size="small">
                <el-option v-for="dict in recordOptions" :key="dict.id" :label="dict.recordName" :value="dict.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </template>
    </process-manage>
  </div>
</template>

<script>
import processManage from "./components/processManage"
export default {
  name: "RecordConfig",
  components: {
    processManage
  },
  data () {
    return {
      recordOptions: [],
      disabled:false
    };
  },
  created () {
    this.getData();
    if(this.$route.query.recordId) {
      this.disabled = true
      this.$nextTick(() =>{
        this.$refs.processRef.changeRecord(Number(this.$route.query.recordId))
      })
    } else {
      this.disabled = false
    }
  },  
  activated() {
    this.getData();
    if(this.$route.query.recordId) {
      this.disabled = true
      this.$nextTick(() =>{
        this.$refs.processRef.changeRecord(Number(this.$route.query.recordId))
      })
    } else {
      this.disabled = false
    }
  },
  methods: {
    getData () {  
      this.loading = true;
      this.$http.recordList().then(res => {
        this.recordOptions = res.data;
      });
    },
    recordChange(val) {
      this.$nextTick(() =>{
        this.$refs.processRef.changeRecord(Number(val))
      })
    },    
  }
};
</script>

<style lang="scss" scoped>
.recordSelect {
  margin-bottom: 0;
}
</style>
