<template>
  <div class="app-container">
    <el-form class="formSearchBox" :model="formSearch" ref="queryForm" :inline="true">
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="formSearch.dictName" placeholder="请输入字典名称" clearable size="small"
          @keyup.enter.native="getData('formSearch')" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formSearch.status" placeholder="状态" clearable size="small" @change="getData('formSearch')">
          <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getData('formSearch')">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini"
          @click="() => { this.$resetForm('queryForm'); getData() }">重置</el-button>
      </el-form-item>
    </el-form>

    <vxe-grid :height="windowHeight-200" ref="xGrid" row-id="id"
      :toolbarConfig="{ size: 'mini', custom: true, slots: { buttons: 'toolbar_left' } }" v-loading="loading"
      :seq-config="{ startIndex: (formSearch.page - 1) * formSearch.pageSize }" @page-change="handlePageChange"
      :pager-config="Object.assign({ currentPage: formSearch.page, total: tablePage.total }, formSearch)"
      :columns="tableColumn" :data="tableData">
      <template #toolbar_left>
        <el-button type="primary" v-permissions="['dict/add']" plain icon="el-icon-plus" @click="handleAdd" size="mini">新增</el-button>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status == 1 ? 'default' : 'danger'">{{ row.status == 1 ? '正常' : '停用' }}</el-tag>
      </template>
      <template #operate="{ row }">
        <el-button type="text" v-permissions="['dict/edit']" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
        <el-button type="text" v-permissions="['dict/del']" icon="el-icon-delete" @click="handleDel(row)">删除</el-button>
      </template>
    </vxe-grid>

    <vxe-modal ref="xModal" v-model="modalVisible" width="800" height="600" :title="modalTitle"
      @show="$xModalOpen('xModal')" :show-zoom="$store.state.settings.formSpan == 12 ? true : false" mask-closable
      esc-closable show-footer resize transfer>
      <template v-slot>
        <vxe-form class="form-k" ref="formRef" :span="$store.state.settings.formSpan" :data="formData" :items="formItem"
          :rules="$rules.userRules" title-width="100" title-align="right" title-overflow='tooltip'>
        </vxe-form>
      </template>
      <template v-slot:footer>
        <div style="width: 100%; text-align: center">
          <el-button type="primary" @click="save">保存</el-button>
          <el-button @click="close">取消</el-button>
        </div>
      </template>
    </vxe-modal>


  </div>
</template>

<script>
export default {
  name: "Dict",
  data () {
    return {
      tablePage: {
        total: 0,
      },
      windowHeight: window.innerHeight, // 用于存储窗口高度
      tableColumn: [
        { type: 'seq', width: 60, title: '序号' },
        { field: 'dictName', title: '字典名称' },
        {
          field: 'dictCode', title: '字典编码',
          slots: {
            default: ({ row }) => {
              return [
                <el-link type="primary" onClick={() => this.$router.push({ path: '/dictData', query: { dictCode: row.dictCode } })} >{row.dictCode}</el-link>
              ]
            }
          }
        },
        { field: 'status', title: '状态', slots: { default: 'status' } },
        { field: 'sortBy', title: '排序' },
        { field: 'createTime', title: '创建时间' },
        { title: '操作', width: 200, slots: { default: 'operate' } }
      ],
      tableData: [],
      formSearch: {
        dictName: '',
        status: '',
        page: 1,
        pageSize: 10
      },
      statusOptions: [
        { value: 1, label: '正常' },
        { value: 2, label: '停用' },
      ],
      menuOptions: [],
      formData: {},
      formItem: [
        { field: 'dictName', title: '字典名称', itemRender: { name: '$input', props: { placeholder: '请输入字典名称' } } },
        { field: 'dictCode', title: '字典编码', itemRender: { name: '$input', props: { placeholder: '请输入字典编码' } } },
        { field: 'sortBy', title: '排序', itemRender: { name: '$input', props: { type: 'integer', placeholder: '请输入排序号' } } },
        { field: 'status', title: '状态', itemRender: { name: '$radio', options: [{ label: '正常', value: 1 }, { label: '停用', value: 2 }] } },
        { field: 'remark', title: '备注', span: 24, itemRender: { name: '$input', props: { placeholder: '请输入备注' } } },
      ],
      modalTitle: '新增信息',
      modalVisible: false,
    };
  },
  created () {
    this.getData();
  },
  mounted() {
        this.updateWindowHeight();
        window.addEventListener('resize', this.updateWindowHeight);
    },
  beforeDestroy() {
        // 组件销毁前移除事件监听
        window.removeEventListener('resize', this.updateWindowHeight);
    },
  methods: {
    updateWindowHeight() {
            console.log(this.windowHeight);

            this.windowHeight = window.innerHeight; // 获取可视窗口的高度
        },
    getData (params) {
      if (params == 'formSearch') {
        this.formSearch.page = 1
      }
      this.loading = true;
      this.$http.dictList(this.formSearch).then(res => {
        this.tableData = res.data;
        this.loading = false;
      });
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.formSearch.page = currentPage
      this.formSearch.pageSize = pageSize
      this.getData()
    },
    handleAdd () {
      this.modalTitle = '新增信息';
      this.formData = {
        dictName: '',
        dictCode: '',
        sortBy: 1,
        status: 1,
        remark: '',
      }
      this.modalVisible = true;
    },
    handleEdit (row) {
      this.modalTitle = '修改信息';
      this.formData = JSON.parse(JSON.stringify(row))
      this.modalVisible = true;
    },
    // 删除菜单
    handleDel (row) {
      this.$confirm('请确认是否删除此数据及其所有子节点数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.dictDel(row.id).then(() => {
          this.$msg('删除成功', 'success');
          this.getData();
        });
      })
    },
    save () {
      this.$refs.formRef.validate((visible) => {
        if (!visible) {
          let form = this.formData
          if (!this.formData.id) {
            this.$http.dictAdd(form).then(res => {
              this.$msg('新增成功', 'success');
              this.close()
              this.getData();
            });
          } else {
            this.$http.dictEdit(form).then(res => {
              this.$msg('修改成功', 'success');
              this.close()
              this.getData();
            });
          }
        } else {
          return false
        }
      })
    },
    close () {
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }
      this.modalVisible = false
    },
  }
};
</script>

<style scoped></style>
