<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" type="border-card">
      <el-tab-pane label="注册功能" name="first">
        <vxe-form ref="formOtherSettings" :data="formData" :items="formItem" title-width="150" title-align="right" titleColon @submit="submitEvent" @reset="resetEvent">
          <template #submitSlots>
            <vxe-button type="submit" status="primary" content="保存"></vxe-button>
            <!-- <vxe-button type="reset" content="重置"></vxe-button> -->
          </template>
        </vxe-form>
      </el-tab-pane>
      <el-tab-pane label="仿真系统web端上传配置" name="second">
        <el-alert title="1.此项需配合nginx配置指定web端访问目录" type="info" show-icon :closable="false">
        </el-alert>
        <el-alert title="2.不填写此配置项，可能会出现访问web端仿真系统过程中后端接口响应慢，卡顿等情况" type="info" show-icon :closable="false">
        </el-alert>
        <el-alert title="3.window系统请填写绝对路径，如 D://webUpload" type="info" show-icon :closable="false">
        </el-alert>
        <vxe-form ref="formOtherSettings" :data="formData" :items="formItem2" title-width="150" title-align="right" titleColon>
          <template #submitSlots>
            <vxe-button type="submit" v-permissions="['config/edit']" status="primary" content="保存" @click="save"></vxe-button>
            <!-- <vxe-button type="reset" content="重置"></vxe-button> -->
          </template>
        </vxe-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>

export default {
  data () {
    return {
      activeName: "first",
      formSearch: {},
      roleOption: [],
      gradeOption: [],
      formData: {
        id: "",
        isApproval: "",
        isRegister: "",
        registerRole: "",
        webPrefixUrl: "",
        webUploadUrl: "",
      },
      formItem: [],
      formItem2: [
        { field: 'webPrefixUrl', title: 'web端访问地址前缀', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入web端访问地址前缀' } } },
        { field: 'webUploadUrl', title: 'web端上传文件目录', span: 12, itemRender: { name: '$input', props: { placeholder: '请输入web端上传文件目录地址' } } },
        { align: 'center', span: 24, slots: { default: "submitSlots" } }
      ],
    }
  },
  created () {
    this.queryRegistration()
    this.selectOption()
  },
  methods: {
    handleClick (tab, event) {
    },
    queryRegistration () {
      this.$http.configInfo().then((res) => {
        this.formData = res.data
      })
    },
    selectOption () {
      let t = this
      Promise.all([
        this.$http.roleOption(),
        this.$http.gradeOption()
      ]).then(([roleRes, gradeRes]) => {
        t.roleOption = roleRes.data;
        t.gradeOption = gradeRes.data;
        this.formItem = [
          { field: 'registerRole', title: '用户角色关联', span: 12, itemRender: { name: '$select', props: { placeholder: '请选择角色' }, options: this.roleOption } },
          // { field: 'gradeId', title: '届/年级关联', span: 12, itemRender: { name: '$select', props: { placeholder: '请选择角色' }, options: this.gradeOption, optionProps: { value: 'id', label: 'gradeName' }, } },
          { field: 'isRegister', title: '是否开启注册功能', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
          { field: 'isApproval', title: '是否开启注册审批', span: 24, itemRender: { name: '$switch', props: { openLabel: '启用', closeLabel: '禁用', openValue: 1, closeValue: 0 } } },
          { align: 'center', span: 24, slots: { default: "submitSlots" } }
        ]
      }).catch(error => {
        console.error("Error fetching data:", error);
      });
    },
    submitEvent () {
      this.$http.configEdit(this.formData).then((res) => {
        this.$msg(res.msg, "success");
        this.queryRegistration()
      })
      // VXETable.modal.message({ content: '保存成功', status: 'success' })
    },
    save () {
      this.$http.configEdit(this.formData).then((res) => {
        this.$msg(res.msg, "success");
        this.queryRegistration()
      })
    },
    resetEvent () {
      // VXETable.modal.message({ content: '重置事件', status: 'info' })
    },
  }
}
</script>

<style lang="scss" scoped></style>