{"name": "blog-serve", "version": "0.0.0", "private": true, "scripts": {"start": "nodemon ./bin/www", "syncdb": "node migrations/index.js", "test": "node test/index.js", "pro": "pm2 start pm2.conf.json --env pro", "office": "node office/index.js"}, "dependencies": {"adm-zip": "^0.5.16", "async": "^3.2.6", "axios": "^1.4.0", "brace-expansion": "^4.0.0", "busboy": "^0.2.14", "child_process": "^1.0.2", "compression": "^1.7.4", "connect-redis": "^5.2.0", "cookie-parser": "^1.4.5", "crypto": "^1.0.1", "dayjs": "^1.10.4", "debug": "^2.6.9", "express": "^4.16.4", "express-jwt": "^6.1.0", "file-saver": "^2.0.5", "http-errors": "^1.6.3", "http-proxy-middleware": "^2.0.6", "ioredis": "^5.0.5", "ip2region": "^2.3.0", "jade": "^1.11.0", "jsonwebtoken": "^8.5.1", "libreoffice-convert": "^1.3.5", "lodash": "^4.17.21", "mammoth": "^1.4.19", "morgan": "^1.9.1", "mount-routes": "^1.0.8", "multer": "^1.4.3", "mysql": "^2.18.1", "mysql2": "^2.3.0", "nanoid": "^3.1.23", "node-schedule": "^2.0.0", "node-stream-zip": "^1.15.0", "node-xlsx": "^0.16.1", "nodemon": "^3.1.10", "office-to-pdf": "^4.0.0", "redis": "^3.1.2", "request": "^2.88.2", "sequelize": "^6.33.0", "sequelize-cli": "^6.6.1", "svg-captcha": "^1.4.0", "ueditor": "^1.2.3", "uniqid": "^5.4.0", "vue-ueditor-wrap": "^2.5.6", "xml-js": "^1.6.11"}, "devDependencies": {"@types/node": "^16.11.11", "@types/sequelize": "^4.28.10", "cross-env": "^7.0.3", "sequelize-auto": "^0.8.4"}, "volta": {"node": "18.20.8"}}