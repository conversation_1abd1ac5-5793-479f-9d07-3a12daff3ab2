'use strict'
const { Model } = require('sequelize')
module.exports = (sequelize, DataTypes) => {
  class Log extends Model {
    static associate (models) {
      this.hasOne(models.User, { foreignKey: 'id', sourceKey: 'userId', constraints: false })
      this.hasMany(models.UserSerial, { foreignKey: 'userId', sourceKey: 'userId', constraints: false })
    }
  }
  Log.init(
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        comment: "唯一id"
      },
      userId: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: "用户id"
      },
      userName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "用户名称"
      },
      hostIp: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "ip地址"
      },
      location: { // 新增字段归属地
        type: DataTypes.STRING(100), // 假设归属地为字符串，长度为100
        allowNull: true,
        comment: "归属地" // 字段备注
      },
      operType: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "操作类型（新增，修改，删除，导入，导出）"
      },
      reqMethod: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: "请求方式"
      },
      operStatus: {
        type: DataTypes.CHAR(1),
        allowNull: true,
        comment: "操作状态（1.成功、0.失败）",
        // get() {
        //   const rawValue = this.getDataValue('operStatus');
        //   console.log(rawValue,'rawValue');
        //   if(rawValue == 1) return '成功'
        //   return '失败'
        // }
      },
      moduleName: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "模块名称"
      },
      operUrl: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: "请求地址"
      },
      operParams: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "请求参数"
      },
      resParams: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "返回参数"
      },
      operTime: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: sequelize.Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "操作时间"
      }
    },
    {
      sequelize,
      timestamps: false,
      modelName: 'Log',
      tableName: 'sys_log',
      comment: '日志记录表'
    }
  )
  return Log
}
