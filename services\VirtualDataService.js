const { VirtualData, VirtualCate, PathPrefix, PathWeb, OperType, $LikeWhere, sequelize: $seq } = require('../models')
const fields = Object.keys(VirtualData.tableAttributes)
const { handleTree } = require('../utils/index')
const fs = require('fs');
const xlsx = require('node-xlsx');
const { excelHeaderValid } = require('../utils/importValid')
// const { compressImage, compressImagesInDir } = require('../utils/picConvert')
const dayjs = require('dayjs');
const StreamZip = require('node-stream-zip');
const xmlConvert = require('xml-js');
const asyncKu = require('async');
const { customAlphabet } = require('nanoid');
const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 10)

class VirtualDataService {
  static async create (req, res) {
    if (!req.body.virtualName) return res.sendSuccess({ code: -1, msg: '名称不能为空' })
    
    // 生成随机virtualId，确保不重复
    let virtualId;
    let isUnique = false;
    while (!isUnique) {
      virtualId = Math.floor(Math.random() * 1000000) + 100000; // 6位随机数
      const existing = await VirtualData.findOne({ where: { virtualId } });
      if (!existing) {
        isUnique = true;
      }
    }
    
    let obj = {
      ...req.body,
      virtualId,
      virtualParentId: req.body.virtualParentId || 0
    };
    
    // 现有的id生成逻辑...
    let maxId = await VirtualData.max('id', { where: { pId: req.body.pId ? req.body.pId : 0 } })
    if (maxId) {
      obj['id'] = maxId + 1
    } else {
      let str = req.body.pId + ''
      obj['id'] = Number(str + '001')
    }
    
    let data = await VirtualData.create(obj)
    res.sendSuccess({ code: 200, data })
  }

  static async update (req, res, next) {
    if (Array.isArray(req.body)) {
      if (req.body.length <= 0) return res.sendSuccess({ code: -1, msg: '请勿传递空数据' })
      let flag = req.body.some(item => (item.code == undefined || item.code == null || item.code == 0 || item.code == ''))
      if (flag) return res.sendSuccess({ code: -1, msg: '数据中存在code缺失的数据' })
      let idArr = []
      let codeArr = []
      req.body.forEach(item => {
        idArr.push(item.id)
        codeArr.push(item.code)
        // if (!item.sortBy) {
        //   item.sortBy = null
        // }
      })
      //查询是否存在已有的id
      let repetIdArr = await VirtualData.findAll({
        attributes: ['id'],
        where: {
          id: {
            $in: idArr
          },
          code: {
            $notIn: codeArr
          }
        },
        raw: true
      })

      if (repetIdArr && repetIdArr.length > 0) {
        let finalRepetIdArr = repetIdArr.map(t => t.id)
        return res.sendSuccess({ code: -1, msg: `数据中部分id[${finalRepetIdArr.join(',')}]已存在` })
      }

      //查询是否存在修改了id的数据
      let eidtIdArr = await VirtualData.findAll({
        attributes: ['id', 'code'],
        where: {
          code: {
            $in: codeArr
          }
        },
        raw: true
      })

      // let data = await VirtualData.bulkCreate(req.body, { updateOnDuplicate: ['id', 'virtualName', 'operName', 'description', 'operTypeId', 'prefixId', 'sortBy'] })
      const transaction = await $seq.transaction()
      try {
        for (let i = 0; i < req.body.length; i++) {
          let updateObj = {
            id: req.body[i].id,
            virtualName: req.body[i].virtualName,
            assetName: req.body[i].assetName,
            operName: req.body[i].operName,
            description: req.body[i].description,
            operTypeId: req.body[i].operTypeId,
            webSrc: req.body[i].webSrc,
            prefixId: req.body[i].prefixId,
            sortBy: req.body[i].sortBy,
            level: req.body[i].level,
          }

          await VirtualData.update(updateObj, {
            where: {
              code: req.body[i].code
            },
            transaction
          })

          let index = eidtIdArr.findIndex(t => t.code == req.body[i].code && t.id != req.body[i].id)
          if (index != -1) {
            await VirtualDataService.updateIdAndPIdDeepChild(updateObj['id'], eidtIdArr[index].id, updateObj['level'], transaction)
            // await $seq.query(`UPDATE zk_virtual_data SET id = REPLACE(id,${eidtIdArr[index].id},${req.body[i].id}),pId = REPLACE(pId,${eidtIdArr[index].id},${req.body[i].id}) WHERE pId LIKE '%${eidtIdArr[index].id}%'`, { transaction })
          }
        }
        await transaction.commit();
        return res.sendSuccess({ code: 200, data: true })
      } catch (error) {
        await transaction.rollback();
        return next(error)
      }
    }

    if (!req.body.code) return res.sendSuccess({ code: -1, msg: '唯一字段缺失' })
    if (req.body.id) {
      let virtual = await VirtualData.findOne({
        where: {
          id: req.body.id,
          code: {
            $ne: req.body.code
          }
        }
      })
      if (virtual) return res.sendSuccess({ code: -1, msg: '此id已存在' })
    }

    let origin = await VirtualData.findOne({
      where: {
        code: req.body.code
      },
      raw: true
    })

    if (origin.pId != req.body.pId) {
      let maxId = await VirtualData.max('id', { where: { pId: req.body.pId ? req.body.pId : 0 } })
      let obj = req.body
      if (maxId) {
        obj['id'] = maxId + 1
      } else {
        let str = req.body.pId + ''
        obj['id'] = Number(str + '001')
      }

      const transaction = await $seq.transaction()
      try {
        let data = await VirtualData.update(obj, {
          where: {
            code: req.body.code
          },
          transaction
        })
        await VirtualDataService.updateIdAndPIdDeepChild(obj['id'], origin.id, obj['level'], transaction)
        // await $seq.query(`UPDATE zk_virtual_data SET id = REPLACE(id,${origin.id},${req.body.id}),pId = REPLACE(pId,${origin.id},${req.body.id}) WHERE pId LIKE '%${origin.id}%'`, { transaction })
        await transaction.commit();
        res.sendSuccess({ code: 200, data })
      } catch (error) {
        await transaction.rollback();
        next(error)
      }
    } else if (origin.id != req.body.id) {
      //如果原始数据的id变了，那么其子数据都得改
      const transaction = await $seq.transaction()
      try {
        let data = await VirtualData.update(req.body, {
          where: {
            code: req.body.code
          },
          transaction
        })
        await VirtualDataService.updateIdAndPIdDeepChild(req.body.id, origin.id, req.body.level, transaction)
        // await $seq.query(`UPDATE zk_virtual_data SET id = REPLACE(id,${origin.id},${req.body.id}),pId = REPLACE(pId,${origin.id},${req.body.id}) WHERE pId LIKE '%${origin.id}%'`, { transaction })
        await transaction.commit();
        res.sendSuccess({ code: 200, data })
      } catch (error) {
        await transaction.rollback();
        next(error)
      }
    } else {
      let data = await VirtualData.update(req.body, {
        where: {
          code: req.body.code
        }
      })
      res.sendSuccess({ code: 200, data })
    }
  }

  //递归批量更新层级变动导致的id和pId变动
  static async updateIdAndPIdDeepChild (newId, pId, level, t) {
    if (level) {
      level += 1
    } else {
      level = 1
    }
    let childData = await VirtualData.findAll({
      where: {
        pId
      },
      raw: true,
      transaction: t
    })
    if (childData && childData.length > 0) {
      for (let i = 0; i < childData.length; i++) {
        if (childData[i] && childData[i].id != 0) {
          let finalNewId = String(childData[i].id).replace(pId, newId)
          await VirtualData.update({ id: finalNewId, pId: newId, level }, {
            where: {
              code: childData[i].code
            },
            transaction: t
          })
          await VirtualDataService.updateIdAndPIdDeepChild(finalNewId, childData[i].id, level, t)
        }
      }
    }
  }

  static async updateBatch (req, res, next, transaction) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id字段缺失' })
    let editObj = {
      isExecutable: req.body.isExecutable,
      isPlatform: req.body.isPlatform,
      webSrc: req.body.webSrc,
      prefixId: req.body.prefixId,
      linkSrc: req.body.linkSrc,
    }

    if (req.body.batchEditMode == '1') {
      //自身及子数据
      await VirtualData.update(editObj, {
        where: {
          id: req.body.id
        },
        transaction
      })
    }

    if (req.body.isCover == '1') {
      //全覆盖
      await VirtualDataService.updateDeepChild(req.body.id, editObj, transaction)
    } else {
      //数据为空的才修改
      await VirtualDataService.updateDeepChildAndPlatformIsNull(req.body.id, editObj, transaction)
    }
    res.sendSuccess({ code: 200, data: true })
  }

  //递归批量更新
  static async updateDeepChild (pId, editData, t) {
    let childData = await VirtualData.findAll({
      where: {
        pId
      },
      raw: true,
      transaction: t
    })
    if (childData && childData.length > 0) {
      await VirtualData.update(editData, {
        where: {
          pId
        },
        transaction: t
      })
      for (let i = 0; i < childData.length; i++) {
        if (childData[i] && childData[i].id != 0) {
          await VirtualDataService.updateDeepChild(childData[i].id, editData, t)
        }
      }
    }
  }

  //递归批量更新platform为空的数据
  static async updateDeepChildAndPlatformIsNull (pId, editData, t) {
    let childData = await VirtualData.findAll({
      where: {
        pId,
        $or: [
          { isPlatform: null },
          { isPlatform: '' },
        ],
      },
      raw: true,
      transaction: t
    })
    if (childData && childData.length > 0) {
      await VirtualData.update(editData, {
        where: {
          pId,
          $or: [
            { isPlatform: null },
            { isPlatform: '' },
          ],
        },
        transaction: t
      })
      for (let i = 0; i < childData.length; i++) {
        if (childData[i] && childData[i].id != 0) {
          await VirtualDataService.updateDeepChildAndPlatformIsNull(childData[i].id, editData, t)
        }
      }
    }
  }

  static async del (req, res, next, transaction) {
    if (!req.params.id) return res.sendSuccess({ code: -1, msg: '参数缺失' })
    let data = await VirtualData.destroy({
      where: {
        id: req.params.id
      },
      transaction
    })
    let pId = req.params.id
    await VirtualDataService.deepChildDel(pId, transaction)
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }

  static async childDel (req, res, next, transaction) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    await VirtualDataService.deepChildDel(req.body.id, transaction)
    res.sendSuccess({ code: 200, data: true, msg: '删除成功' })
  }

  static async deepChildDel (pId, transaction) {
    let childData = await VirtualData.findAll({
      where: {
        pId
      },
      raw: true,
      transaction
    })
    if (childData && childData.length > 0) {
      await VirtualData.destroy({ where: { pId }, transaction })
      for (let i = 0; i < childData.length; i++) {
        if (childData[i].id) {
          await VirtualDataService.deepChildDel(childData[i].id, transaction)
        }
      }
    }
  }

  static async list (req, res) {
    let where = $LikeWhere(req, res, fields, 'VirtualData')
    let result = await VirtualData.findAll({
      attributes: {
        include: [
          [$seq.col('PathPrefix.prefixName'), 'prefixName'],
          [$seq.col('PathWeb.webName'), 'webName'],
          [$seq.col('PathWeb.webUrl'), 'webUrl'],
          [$seq.col('OperType.operTypeName'), 'operTypeName'],
        ]
      },
      include: [
        {
          model: PathPrefix,
          attributes: []
        },
        {
          model: PathWeb,
          attributes: []
        },
        {
          model: OperType,
          attributes: []
        }
      ],
      where,
      order: [
        // [$seq.literal('FIELD(`VirtualData`.`sortBy`,NULL) ASC')],
        [$seq.literal('ISNULL(`VirtualData`.`sortBy`)')],
        ['sortBy'],
        ['id']
      ],
      raw: true
    })
    // let data = handleTree(result, 'id', 'pId')
    // let data = result.map(item => {
    //   if(item.pId === 0) item.pId = null
    //   return item
    // })
    res.sendSuccess({ code: 200, data: result })
  }

  static async status (req, res, next, transaction) {
    if (!req.body.id || !req.body.pId || !req.body.status) return res.sendSuccess({ code: -1, msg: 'id或pId或status参数缺失' })
    //如果status = 1，表示启用，那么自身及其所有子集都启用
    let data = await VirtualData.update({ status: req.body.status }, {
      where: {
        id: req.body.id
      },
      transaction
    })
    //如果status = 1，表示启用，并递归查询父级状态，并改为启用
    if (req.body.status == 1) {
      let origin = await VirtualData.findOne({
        where: {
          id: req.body.id
        },
        transaction
      })
      if (origin && origin.pId) {
        await VirtualDataService.deepFatherUpdateStatus(origin.pId, transaction)
      }
    }

    await VirtualDataService.deepChildUpdateStatus(req.body.id, req.body.status, transaction)
    res.sendSuccess({ code: 200, data })
  }

  static async deepChildUpdateStatus (pId, status, transaction) {
    let childData = await VirtualData.findAll({
      where: {
        pId
      },
      raw: true,
      transaction
    })
    if (childData && childData.length > 0) {
      await VirtualData.update({ status }, { where: { pId }, transaction })
      for (let i = 0; i < childData.length; i++) {
        if (childData[i].id) {
          await VirtualDataService.deepChildUpdateStatus(childData[i].id, status, transaction)
        }
      }
    }
  }

  static async deepFatherUpdateStatus (pId, transaction) {
    let fatherData = await VirtualData.findOne({
      where: {
        id: pId
      },
      transaction
    })
    if (fatherData && fatherData.status != 1) {
      await VirtualData.update({ status: 1 }, {
        where: {
          id: fatherData.id
        },
        transaction
      })
      if (fatherData.pId) {
        await VirtualDataService.deepFatherUpdateStatus(fatherData.pId, transaction)
      }
    }
  }

  static async updateAllCode (req, res) {
    let list = await VirtualData.findAll({ raw: true });
    list.forEach(async item => {
      await VirtualData.update({ code: 'VR_' + nanoid(10) }, {
        where: {
          id: item.id
        }
      });
    })
    res.sendSuccess({ code: 200, data })
  }

  static async sort (req, res, next) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await VirtualData.findAll({
      where: {
        pId: req.body.id,
        status: 1,
        // sortBy: {
        //   $ne: null
        // }
      },
      order: [
        [$seq.literal('ISNULL(`VirtualData`.`sortBy`)')],
        ['sortBy'],
        ['id']
      ],
      raw: true
    })
    const t = await $seq.transaction()
    try {
      // 然后,我们进行一些调用以将此事务作为参数传递:
      for (let i = 0; i < data.length; i++) {
        await VirtualData.update({ sortBy: i + 1 }, {
          where: {
            id: data[i].id
          },
          transaction: t
        })
      }
      await t.commit();
    } catch (error) {
      // 如果执行到达此行,则抛出错误.
      // 我们回滚事务.
      await t.rollback();
    }
    res.sendSuccess({ code: 200, data })
  }

  static async operList (req, res) {
    if (!req.query.pId) return res.sendSuccess({ code: -1, msg: 'pId参数缺失' })
    let data = await VirtualData.findAll({
      attributes: ['id', 'virtualName', 'assetName', 'description', 'operName', 'sortBy', 'operTypeId', [$seq.col('OperType.operTypeName'), 'operTypeName']],
      include: {
        model: OperType,
        attributes: []
      },
      where: {
        pId: req.query.pId,
        status: '1'
      },
      order: [
        ['sortBy'],
        ['id']
      ],
      raw: true
    })
    res.sendSuccess({ code: 200, data })
  }

  static async sortValid (req, res, next) {
    if (!req.body.id) return res.sendSuccess({ code: -1, msg: 'id参数缺失' })
    let data = await VirtualData.findAll({
      attributes: ['sortBy', [$seq.fn('COUNT', 'sortBy'), 'count']],
      where: {
        pId: req.body.id,
        status: 1,
        sortBy: {
          $ne: null
        }
      },
      group: 'sortBy',
      order: [
        ['sortBy']
      ],
      raw: true
    })
    let arr = []
    for (let i = 0; i < data.length; i++) {
      if (data[i].count > 1) {
        arr.push(data[i])
      }
    }
    res.sendSuccess({ code: 200, data: arr })
  }

  //更新webParams和linkSrc字段参数数据
  static async updateParams (req, res, next, transaction) {
    let data = await $seq.query(`UPDATE zk_virtual_data SET webParams = CONCAT('&asset=',assetName,'&mode=4&process=',id,'&device=',id),linkSrc = CONCAT(id,'|3|1') WHERE pId = 0`, { transaction })
    let child = await $seq.query(`UPDATE zk_virtual_data SET webParams = CONCAT('&asset=',assetName,'&mode=1&process=',pId,'&device=',id),linkSrc = CONCAT(pId,'|1|',id) WHERE pId != 0 AND level = 2`, { transaction })
    res.send({ code: 200, data: true, parentNum: data, childNum: child })
  }

  static async homeList (req, res) {
    let resultCate = await VirtualCate.findAll({
      attributes: {
        include: [
          ['orderNum', 'sortBy']
        ]
      },
      include: [
        {
          required: false,
          // left: true,
          model: VirtualData,
          attributes: {
            include: [
              ['virtualName', 'name']
            ]
          },
          where: {
            "$VirtualData.status$": 1,
          },
          include: [
            {
              model: PathPrefix
            },
            {
              model: PathWeb
            }
          ]
        }
      ],
      where: {
        status: 1,
      },
      order: [
        ['orderNum'],
        ['id'],
        [$seq.col('VirtualData.sortBy')],
        [$seq.col('VirtualData.id')]
      ]
    })
    let result = await VirtualData.findAll({
      attributes: {
        include: [
          ['virtualName', 'name'],
        ]
      },
      include: [
        {
          model: PathPrefix,
        },
        {
          model: PathWeb
        }
      ],
      where: {
        status: '1',
        isExecutable: 1
        // virtualCateId: {
        //   $ne: null
        // }
        // level: {
        //   $lt: 3
        // }
      },
      order: [
        ['sortBy'], ['id']
      ]
    })
    let hostShowData = result.filter(t => t.isHostShow == 1)
    let virtualData = handleTree(result, 'id', 'pId')

    //组装分类与树形仿真数据
    for (let i = 0; i < virtualData.length; i++) {
      if (virtualData[i].virtualCateId) {
        for (let j = 0; j < resultCate.length; j++) {
          if (virtualData[i].virtualCateId == resultCate[j].id && resultCate[j].VirtualData && resultCate[j].VirtualData.length > 0) {
            let index = resultCate[j].VirtualData.findIndex(t => t.code == virtualData[i].code)
            if (index != -1) {
              resultCate[j].VirtualData[index] = virtualData[i]
            }
          }
        }
      }
    }
    let data = handleTree(resultCate, 'id', 'pId')
    data = [...hostShowData, ...data]
    data.sort((a, b) => { return a.dataValues.sortBy - b.dataValues.sortBy })
    res.sendSuccess({ code: 200, data })
  }

  static async tree2List (req, res) {
    let data = await VirtualData.findAll({
      include: [
        {
          model: VirtualData,
          as: 'children',
          // attributes: [],
          include: [{
            model: VirtualData,
            as: 'children',
            // attributes: [],
          }]
        }
      ],
      where: {
        status: '1',
        pId: 0
      },
      order: [
        ['sortBy']
      ]
    })
    // let data = handleTree(result, 'id','pId')
    res.sendSuccess({ code: 200, data })
  }

  static async upload (req, res, next) {
    let file = req.file
    if (!file) return res.sendSuccess({ code: -1, msg: '文件上传异常，请联系管理员' })

    const zip = new StreamZip.async({
      file: file.path,
      storeEntries: true
    });

    let flag = false
    const entries = Object.values(await zip.entries());
    let drawIndex = entries.findIndex(item => item.name === 'xl/drawings/drawing1.xml')
    let cellIndex = entries.findIndex(item => item.name === 'xl/cellimages.xml')
    if (drawIndex != -1 || cellIndex != -1) {
      flag = true
    }

    let convertPicArr = []
    let convertPicCellArr = []
    let errPicArr = []
    let picPrefix = ''
    if (flag) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      // let dir = path.join(__dirname, "../public/uploads/" + year + month + day + "/" + file.filename.split('.')[0]);
      let dir = "./public/uploads/" + year + month + day + "/" + file.filename.split('.')[0];
      picPrefix = '/uploads' + dir.split('/uploads')[1]
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      await zip.extract('xl/media', dir)
      //图片压缩
      // compressImagesInDir(dir, dir)

      if (drawIndex != -1) {
        let stm1 = await zip.entryData('xl/drawings/drawing1.xml')
        let stm2 = await zip.entryData('xl/drawings/_rels/drawing1.xml.rels')
        let xmlData1 = xmlConvert.xml2js(stm1, { compact: true, spaces: 4 })
        let xmlData2 = xmlConvert.xml2js(stm2, { compact: true, spaces: 4 })
        let picArr = xmlData1['xdr:wsDr']['xdr:twoCellAnchor']
        let picImg = xmlData2['Relationships']['Relationship']
        let convertPicObj = {}
        if (picImg && picImg.length > 0) {
          for (let i = 0; i < picImg.length; i++) {
            convertPicObj[picImg[i]['_attributes'].Id] = picImg[i]['_attributes'].Target.split('media')[1]
          }
        }
        if (picArr && picArr.length > 0) {
          for (let i = 0; i < picArr.length; i++) {
            if (picArr[i]["xdr:from"]["xdr:row"]["_text"] == picArr[i]["xdr:to"]["xdr:row"]["_text"]) {
              convertPicArr.push({
                index: Number(picArr[i]["xdr:from"]["xdr:row"]["_text"]) - 1,
                picId: picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
                picName: picArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
                fromRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
                toRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
                picUrl: convertPicObj[picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"]]
              })
            } else {
              errPicArr.push({
                indexFrom: picArr[i]["xdr:from"]["xdr:row"]["_text"],
                indexTo: picArr[i]["xdr:to"]["xdr:row"]["_text"],
                picId: picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
                picName: picArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
                fromRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
                toRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
              })
            }
          }
        }
      }
      if (cellIndex != -1) {
        let stm3 = await zip.entryData('xl/cellimages.xml')
        let stm4 = await zip.entryData('xl/_rels/cellimages.xml.rels')
        let xmlData3 = xmlConvert.xml2js(stm3, { compact: true, spaces: 4 })
        let xmlData4 = xmlConvert.xml2js(stm4, { compact: true, spaces: 4 })
        let picCellArr = xmlData3['etc:cellImages']['etc:cellImage']
        let picCellImg = xmlData4['Relationships']['Relationship']
        let convertPicCellObj = {}
        if (picCellImg && picCellImg.length > 0) {
          for (let i = 0; i < picCellImg.length; i++) {
            convertPicCellObj[picCellImg[i]['_attributes'].Id] = picCellImg[i]['_attributes'].Target.split('media')[1]
          }
        }
        if (picCellArr && picCellArr.length > 0) {
          for (let i = 0; i < picCellArr.length; i++) {
            convertPicCellArr.push({
              picId: picCellArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
              picName: picCellArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["name"],
              picDescr: picCellArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
              picUrl: convertPicCellObj[picCellArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"]]
            })
          }
        }
      }
      await zip.close();
    }

    // let jyName = 'temp_'+file.destination + '\\' + file.filename.split('.')[0]
    // fs.mkdirSync(jyName);

    let obj = xlsx.parse(file.path)
    let xlsxData = obj[0].data//原始表格获取数据
    let header = xlsxData.shift()
    let len = header.length
    let excelIndexObj = excelHeaderValid('virtualData', header, res)

    let insertData = []//处理后的导入数据库数据
    let errData = []//处理excel文件中数据异常的项

    let maxId = await VirtualData.max('id', { where: { pId: req.body.uploadId ? req.body.uploadId : 0 } })
    if (maxId) {
      maxId = maxId + 1
    } else {
      maxId = Number(req.body.uploadId + '001')
    }

    xlsxData.forEach((item, i) => {
      //判断数据是否在convertPicArr中有，有则处理数据
      if (drawIndex != -1 && !item[excelIndexObj['picIndex']] && convertPicArr.length > 0) {
        let picIndex = convertPicArr.findIndex(value => value.index == i)
        if (picIndex != -1) {
          item[excelIndexObj['picIndex']] = picPrefix + convertPicArr[picIndex].picUrl
        }
      }
      if (cellIndex != -1 && item[excelIndexObj['picIndex']] && item[excelIndexObj['picIndex']].indexOf('=DISPIMG(') > -1 && convertPicCellArr.length > 0) {
        let picIndex = convertPicCellArr.findIndex(value => item[excelIndexObj['picIndex']].indexOf(value.picName) > -1)
        if (picIndex != -1) {
          item[excelIndexObj['picIndex']] = picPrefix + convertPicCellArr[picIndex].picUrl
        }
      }

      if (item.length && item.length != 0) {
        //1.补全node-xlsx读取缺失部分信息问题
        if (item.length < len) {
          let num = len - item.length
          for (let j = 0; j < num; j++) {
            item.push('')
          }
        }
        item.push(req.user.userId, (maxId + i), req.body.uploadId, (Number(req.body.uploadLevel) + 1), 1, 1)
        insertData.push(item)
      }
    })

    let sqlStr = excelIndexObj.sqlStr + ',id,pId,level,isPlatform,status'

    if (errData.length > 0) {
      let errStr = '<div style="color:red">'
      errData.forEach((item) => {
        if (item.row) {
          errStr += `<p>EXCEL表格第${item.row}行数据异常,${item.err};</p>\n`
        }
      })
      errStr += '</div>'
      return res.sendSuccess({ code: -1, error: '导入数据有误', msg: errStr })
    }

    try {
      const result = await $seq.transaction(async t => {
        let sTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
        insertData = JSON.parse(JSON.stringify(insertData))
        let data = await $seq.query(`INSERT INTO zk_virtual_data(${sqlStr}) values ?`, { replacements: [insertData], raw: true, transaction: t })
        let eTime = dayjs().add(1, 'second').format('YYYY-MM-DD HH:mm:ss')
        // await $seq.query(`UPDATE zk_workmatecup_typical w JOIN zk_workmatecup u ON w.projectName = u.projectName SET w.pId = u.id,w.enterPriseId = u.enterPriseId WHERE w.pId IS NULL AND w.createTime >= '${sTime}'`, { transaction: t })
        // await $seq.query(`UPDATE zk_workmatecup_typical w JOIN zk_zcspace u ON w.spaceName = u.spaceName SET w.spaceId = u.id WHERE w.spaceId IS NULL AND w.createTime >= '${sTime}'`, { transaction: t })
        return data
      });
      res.sendSuccess({ code: 200, success: result, msg: '导入成功' + insertData.length + '条数据' })
    } catch (error) {
      console.log(error, '\n------------------error-----------------');
      next(error)
    }
  }

  static async export (req, res, next) { }

  static async zipFile (req, res, next) {
    let path = 'E:\\zk-lzxnfz\\uploadImportFile\\20230424\\file_33hq6eka4lgun7yic.xlsx'
    const zip = new StreamZip.async({
      file: path,
      storeEntries: true
    });
    const entries = Object.values(await zip.entries());
    // for (const entry of Object.values(entries)) {
    //   const desc = entry.isDirectory ? 'directory' : `${entry.size} bytes`;
    // }
    let extractPath = 'E:\\zk-lzxnfz\\uploadImportFile\\20230421\\file_33hq6edoklgqbfz8y\\media'

    if (!fs.existsSync(extractPath)) {
      fs.mkdirSync(extractPath);
    }

    const stm1 = await zip.entryData('xl/drawings/drawing1.xml')
    const stm2 = await zip.entryData('xl/drawings/_rels/drawing1.xml.rels')
    const stm3 = await zip.entryData('xl/cellimages.xml')
    const stm4 = await zip.entryData('xl/_rels/cellimages.xml.rels')
    await zip.close();

    // stm.pipe(xmlPath)
    let xmlData1 = xmlConvert.xml2js(stm1, { compact: true, spaces: 4 })
    let xmlData2 = xmlConvert.xml2js(stm2, { compact: true, spaces: 4 })
    let xmlData3 = xmlConvert.xml2js(stm3, { compact: true, spaces: 4 })
    let xmlData4 = xmlConvert.xml2js(stm4, { compact: true, spaces: 4 })

    //解析图片的xml数据，提取图片所处位置
    let picArr = xmlData1['xdr:wsDr']['xdr:twoCellAnchor']
    let picImg = xmlData2['Relationships']['Relationship']
    let picCellArr = xmlData3['etc:cellImages']['etc:cellImage']
    let picCellImg = xmlData4['Relationships']['Relationship']
    let convertPicArr = []
    let convertPicCellArr = []
    let convertPicObj = {}
    let convertPicCellObj = {}
    let errPicArr = []
    if (picImg && picImg.length > 0) {
      for (let i = 0; i < picImg.length; i++) {
        convertPicObj[picImg[i]['_attributes'].Id] = picImg[i]['_attributes'].Target.split('media')[1]
      }
    }
    if (picArr && picArr.length > 0) {
      for (let i = 0; i < picArr.length; i++) {
        if (picArr[i]["xdr:from"]["xdr:row"]["_text"] == picArr[i]["xdr:to"]["xdr:row"]["_text"]) {
          convertPicArr.push({
            index: picArr[i]["xdr:from"]["xdr:row"]["_text"],
            picId: picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
            picName: picArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
            fromRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
            toRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
            picUrl: convertPicObj[picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"]]
          })
        } else {
          errPicArr.push({
            indexFrom: picArr[i]["xdr:from"]["xdr:row"]["_text"],
            indexTo: picArr[i]["xdr:to"]["xdr:row"]["_text"],
            picId: picArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
            picName: picArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["name"],
            picName: picArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
            fromRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
            toRowOff: picArr[i]["xdr:from"]["xdr:rowOff"]["_text"],
          })
        }
      }
    }
    if (picCellImg && picCellImg.length > 0) {
      for (let i = 0; i < picCellImg.length; i++) {
        convertPicCellObj[picCellImg[i]['_attributes'].Id] = picCellImg[i]['_attributes'].Target.split('media')[1]
      }
    }
    if (picCellArr && picCellArr.length > 0) {
      for (let i = 0; i < picCellArr.length; i++) {
        convertPicCellArr.push({
          picId: picCellArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"],
          picName: picCellArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["name"],
          picDescr: picCellArr[i]["xdr:pic"]["xdr:nvPicPr"]["xdr:cNvPr"]["_attributes"]["descr"],
          picUrl: convertPicCellObj[picCellArr[i]["xdr:pic"]["xdr:blipFill"]["a:blip"]["_attributes"]["r:embed"]]
        })
      }
    }
    //存在图片位置异常的
    // if(errPicArr.length > 0) {

    // }

    //先判断media文件夹是否存在图片
    // let result
    // zip.stream('xl/drawings/drawing1.xml', (err, stm) => {
    //   // stm.pipe(process.stdout);
    //   result = JSON.parse(xmlConvert.xml2json(stm, { compact: true, spaces: 4 }))
    //   stm.on('end', () => zip.close());
    // });
    let data = []
    res.sendSuccess({
      code: 200, xmlData3,
      xmlData4, convertPicArr, data, xmlData2
    })

  }

  static async readXml (req, res, next) {
    let path = 'E:\\zk-lzxnfz\\uploadImportFile\\20230421\\file_33hq6ef9wlgq8p4qd\\xl\\drawings\\drawing1.xml'

    let XMLdata = fs.readFileSync(path, 'utf-8')
    let result = JSON.parse(xmlConvert.xml2json(XMLdata, { compact: true, spaces: 4 }))
    res.sendSuccess({ code: 200, data: result, })
  }

  static async sharpFile (req, res, next) {
    //图片压缩
    let dir = "./public/uploads/20230424/test";
    const inputDir = dir; // 输入目录，存放原始图片
    const outputDir = dir; // 输出目录，存放压缩后的图片
    const quality = 100; // 压缩质量

    let data = await compressImagesInDir(inputDir, outputDir)
    // .then((outputPaths) => {
    //   console.log(outputPaths,'outputPaths');
    //   console.log(`压缩完成，共压缩了 ${outputPaths.length} 张图片`);
    // })
    // .catch((err) => {
    //   console.error(err,'err');
    // });
    // console.log(data, 'data');
    res.send({ code: 200, total: data.length, data })
  }

  static async readXlsx (req, res, next) {
    // const ExcelJS = require('exceljs');
    // const imageDataURI = require('image-data-uri');

    // const workbook = new ExcelJS.Workbook();
    // const worksheetName = 'sheet1';

    // workbook.xlsx.readFile('E:\\zk-lzxnfz\\uploadImportFile\\20230421\\file_33hq6ef9wlgq8p4qd.xlsx').then(() => {
    //   const worksheet = workbook.getWorksheet(worksheetName);
    //   worksheet.eachRow((row, rowNumber) => {
    //     // console.log(row,'rw');
    //     row.eachCell((cell, colNumber) => {
    //       if (cell.type === ExcelJS.ValueType.String && cell.value.startsWith('data:image/')) {
    //         const imageData = cell.value.replace(/^data:image\/\w+;base64,/, '');
    //         const imageBuffer = imageDataURI.decode(imageData);
    //         const imageName = `${worksheetName}_${rowNumber}_${colNumber}.jpg`;

    //         // 保存图片
    //         fs.writeFileSync(imageName, imageBuffer.data);
    //       }
    //     });
    //   });
    // });
  }

}

module.exports = VirtualDataService

