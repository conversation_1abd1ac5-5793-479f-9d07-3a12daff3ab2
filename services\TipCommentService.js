const { TipComment, User, sequelize: $seq } = require('../models')
class TipCommentService {
  static async create (req, res) {
    let ip = (req.headers['x-forwarded-for'] || '').split(',').pop().trim() || req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
    req.body.hostIp = ip
    let data = await TipComment.create(req.body)
    res.sendSuccess({ code: 200, data, msg: '新增成功' })
  }
  static async update (req, res) {
    let data = await TipComment.update(req.body, {
      where: {
        commentId: req.body.commentId
      }
    })
    res.sendSuccess({ code: 200, data, msg: '修改成功' })
  }
  static async delete (req, res) {
    const { commentId } = req.params
    let data = await TipComment.destroy({
      where: {
        commentId
      },
    })
    res.sendSuccess({ code: 200, data, msg: '删除成功' })
  }
  static async list (req, res) {
    let { articleId, page, pageSize = 10 } = req.body
    if (page && pageSize) {
      let { rows, count } = await TipComment.findAndCountAll({
        attributes: {
          include: [
            $seq.col('User.userName'),
            $seq.col('User.nickName'),
            $seq.col('User.avatar'),
            $seq.col('User.phone'),
            $seq.col('User.email'),
            $seq.col('User.unit'),
            $seq.col('User.sex'),
          ]
        },
        include: [
          {
            model: User,
            attributes: []
          },
        ],
        where: {
          articleId
        },
        offset: (page - 1) * pageSize,
        limit: pageSize,
        distinct: true,
        raw: true
      })
      res.sendSuccess({ code: 200, data: rows, total: count })
    } else {
      res.sendSuccess({ code: -1, msg: '分页参数缺失' })
    }
  }
}

module.exports = TipCommentService
