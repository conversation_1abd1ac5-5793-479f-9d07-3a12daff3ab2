const { <PERSON>u, User, Role, UserRole, sequelize: $seq, $LikeWhere } = require('../models')
const { handleTree } = require('../utils/index')
const RedisService = require('./RedisService')
const fields = Object.keys(Menu.tableAttributes)

function getLevelCode (str, splitStr) {
  let strOne
  if (!splitStr) {
    strOne = parseInt(str)
  } else {
    strOne = parseInt(str.substr(splitStr.length))
  }
  let num = strOne + 1
  switch ((num + '').length) {
    case 1:
      return splitStr + '000' + num
    case 2:
      return splitStr + '00' + num
    case 3:
      return splitStr + '0' + num
    case 4:
      return splitStr + num
  }
}

class MenuService {
  static async create (req, res) {
    let { menuName, levelCode, parentId } = req.body
    // let checkDate = await Menu.findOne({ where: { menuName } })
    // if (checkDate) return res.sendSuccess({ code: -1, msg: '此菜单名称已存在' })
    let maxLevelCode
    if (!levelCode && !parentId) {
      maxLevelCode = await Menu.max('levelCode', { where: { parentId: 0 } })
    } else {
      maxLevelCode = await Menu.max('levelCode', { where: { parentId } })
      levelCode = await Menu.findOne({ where: { menuId: parentId } }).then(data => { return data.levelCode })
    }

    let code = ''
    if (!parentId && !maxLevelCode) {
      code = '0001'
    } else if (!parentId && maxLevelCode) {
      code = getLevelCode(maxLevelCode, '')
    } else if (parentId && maxLevelCode) {
      code = getLevelCode(maxLevelCode, levelCode ? levelCode : '')
    } else if (parentId && !maxLevelCode) {
      code = (levelCode ? levelCode : '') + '0001'
    } else {
      code = (levelCode ? levelCode : '') + '0001'
    }
    if (!code || (code && code.indexOf('NaN') > -1))
      return res.sendSuccess({ code: -1, msg: 'levelCode存在异常:' + code })
    req.body['levelCode'] = code
    req.body['parentId'] = req.body.parentId || 0
    let data = await Menu.create(req.body)
    RedisService.setRoleMenuOption()
    RedisService.setSysPermission()
    res.sendSuccess({ code: 200, data })
  }

  static async update (req, res) {
    const { menuId, menuName } = req.body
    // let checkDate = await Menu.findOne({
    //   where: {
    //     menuName,
    //     menuId: {
    //       $ne: menuId,
    //     },
    //   },
    // })
    // if (checkDate) return res.sendSuccess({ code: -1, msg: '此菜单名称已存在' })
    let data = await Menu.update(req.body, {
      where: { menuId }
    })
    RedisService.setRoleMenuOption()
    RedisService.setSysPermission()
    res.sendSuccess({ code: 200, data })
  }

  static async del (req, res) {
    const { menuId } = req.params
    let levelCode = await Menu.findOne({ where: { menuId } }).then(data => { return data.levelCode })
    let data = await Menu.destroy({
      where: {
        levelCode: {
          $like: `${levelCode}%`
        }
      },
    })
    RedisService.setRoleMenuOption()
    RedisService.setSysPermission()
    res.sendSuccess({ code: 200, data })
  }

  //递归子查询函数
  static async getChildNeeds (rootNeeds, ids) {
    let expendPromise = [];
    rootNeeds.forEach(item => {
      expendPromise.push(Menu.findAll({
        where: {
          parentId: item.menuId
        }
      }))
    })
    let child = await Promise.all(expendPromise);
    for (let [idx, item] of child.entries()) {
      if (item.length > 0) {
        item = await this.getChildNeeds(item);
      }
      rootNeeds[idx].dataValues.children = item;
    }
    return rootNeeds;
  }

  static async menuList (req, res) {
    let where = $LikeWhere(req, res, fields)
    let result = await Menu.findAll({
      where,
      order: ['sortBy']
    })
    let data = handleTree(result, 'menuId')
    res.sendSuccess({ code: 200, data })
  }

  static async menuBeauList (req, res) {
    let result = await Menu.findAll({
      order: ['sortBy']
    })
    let data = handleTree(result, 'menuId')
    res.sendSuccess({ code: 200, data })
  }

  static async getRoutes (req, res) {
    let { userId, userName } = req.user
    let roleMenu
    if (userName == 'admin') {
      roleMenu = ''
    } else {
      roleMenu = await Role.findAll({
        attributes: ['roles.userId', [$seq.fn('group_concat', $seq.fn('concat_ws', ',', $seq.col('menuIds'), $seq.col('halfIds'))), 'menuIds']],
        include: [
          {
            model: UserRole,
            as: 'roles',
            attributes: [],
            where: {
              userId
            },
          }
        ],
        group: 'roles.userId',
        raw: true
      })
        .then(data => {
          if(data && data.length > 0) {
            return [...new Set(data[0].menuIds.split(','))]
          } else {
            return []
          }
        })
    }
    let result = await Menu.findAll({
      attributes: {
        // include: [
        //   // [$seq.literal(`CASE WHEN path !='' THEN CONCAT('/',path) ELSE path END`), 'path'],
        //   // [$seq.literal(`CASE WHEN visible = 0 THEN ${true} ELSE ${false} END`), 'hidden'],
        // ],
        exclude: ['remark', 'createTime', 'updateTime', 'createBy', 'status', 'menuTypeName']
      },
      where: {
        status: {
          $ne: 1
        },
        menuType: {
          $ne: 'F'
        },
        menuId: roleMenu ? {
          $in: roleMenu
        } : { $ne: null }
      },
      order: [
        'sortBy'
      ]
    })
    let resultArr = []
    result.forEach((value, index) => {
      let item = value.dataValues
      if (item.menuType == 'M' && item.parentId == 0) {
        item.component = 'Layout'
      } else if (item.menuType == 'M' && item.parentId != 0) {
        item.component = 'ParentView'
      }
      resultArr.push({
        name: item.path.charAt(0).toUpperCase() + item.path.slice(1),
        component: item.component,
        path: (item.isFrame != '1' && item.menuType == 'M') ? '/' + item.path : item.path,
        parentId: item.parentId,
        menuId: item.menuId,
        hidden: item.visible == '0' ? false : true,
        redirect: item.menuType == 'M' ? 'noRedirect' : undefined,
        meta: {
          icon: item.icon,
          title: item.menuName,
          // affix: true,
          noKeepAlive: item.isCache == '0' ? false : true,
          noCache: item.isCache ? false : true,
          path: item.isFrame == '1' ? '_blank' : null
        },
      })
    })
    let data = handleTree(resultArr, 'menuId')
    res.sendSuccess({ code: 200, data })
  }

}

module.exports = MenuService
